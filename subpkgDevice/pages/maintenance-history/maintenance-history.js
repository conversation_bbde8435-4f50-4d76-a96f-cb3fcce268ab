import profitBillAllPopup from '@/subpkg/components/profit-bill-all-popup/profit-bill-all-popup.vue';
import {isManager, manageId, orgId} from "@/utils/role";
import MaintenanceCharts from "@/subpkgDevice/components/maintenance/MaintenanceCharts.vue";
import maintenanceMixin from "@/subpkgDevice/components/maintenance/maintenanceMixin";
import {getTimeUntilDeadline} from "@/utils/function";
import TaskCount from "@/subpkgDevice/components/maintenance/TaskCount.vue";
import TaskHistory from "@/subpkgDevice/components/maintenance/TaskHistory.vue";
import TaskStatHistory from "@/subpkgDevice/components/maintenance/TaskStatHistory.vue";

export default {
  mixins: [maintenanceMixin],
  data() {
    return {
      tab: 1,
      staffId: '',
      showStat: true,
      showTask: true,
      startX: 0,
      isShowPopup:false
    };
  },
  components: {
    TaskStatHistory,
    TaskHistory,
    TaskCount,
    MaintenanceCharts,
    profitBillAllPopup,
  },
  methods: {
    touchstartHandle(e) {
      this.startX = e.touches[0].pageX
    },
    touchendHandle(e) {
      if(this.showTask && this.showStat){
        const endX = e.changedTouches[0].pageX
        const distance = endX - this.startX
        const threshold = 120
        console.log(distance)
        // 向左=负数 向右=正数
        // if (distance > threshold) {
        //   this.tab = 1
        // } else if (distance < -threshold) {
        //   this.tab = 2
        // }
      }

    },
    getTimeUntilDeadline,
    async changeTab(tab) {
      this.tab = tab;
    },
    checkShowTask() {
      this.showTask = (+this.staffId === +this.manageId || this.isManager)
      if (this.showTask && !this.showStat) {
        this.tab = 2
      }
    }
  },
  computed: {
    isManager,
    manageId,
    orgId,
  },
  watch: {
    staffId() {
      console.log('watch-staffId', this.staffId)
      this.checkShowTask()
    }
  },
  async onLoad(options) {
    const {id, showStat, showTask} = options
    this.staffId = id
    console.log('this.staffId', this.staffId)
    this.showStat = showStat === undefined ? true : +showStat
    this.showTask = showTask === undefined ? true : +showTask
    this.checkShowTask()

  },
  onReachBottom() {
    uni.$emit('onReachBottom'+this.tab)
  },
}