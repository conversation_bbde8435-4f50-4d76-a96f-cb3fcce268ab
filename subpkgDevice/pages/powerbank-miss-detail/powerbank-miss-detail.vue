<template>
  <view class="powerbank-miss-detail">
    <uv-sticky>
      <view class="tab-box">
        <view class="tab-item" :class="{'tab-item-active': tab === 1}" @click="changeTab(1)">库存失踪</view>
        <view class="tab-item" :class="{'tab-item-active': tab === 2}" @click="changeTab(2)">运行失踪</view>
        <view class="tab-item" :class="{'tab-item-active': tab === 3}" @click="changeTab(3)">人工失踪</view>
      </view>
      <view class="search-box flex-row">
        <view class="search-content flex-row">
          <view class="search-icon" @click="search"></view>
          <input class="search-input" placeholder="请输入搜索内容" type="text" v-model="searchText"/>
        </view>
        <view class="type-box flex-row" v-if="tab == 2">
          <view class="type-item" :class="{'type-item-active': type == 1}" @click="changeType(1)">按设备</view>
          <view class="type-item" :class="{'type-item-active': type == 2}" @click="changeType(2)">按时间</view>
        </view>
      </view>
    </uv-sticky>
    <view class="table-box">
      <view class="">
        <view class="table-header table-tr flex-row-between">
          <view class="table-td1 table-td" v-if="tab === 1 || (tab === 2 && type === 1) || tab === 3">设备SN</view>
          <view class="table-td1 table-td" v-if="tab === 2 && type === 2">月份</view>
          <view class="table-td2 table-td" v-if="tab === 1">BD</view>
          <view class="table-td2 table-td" v-if="tab === 3">操作人</view>
          <view class="table-td2 table-td" v-else-if="tab === 2 && type == 1">门店</view>
          <view class="table-td3 table-td" @click="changeDir" v-if="type == 1 && tab !== 3">
            <view class="flex-row" style="justify-content: flex-end;">
              <view>失踪日期</view>
              <view class="sort-icon" :class="{'sort-icon-rotate': dir != 'DESC'}"></view>
            </view>
            <view class="">天数</view>
          </view>
          <view class="table-td3 table-td" v-else-if="tab === 3">
            <view class="flex-row" style="justify-content: flex-end;" @click="changeDir">
              <view>失踪时间</view>
            </view>
          </view>
          <view class="table-td3 table-td" v-else>
            <view class="flex-row" style="justify-content: flex-end;" @click="changeDir">
              <view>失踪宝数</view>
              <view class="sort-icon" v-if="type == 2" :class="{'sort-icon-rotate': dir != 'DESC'}"></view>
            </view>
          </view>
        </view>
        <view class="table-content" v-if="tableLength">
          <view class="table-th table-row flex-row-between"
                :class="{'table-tr-odd': i % 2}"
                v-for="(row,i) in missAtPersonalHubDetailList"
                :key="i"
                v-if="tab === 1"
                @click="toPowerbankDetail(row.sn)"
          >
            <view class="table-td1 table-td">{{ row.sn }}</view>
            <view class="table-td2 table-td">{{ row.keeper.name }}</view>
            <view class="table-td3 table-td">
              <view class="">{{ row.formatDateTime }}</view>
              <view class="special-text">{{ row.overdueDays }}</view>
            </view>
          </view>
          <view class="table-th table-row flex-row-between"
                :class="{'table-tr-odd': i % 2}"
                v-for="(row,i) in missAtStoreDetailList"
                :key="row.sn"
                v-if="tab === 2 && type === 1" @click="toPowerbankDetail(row.sn)">
            <view class="table-td1 table-td">{{ row.sn }}</view>
            <view class="table-td2 table-td">{{ row.keeper.name }}</view>
            <view class="table-td3 table-td">
              <view class="">{{ row.formatDateTime }}</view>
              <view class="special-text">{{ row.overdueDays }}</view>
            </view>
          </view>

          <view class="table-th table-row flex-row-between"
                :class="{'table-tr-odd': i % 2}"
                v-for="(row,i) in assetMonthlyMissAtStoreList"
                :key="row.date"
                v-if="tab === 2 && type === 2">
            <view class="table-td1 table-td">{{ row.date }}</view>
            <view class="table-td2 table-td special-text" style="text-align: right;">{{ row.count }}</view>
          </view>

          <view class="table-th table-row flex-row-between"
                :class="{'table-tr-odd': i % 2}"
                v-for="(row,i) in manualMissDetailList"
                :key="row.sn"
                v-if="tab === 3" @click="toPowerbankDetail(row.sn)">
            <view class="table-td1 table-td">{{ row.sn }}</view>
            <view class="table-td2 table-td">{{ '未知' }}</view>
            <view class="table-td3 table-td">
              <view class="">{{ row.formatDateTime }}</view>
              <view class="special-text">{{ row.overdueDays }}</view>
            </view>
          </view>
        </view>
        <view class="table-empty" v-else>暂无数据哦~</view>
      </view>
    </view>
  </view>
</template>

<script src="./powerbank-miss-detail.js"></script>

<style lang="scss" scoped>
@import './powerbank-miss-detail.scss';
</style>
