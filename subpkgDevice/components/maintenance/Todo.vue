<script>
import {TaskFilterResultEnum, TaskStatKeyEnum} from "@/subpkgDevice/utils/maintenanceUtil";
import {isManager, manageId, orgId} from "@/utils/role";
import maintenanceMixin from "@/subpkgDevice/components/maintenance/maintenanceMixin";

export default {
  name: "Todo",
  mixins: [maintenanceMixin],
  data() {
    return {
      todoStat: [],
      currentPoints: 0,
    }
  },
  methods: {
    async getTodoStat() {
      const filter = {
        results: [TaskFilterResultEnum.DOING],
        assignees: this.isManager ? [] : [+this.manageId]
      }
      this.todoStat = await this.getStat(filter, Object.keys(TaskStatKeyEnum))
    },
    async getCurrentPoints() {
      this.currentPoints = await this.getStaffPoints(this.manageId)
      console.log(this.currentPoints)
    },
    getUserInitPoints(userId) {
      return this.setting.userPointsMap[+userId] || this.setting.defaultPoints
    },
    getTodoCount(key) {
      const todoItem = this.todoStat.find(item => item.name === key) || {}
      return todoItem.count
    },
    toHistory() {
      uni.navigateTo({
        url: `/subpkgDevice/pages/maintenance-history/maintenance-history?id=${this.manageId}`
      })
    }
  },
  computed: {
    TaskStatKeyEnum() {
      return TaskStatKeyEnum
    },
    isManager,
    orgId,
    manageId
  },
  async created() {
    await this.getTodoStat()
    if (!this.isManager) {
      await this.getCurrentPoints()
    }
  },
}
</script>

<template>
  <view class="pannel" :class="{'pannel-manager': isManager}">
    <view v-if="!isManager" class="score-box flex-row-between">
      <view class="flex-row">
        <view class="score-icon"></view>
        <view class="score-text">{{ (currentPoints / 100).toFixed(1) }}</view>
      </view>
      <view class="history-btn" @click="toHistory">罚分记录</view>
    </view>
    <view class="pannel-header flex-row-between">
      <view class="pannel-title">今日待办</view>
      <!--      <view class="flex-row">-->
      <!--        <view class="">本月罚分：</view>-->
      <!--        <view class="">49.5</view>-->
      <!--      </view>-->
    </view>
    <view class="flex-row-between">
      <view class="pannel-item" v-for="(value,key) in TaskStatKeyEnum" :key="key">
        <view class="pannel-item-header flex-row">
          <view :class="['pannel-item-icon',value.clazz]"></view>
          <view class="pannel-item-text">{{ value.text }}</view>
        </view>
        <view class="pannel-item-count">{{ getTodoCount(key) }}</view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">

.pannel {
  //margin: -76px 24rpx 0;
  margin: 20rpx 24rpx 0;
  padding: 32rpx;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px 8px 8px 8px;

  .score-box {
    margin-bottom: 26rpx;
    .score-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 8rpx;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/maintenance-history-icon2.png');
      background-size: 48rpx 48rpx;
    }

    .score-text {
      font-size: 40rpx;
      font-weight: bold;
    }

    .history-btn {
      color: #7F8998;
      font-size: 28rpx;
    }
  }

  .pannel-header {
    margin-bottom: 24rpx;
    font-size: 28rpx;
    line-height: 40rpx;
  }

  .pannel-title {
    font-weight: bold;
    color: #26282B;
  }

  .pannel-content-left {
    width: 328rpx;
  }

  .pannel-content-right {
    flex: 1;
  }

  .pannel-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .pannel-item-header {
      margin-bottom: 10rpx;
    }

    .pannel-item-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
      background-size: 32rpx 32rpx;
    }

    .pannel-item-icon1 {
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/maintenance-service-icon9.png');
    }

    .pannel-item-icon2 {
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/maintenance-service-icon10.png');
    }

    .pannel-item-icon3 {
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/maintenance-service-icon11.png');
    }

    .pannel-item-icon4 {
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/maintenance-service-icon12.png');
    }

    .pannel-item-icon5 {
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/maintenance-service-icon13.png');
    }

    .pannel-item-text {
      font-size: 24rpx;
      line-height: 34rpx;
      font-weight: bold;
    }

    .pannel-item-count {
      font-size: 28rpx;
      line-height: 40rpx;
      color: #26282B;
    }
  }
}

.pannel-manager {
  margin-top: -76px;
}
</style>