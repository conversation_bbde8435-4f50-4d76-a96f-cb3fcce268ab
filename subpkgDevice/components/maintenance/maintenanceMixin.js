import {
  DateFilterOptions,
  dateFormat,
  getDateMonthFirstDay,
  getDateMonthLastDay,
  getTaskTypeCount,
  TaskFilterResultEnum,
  TaskGroupByEum,
  TaskImportanceEnum,
  TaskStatKeyEnum,
  TaskTransferTypeEum,
  TaskTypeEnum
} from '@/subpkgDevice/utils/maintenanceUtil'
import { findMaintenanceTaskStatGroupReq, findMaintenanceTaskStatReq } from '@/subpkgDevice/api/maintenance'
import { isManager, manageId, orgId } from '@/utils/role'

export default {
  data() {
    return {
      dateFilterOption: DateFilterOptions(),
      staffList: []
    }
  },
  methods: {
    getStat(filter, keys = Object.keys(TaskStatKeyEnum)) {
      const variables = {
        orgId: this.orgId,
        keys,
        filter
      }
      return findMaintenanceTaskStatReq(variables).then(res => res.data.taskStat)
    },
    getStatGroup(filter, groupBy) {
      return findMaintenanceTaskStatGroupReq({
        orgId: this.orgId,
        groupBy,
        filter
      }).then(res => res.data.taskStatByGroup)
    },
    getUserInitPoints(userId) {
      return this.setting && (this.setting.userPointsMap[+userId] || this.setting.defaultPoints)
    },
    async getStaffPoints(staffId, begin, end) {
      if (!begin) {
        begin = dateFormat(getDateMonthFirstDay())
      }
      if (!end) {
        end = dateFormat(getDateMonthLastDay())
      }
      const filter = {
        assignees: [+staffId],
        begin,
        end
      }
      const overdueStat = await this.getStat(filter, [TaskStatKeyEnum.OVERDUE.code])
      const totalPenalties = overdueStat.reduce((total, item) => total + item.penalties, 0)
      return this.getUserInitPoints(staffId) - totalPenalties
    },
    checkTaskExpire(task) {
      return +new Date(task.endTime) > +new Date(task.deadline)
    },
    getTaskTypeCount,
    getStaffList(showAll = true) {
      this.staffList = [...this.$store.getters['maintenance/staffList']]
      console.log('this.staffList', this.staffList)
      if (showAll && this.staffList && this.staffList.length && this.staffList[0].text !== '全部') {
        this.staffList.unshift({ text: '全部', value: null })
      }
    }
  },
  computed: {
    manageId,
    isManager,
    orgId,
    setting() {
      return this.$store.getters['maintenance/setting']
    },
    TaskTransferTypeEum() {
      return TaskTransferTypeEum
    },
    currentDateFilter() {
      return this.dateFilterOption.find(item => item.value === this.dateFilterCode)
    },
    TaskImportanceEnum() {
      return TaskImportanceEnum
    },
    TaskTypeEnum() {
      return TaskTypeEnum
    },
    TaskStatKeyEnum() {
      return TaskStatKeyEnum
    },
    TaskGroupByEum() {
      return TaskGroupByEum
    },
    TaskFilterResultEnum() {
      return TaskFilterResultEnum
    }
  },
  filters: {
    formatDateTimeFilter(dateString) {
      const date = new Date(dateString)
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
    },
    formatDistanceFilter(distance) {
      const IntVal = +distance
      return IntVal > 1000 ? `${(IntVal / 1000).toFixed(2)}km` : `${IntVal.toFixed(0)}m`
    }
  }
}
