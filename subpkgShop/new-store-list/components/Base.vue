<script>
import Chart from '@/subpkgShop/new-store-list/components/Chart.vue'
import MoreFilter from '@/subpkgShop/new-store-list/components/MoreFilter.vue'
import { getOrgRouteFilter, manageId } from '@/utils/role'
import { findNewStoreReq, newStoreListMapper } from '@/subpkgShop/new-store-list/api/newStore'
import { getGraphPageNodes } from '@/utils/requestUtil'

export default {
  name: 'Base',
  components: { MoreFilter, Chart },
  data(){
    return {
      loading:false,
      searchText: '',
      constants: {},
      dataList: [],
      after: '',
      hasNext: '',
      levelIn: [],
      moreFilterShow: false,
      isFilterVisible: false,
      tagIds: null,
      // 等级
      levelList: [
        {text: '全部', value: ''},
        {text: '低动销(LV.1-LV.2)', value: 'level_2_below'},
        {text: '中动销(LV.3-LV.5)', value: 'level_3_above_5_below'},
        {text: '高动销(LV.6以上)', value: 'level_6_above'}
      ],
      currentSortValue: 'assetAccountDeposit_DESC',
      currentLevelValue: '',
      levelValue: '',
      tabList: [
        {text: '收益', value: 'profit', disabled: false},
        {text: '私域', value: 'private', disabled: false},
        {text: '资产', value: 'asset', disabled: true},
        {text: '门店竞争', value: 'competition', disabled: true},
      ],
      activeTabValue: 'profit',
      activeTabTagValue: 'efficiency',
    }
  },
  computed: {
    manageId,
    isTotal(){
      return this.activeTabValue === 'profit' && this.activeTabTagValue === 'total'
    },
    isEfficiency(){
      return this.activeTabValue === 'profit' && this.activeTabTagValue === 'efficiency'
    },
    isRetention(){
      return this.activeTabValue === 'profit' && this.activeTabTagValue === 'retention'
    },
    sortList () {
      if (this.activeTabValue === 'profit' && this.activeTabTagValue === 'retention') {
        return [
          { text: '余额高到低', value: 'assetReceivableStoreKeeper_ASC' },
          { text: '余额低到高', value: 'assetReceivableStoreKeeper_DESC' },
          { text: '收益高到低', value: 'assetAccountDeposit_DESC' },
          { text: '收益低到高', value: 'assetAccountDeposit_ASC' },
          { text: '利润高到低', value: 'netRevenue_ASC' },
          { text: '利润低到高', value: 'netRevenue_DESC' }
        ]
      }
      return [
        { text: '收益高到低', value: 'assetAccountDeposit_DESC' },
        { text: '收益低到高', value: 'assetAccountDeposit_ASC' },
        { text: '利润高到低', value: 'netRevenue_DESC' },
        { text: '利润低到高', value: 'netRevenue_ASC' }
      ]
    },
    activeTabIndex() {
      return this.tabList.findIndex(tab => tab.value === this.activeTabValue)
    },
    profitTabTagList() {
      return [
        { text: '总收益', value: 'total', disabled: false },
        { text: '资金效率', value: 'efficiency', disabled: false },
        { text: '利润留存', value: 'retention', disabled: false },
        { text: '利润率', value: 'profitRate', disabled: true },
      ]
    },
    privateTabTagList() {
      return []
    },
    currentTabTagList() {
      if (this.activeTabValue === 'profit') return this.profitTabTagList
      if (this.activeTabValue === 'private') return this.privateTabTagList
      return []
    },
    queryMode() {
      const result = {
        queryPrivateDomain: false,
        queryProfit_total: false,
        queryProfit_efficiency: false,
        queryProfit_retention: false
      }
      if (this.activeTabValue === 'private') {
        result.queryPrivateDomain = true
      } else {
        result[`queryProfit_${this.activeTabTagValue}`] = true
      }
      return result
    }
  },
  methods: {
    goDetail(row){
      return this.$tab.navigateTo(`/subpkg/pages/storekeeper-info/storekeeper-info?id=${row.id}`)
    },
    showSharingInfo(nominalProfitSharing,actualProfitSharing){
      uni.showModal({
        title: `${nominalProfitSharing}% → ${actualProfitSharing}%`,
        content: '约定分成 → 实际分成',
        showCancel: false,
        confirmText: '我知道了'
      })
    },
    showMonthProfitInfo() {
      uni.showModal({
        title: '月收益说明',
        content: '月收益指当前30天内收益',
        showCancel: false,
        confirmText: '我知道了'
      })
    },
    showMonthInvestorInfo(){
      uni.showModal({
        title: '投资月收益率',
        content: '投资月收益率指当前30天内总投资收益率',
        showCancel: false,
        confirmText: '我知道了'
      })
    },
    onSearchInput() {
      this.getDataList()
    },
    changeTab(value) {
      if (this.tabList.find(tab => tab.value === value)?.disabled) return
      this.activeTabValue = value
      if (value === 'private') {
        this.tagIds = [12]
      } else {
        this.tagIds = null
      }
      this.getDataList()
    },
    changeTabTag(value,disabled) {
      if (disabled) return
      this.activeTabTagValue = value
      if(this.isRetention){
        this.currentSortValue = 'assetReceivableStoreKeeper_ASC'
      }else if(this.currentSortValue?.includes('assetReceivableStoreKeeper')){
        this.currentSortValue = 'assetAccountDeposit_DESC'
      }
      this.getDataList()
    },
    changeSort(value) {
      this.currentSortValue = value
      this.getDataList()
    },
    changeLevel(value) {
      console.log(value)
      console.log(this.currentLevelValue)
      if (this.currentLevelValue === value) {
        // 如果当前选择的值与之前的选择相同，则清空 levelIn
        this.levelIn = null;
        this.levelValue = '';
      } else {
        if (value === 'level_2_below') {
          this.levelIn = [0, 1, 2];
        } else if (value === 'level_3_above_5_below') {
          this.levelIn = [3, 4, 5];
        } else if (value === 'level_6_above') {
          this.levelIn = [6, 7, 8];
        } else {
          this.levelIn = null;
        }
      }
      this.currentLevelValue = this.levelValue;
      console.log(this.levelIn)
      this.getDataList();
    },
    handleFilterChange(newFilterState) {
      let newTagIds = null;
      switch (newFilterState.cooperativeModel.selected) {
        case 'PROFIT_SHARING':
          newTagIds = 7;
          break;
        case 'PREPAY':
          newTagIds = 8;
          break;
        case 'RENT':
          newTagIds = 9;
          break;
        case 'ENTRY_FEE':
          newTagIds = 10;
          break;
        default:
          newTagIds = null;
          break;
      }

      if (this.activeTabValue === 'private') {
        // 确保 12 始终在 tagIds 中
        if (newTagIds !== null) {
          this.tagIds = [12, newTagIds];
        } else {
          this.tagIds = [12];
        }
      } else {
        this.tagIds = newTagIds;
      }

      this.getDataList();
    },
    getDataList(init = true) {
      if (init) {
        this.hasNext = true
        this.after = null
        this.dataList = []
      }
      if (this.loading || !this.hasNext) return
      this.loading = true
      const otherVariables = this.activeTabValue === 'private' ?
          { tagIdsAnd: this.tagIds } :
          { tagIds: this.tagIds }
      const variables = {
        first: 10,
        after: this.after,
        filter: {
          orgRouteFilter: getOrgRouteFilter(),
          orgType: 'STOREKEEPER',
          fuzzySearch: this.searchText,
          tagIds: this.tagIds,
          storeKeeperLevelIn: this.levelIn,
          lowerExistStore: true,
          suspend: false,
          ...otherVariables
        },
        sort: this.currentSortValue?.split('_')[0] || null,
        dir: this.currentSortValue?.split('_')[1] || null,
        ...this.queryMode
      }
      if (this.isEfficiency ) {
        variables.filter.hasInvestmentAmount = true
      }
      if (this.isRetention) {
        variables.filter.hasWithdrawAmount = true
      }
      console.log(JSON.stringify(variables))
      findNewStoreReq(variables)
          .then(result => {
            this.dataList = init ? [] : this.dataList
            this.dataList.push(...newStoreListMapper(getGraphPageNodes(result)))
            this.hasNext = result.data.result.pageInfo.hasNextPage
            this.after = result.data.result.pageInfo.endCursor
            this.loading = false
            if(init){
              this.$modal.closeLoading()
            }
          }).finally(() => { this.$modal.closeLoading()})
    }
  },
  created(){
    this.changeTabTag('total')
    this.getDataList()
  }
}
</script>

<template>
  <view class="block_20 flex-col">
    <view class="group_5 flex-col">
      <view class="flex-row-between" style="padding: 0 24rpx">
        <view class="text-wrapper_44 flex-row" :style="{'--new-store-tab-index': activeTabIndex}">
          <text v-for="(item, index) in tabList" :key="index"
                class="tab-item"
                :class="{'tab-active': activeTabValue === item.value, 'tab-disable': item.disabled}"
                @click="changeTab(item.value)">
            {{ item.text }}
          </text>
        </view>
        <view style="flex: 1;">
          <uni-easyinput prefix-icon="search" auto-height placeholder="搜索门店" :model-value="value" @input="updateValue" @confirm="onConfirm" @clear="onClear"/>
        </view>
      </view>
      <view class="section_13 flex-row justify-between">
        <view v-for="(item, index) in currentTabTagList" :key="index"
              class="tab-tag flex-col"
              :class="{'tab-tag-active': activeTabTagValue === item.value,'tab-tag-disable': item.disabled}"
              @click="changeTabTag(item.value,item.disabled)">
          <text class="text">{{ item.text }}</text>
        </view>
      </view>
    </view>
    <view class="group_52 flex-row justify-between" v-if="tabList.length > 0">
      <uni-data-select
          v-model="currentSortValue"
          :localdata="sortList"
          @change="changeSort"
          :clear="false"
          placeholder="请选择排序方式"/>
      <uni-data-select
          style="margin-left: 80rpx;"
          v-model="levelValue"
          :localdata="levelList"
          :clear="false"
          @change="changeLevel"
          placeholder="等级"/>
      <view class="image-text_13 flex-row justify-between">
        <text class="text-group_12" @click="moreFilterShow = true">更多筛选</text>
      </view>
    </view>
    <view class="scroll-wrapper">
      <scroll-view scroll-y style="height: 100%;" @scrolltolower="getDataList(false)">
        <view v-if="dataList.length > 0" style="padding-bottom: 16rpx;">
          <view class="group_9 flex-col" v-for="item in dataList" :style="{'align-items': isTotal?'':'flex-start'}" @click="goDetail(item)">
            <view class="block_21 flex-row justify-between">
              <view class="flex-row">
                <view class="text-wrapper_5">
                  <text class="text_10" style="font-size: 24rpx">Lv.</text>
                  <text class="text_10" style="font-size: 24rpx;">{{ item.level }}</text>
                </view>
                <text class="text_12">{{ item.name }}</text>
                <template v-if="item.countStore > 1">
                  <image
                      style="width: 38rpx;height: 38rpx;margin-left: 16rpx"
                      src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/new-store-group.png"/>
                  <text class="count-store">{{ item.countStore }}</text>
                </template>

              </view>
              <view class="text-wrapper_6 flex-row" style="align-items: center">
                <view class="text_14">{{ item.nominalProfitSharing }}%</view>
                <view class="iconfont-upgrade" style="margin: 0 6rpx;transform: rotate(90deg);font-size: 30rpx;"></view>
                <view class="text_13">{{ item.actualProfitSharing }}%</view>
                <view class="iconfont-info" style="color:#979494;margin:2rpx 6rpx 0 6rpx;font-size: 28rpx" @click.stop="showSharingInfo(item.nominalProfitSharing,item.actualProfitSharing)"></view>
              </view>
            </view>
            <view class="block_22 flex-row justify-between">
              <view class="flex-row">
                <view class="text-wrapper_31 flex-col" v-if="item.privateDomain">
                  <text class="text_15">私域</text>
                </view>
                <view class="text-wrapper_32 flex-col" v-for="cooperative in item.cooperativeModel">
                  <text class="text_16">{{ cooperative }}</text>
                </view>
              </view>
              <view class="flex-row">
                <view class="error-tag flex-col" v-for="errorTag in item.errorTag">
                  <text class="text">{{ errorTag }}</text>
                </view>
              </view>
            </view>
            <template  v-if="activeTabValue === 'profit' && activeTabTagValue === 'total'">
              <view class="flex-row-between" style="padding: 16rpx 24rpx;">
                <view style="width: 160rpx;margin-right: 32rpx;height: 150rpx;">
                  <view class="flex-row"  style="align-items: center" @click.stop="showMonthProfitInfo">
                    <view style="color: #979494;font-size: 24rpx;margin-bottom: 12rpx;font-weight: 300">月收益</view>
                    <view class="iconfont-info" style="color:#979494;margin:0 6rpx 8rpx 6rpx;font-size: 28rpx"></view>
                  </view>
                  <view style="font-weight: 400;font-size: 32rpx;color: #2E2C2B;margin-bottom: 4rpx">{{ item.last30DaysProfit }}</view>
                  <view class="flex-row" v-if="Number(item.profitFluctuations) !== 0.0 && !isNaN(Number(item.profitFluctuations)) && isFinite(Number(item.profitFluctuations))" >
                    <view :class="item.profitFluctuations>=0?'triangle-up':'triangle-down'"
                          style="margin-left: 0"
                    ></view>
                    <text style="margin-left: 4rpx;font-size: 24rpx;color: #FF3B30;font-weight: 300;" :class="Number(item.profitFluctuations)>=0?'color-red':'color-green'">
                      {{ item.profitFluctuations }}%
                    </text>
                  </view>
                </view>
                <view style="width: 500rpx;height: 170rpx;">
                  <Chart style="height: 100%" :chartData="item.chartData"/>
                </view>
              </view>
            </template>
            <template v-else>
              <view class="section_3 flex-row" style="justify-content: space-between;width: calc(654rpx - 48rpx)" >
                <view class="group_53 flex-col justify-between" :style="{width:isEfficiency?'220rpx':'191rpx'}">
                  <view class="flex-row" style="align-items: center" @click.stop="showMonthProfitInfo">
                    <text class="text_20">月收益</text>
                    <view class="iconfont-info" style="color:#979494;margin:2rpx 6rpx 0 6rpx;font-size: 28rpx"></view>
                  </view>
                  <view class="box_23" :class="isRetention?'flex-col':'flex-row'">
                    <text class="text_21">{{ item.last30DaysProfit }}</text>
                    <view class="flex-row" style="margin-top: 10rpx" v-if="Number(item.profitFluctuations) !== 0.0 && !isNaN(Number(item.profitFluctuations)) && isFinite(Number(item.profitFluctuations))">
                      <view :class="item.profitFluctuations>=0?'triangle-up':'triangle-down'"
                            :style="{marginLeft: isRetention?'0':'16rpx'}"
                      ></view>
                      <view class="text-wrapper_12" >
                        <text style="margin-left: 4rpx" class="text_22" :class="Number(item.profitFluctuations)>=0?'color-red':'color-green'">
                          {{ item.profitFluctuations }}%
                        </text>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="text-group_17 flex-col justify-between" >
                  <template v-if="activeTabValue === 'private'">
                    <text class="text_24">引流人数</text>
                    <text class="text_25">{{ item.countGroupChatMember }}</text>
                  </template>
                  <template v-else-if="isEfficiency">
                    <view class="flex-row" @click.stop="showMonthInvestorInfo">
                      <text class="text_24">投资月收益率</text>
                      <view class="iconfont-info" style="color:#979494;margin:2rpx 6rpx 0 6rpx;font-size: 28rpx" ></view>
                    </view>
                    <text class="text_25">{{ item.rateOfReturn }}%</text>
                  </template>
                  <template v-else>
                    <template v-if="item.receivable<=0">
                      <text class="text_24">待提现</text>
                      <text class="text_25">{{ (-item.receivable).toFixed(2) }}</text>
                    </template>
                    <template v-else>
                      <text class="text_24">待回款</text>
                      <text class="text_25">{{ item.receivable.toFixed(2) }}</text>
                    </template>
                  </template>
                </view>
                <view class="group_54 flex-col justify-between">
                  <template v-if="activeTabValue === 'private'">
                    <text class="text_26" style="width: auto;text-align: right">总订单</text>
                    <text class="text_27">{{ item.countValidOrder }}</text>
                  </template>
                  <template v-else-if="isEfficiency">
                    <text class="text_26" style="width: auto;text-align: right">投资金额</text>
                    <text class="text_27">{{ item.investmentAmount }}</text>
                  </template>
                  <template v-else>
                    <text class="text_26" style="width: auto;">最近提现</text>
                    <view>
                      <view class="flex-col">
                        <text class="text_27" style="width: auto;text-align: right">{{ item.lastWithdrawalAmount }}</text>
                        <text class="text_27" style="color: #979494;font-size: 24rpx;font-weight: 300">{{ item.lastWithdrawalTime }}</text>
                      </view>
                    </view>
                  </template>
                </view>
              </view>
            </template>


          </view>
          <uni-load-more status="loading" content-text="加载中" icon-size="12" :content-text="{contentrefresh:'正在加载'}" v-if="loading"></uni-load-more>
        </view>
        <view v-else-if="!loading && dataList.length <= 0" class="empty-state">
          <image src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/empty-image.png" alt="暂无数据" style="width: 100px; height: 100px;"/>
          <p>暂无数据</p>
        </view>
      </scroll-view>
    </view>
    <MoreFilter :show.sync="moreFilterShow"
                @update:show="isFilterVisible = $event"
                @change="handleFilterChange"/>
  </view>
</template>

<style lang="scss">
@import '../common.scss';
@import "../base";

</style>
