<template>
	<view class="storekeeper-list-contain">
		<view class="list" v-if="storekeeperList.length">
			<view
				class="list-item"
				v-for="(item, key) in storekeeperList"
				:key="item.node.id"
			>
				<view class="list-item-header" @click="toDetail(item)">
					<view class="list-item-title flex-row-between">
						<view class="list-item-name">{{ item.node.companyFullName }}</view>
						<view class="list-item-title-icon" v-if="false"></view>
					</view>
					<view class="list-item-header-content flex-row">
						<view class="list-item-header-content-item">{{
							item.node.responsiblePerson.realName
						}}</view>
						<view class="list-item-header-content-item"
							>门店：{{ item.node.framework.countStore }}</view
						>
						<view class="list-item-header-content-item"
							>机柜：{{ item.node.framework.countTerminal.countAll }}</view
						>
						<view class="list-item-header-content-item"
							>BD：{{
								item.node.developer ? item.node.developer.realName : "无"
							}}</view
						>
					</view>
				</view>
				<view class="list-item-content" @click="toDetail(item)">
					<view class="item-content-row flex-row-between">
						<view class="item-content-row-left">主营收入：</view>
						<view class="item-content-row-right flex-row">
							<view style="margin-right: 20rpx;">
								租宝：<text class="money-unit">￥</text>
								<text>{{ item.node.statisticTotal.revenueRentEmp / 100 }}</text>
							</view>
							<view>
								售宝：<text class="money-unit">￥</text>
								<text>{{ item.node.statisticTotal.revenueSellEmp / 100 }}</text>
							</view>
						</view>
					</view>
					<view class="item-content-row flex-row-between">
						<view class="item-content-row-left">VIP减免：</view>
						<view class="item-content-row-right">
							<text class="money-unit">￥</text>
							<text>{{ item.node.statisticTotal.expenseDiscount / 100 }}</text>
						</view>
					</view>
					<view class="item-content-row flex-row-between">
						<view class="item-content-row-left">订单实收：</view>
						<view class="item-content-row-right flex-row">
							<view style="margin-right: 20rpx;">
								租宝：<text class="money-unit">￥</text>
								<text>{{ (item.node.statisticTotal.revenueRentEmp - item.node.statisticTotal.expenseDiscount) / 100 }}</text>
							</view>
							<view>
								售宝：<text class="money-unit">￥</text>
								<text>{{ item.node.statisticTotal.revenueSellEmp / 100 }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="list-item-footer" @click="toDetail(item)">
					<view class="item-footer-row flex-row-between">
						<view class="item-footer-row-left">商户收益：</view>
						<view class="item-footer-row-right">
							<text class="money-unit">￥</text>
							<text>{{
								item.node.statisticTotal.storeKeeperFakeProfit / 100
							}}</text>
						</view>
					</view>
					<view class="item-footer-row flex-row-between">
						<view class="item-footer-row-left fb-600">我方收益：</view>
						<view class="item-footer-row-right fb-600">
							<text class="money-unit">￥</text>
							<text>{{
								item.node.statisticTotal.agentManagerProfit / 100
							}}</text>
						</view>
					</view>
				</view>
				<view class="list-item-btn-box">
					<view class="bill-btn" @click="toBill(item)">商户月账单</view>
				</view>
			</view>
		</view>
		<view class="list-empty" v-else> 暂时没有商户哦~ </view>
		<view
			class="list-end"
			v-if="!pageInfo.hasNextPage && storekeeperList.length"
		>
			<view class="list-end-line"></view>
			<view class="list-end-content">已经到底了</view>
			<view class="list-end-line"></view>
		</view>
		<view class="fix-bottom" v-if="hasAddStoreKeeperPermi">
			<view class="save-btn" @click="addStoreKeeper">新增商户</view>
		</view>
	</view>
</template>

<script>
import { findStoreKeeperList } from "@/subpkgShop/api/storeKeeperList.js";
import { hasAddStoreKeeperPermi } from "@/utils/permissionList.js";
import { toast } from "@/utils/common";
import { orgId, } from "@/utils/role.js";

export default {
	data() {
		return {
			storekeeperList: [],
			pageInfo: {},
		};
	},
	computed: {
		isToAddStoreKeeper() {
			return this.$store.state.merchant.isToAddStoreKeeper;
		},
		hasAddStoreKeeperPermi,
		orgId,
	},
	async onReachBottom() {
		console.log("触底了");
		if (!this.pageInfo.hasNextPage) return;
		this.$modal.loading("加载中");
		let list = await this.getList();
		this.storekeeperList = [
			...this.storekeeperList,
			...list,
		];
		this.$modal.closeLoading();
	},
	async onPullDownRefresh() {
		this.pageInfo = {};
		this.storekeeperList = await this.getList();
		uni.stopPullDownRefresh();
	},
	async onLoad() {
		this.storekeeperList = await this.getList();
	},
	async onShow() {
		if (!this.isToAddStoreKeeper) return;
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 0,
		});
		this.pageInfo = {};
		this.storekeeperList = await this.getList();
		this.$store.commit("merchant/SET_IS_TO_ADD_STOREKEEPER", false);
	},
	methods: {
		async getList() {
			let { pageInfo } = this;

			try {
				this.$modal.loading('加载中');
				let res = await findStoreKeeperList({
					after: pageInfo.endCursor,
					OrgRouteFilter: {
						orgId: this.orgId,
						routeTypes: ['PARENT']
					}
				});
				this.$modal.closeLoading();
				this.pageInfo = res.data.storeKeeperList.pageInfo;
				console.log(res.data.storeKeeperList.content, 'res.data.storeKeeperList.content')
				return this.formatList(res.data.storeKeeperList.content);
			} catch (e) {
				console.log(e);
				this.$modal.closeLoading();
				toast('未知错误');
				return [];
			}
		},
		formatList(list) {
			list.forEach(item => {
				item.node.statisticTotal = item.node.framework.dateRange.dataset;
			})
			return list;
		},
		addStoreKeeper() {
			this.$store.commit("merchant/SET_IS_TO_ADD_STOREKEEPER", true);
			uni.navigateTo({
				url: `/subpkg/pages/add-merchant/add-merchant`,
			});
		},
		toDetail(item) {
			uni.navigateTo({
				url: `/subpkgShop/shop-manage/shop-manage?storeKeeperId=${item.node.id}`,
			});
		},
		toBill(item) {
			uni.navigateTo({
				url: `/subpkgShop/bill-month/bill-month?storeKeeperId=${item.node.id}`,
			});
		},
	},
};
</script>

<style lang="scss" scoped>
@import "./storekeeper-list.scss";
</style>
