<template>
	<view class="widthdraw-detail-contain">
		<view class="withdraw-info">
			<view class="withdraw-info-header">提现信息</view>
			<view class="withdraw-info-content">
				<view class="withdraw-info-row info-row">
					<view class="info-row-left">提现金额(税前)：</view>
					<view class="info-row-right cash-out-right">
						{{ detail.appliedAmount ? (detail.appliedAmount / 100).toFixed(2) : 0 }}
					</view>
				</view>
				<view class="withdraw-info-row info-row">
					<view class="info-row-left">提现金额(税后)：</view>
					<view class="info-row-right cash-out-right">
						{{ detail.afterTaxAmount ? (detail.afterTaxAmount / 100).toFixed(2) : 0 }}
					</view>
				</view>
				<view class="withdraw-info-row info-row">
					<view class="info-row-left">申请人：</view>
					<view class="info-row-right">{{ detail.applicant.name }}</view>
				</view>
				<view class="withdraw-info-row info-row">
					<view class="info-row-left">电话：</view>
					<view class="info-row-right">{{ detail.applicant.phone }}</view>
				</view>
			</view>
		</view>
		<view class="withdraw-info">
			<view class="withdraw-info-header cash-out-info-header">
				<text>收款信息</text>
			</view>
			<view class="withdraw-info-content">
				<view class="withdraw-info-row info-row">
					<view class="info-row-left">提现到：</view>
					<view class="info-row-right">{{withdrawTypeString[detail.account.accountType]}}</view>
				</view>
				<view class="withdraw-info-row info-row">
					<view class="info-row-left">收款人姓名：</view>
					<view class="info-row-right">{{ detail.account.accountName }}</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="detail.account.accountType!='WECHAT_CASH'">
					<view class="info-row-left">收款账户：</view>
					<view class="info-row-right">
						<text>{{ detail.account.accountNumber }}</text>
					</view>
				</view>
				<view class="withdraw-info-row info-row" v-else>
					<view class="info-row-left">收款账户：</view>
					<view class="info-row-right flex-row">
						<image class="avatar" :src="detail.account.detailObj.accountWechatDetail.userHeadImg"></image>
						{{ detail.account.detailObj.accountWechatDetail.userNickname }}
					</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="detail.account.bankName">
					<view class="info-row-left">开户行：</view>
					<view class="info-row-right">
						<text>{{ detail.account.bankName }}</text>
					</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="detail.account.bank">
					<view class="info-row-left">开户行：</view>
					<view class="info-row-right">{{ detail.account.bank }}</view>
				</view>
				<!-- <view class="withdraw-info-row info-row" v-if="['FLEXIBLE_EMPLOYEE', 'INTERNET_BANK'].indexOf(detail.account.accountType) > -1">
					<view class="info-row-left">收款账号：</view>
					<view class="info-row-right">{{ detail.account.account }}</view>
				</view> -->
				<view class="withdraw-info-row info-row invoice-row">
					<view class="info-row-left">发票抵税：</view>
					<view class="info-row-right ">
						{{ invoiceTypeList[detail.invoice.type] || '无发票' }}
					</view>
				</view>
				<view class="withdraw-info-row" v-if="detail.invoice && detail.invoice.url && detail.invoice.url.length">
					<block v-for="(item, key) in detail.invoice.url" :key="key">
						<view class="invoice-item" @click="preview">
							<image :src="item" alt="" class="invoice-pic"></image>
							<view class="del-icon" v-if="false"></view>
						</view>
					</block>
				</view>
			</view>
		</view>
		<view class="withdraw-info">
			<view class="withdraw-info-header cash-out-info-header">
				<text>提现进度</text>
			</view>
			<view class="withdraw-info-content">
				<view class="withdraw-info-row info-row">
					<view class="info-row-left">提现状态：</view>
					<view class="info-row-right" :class="{'green': greenList.indexOf(detail.status) > -1, 'red': redList.indexOf(detail.status) > -1}">{{ processStatusTypeList[detail.status] }}</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="false && detail.Task_CheckWithdrawal_AssigneeName">
					<view class="info-row-left">审批人：</view>
					<view class="info-row-right">{{ detail.Task_CheckWithdrawal_AssigneeName }}</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="detail.status != 'WAIT_FOR_APPROVAL'">
					<view class="info-row-left">审批状态：</view>
					<view class="info-row-right">{{ detail.status === 'REJECTED' ? '驳回' : '通过' }}</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="detail.status == 'REJECTED' && detail.g_reason">
					<view class="info-row-left">驳回原因：</view>
					<view class="info-row-right">{{ detail.g_reason }}</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="false && detail.Task_Transfer_AssigneeName">
					<view class="info-row-left">打款人：</view>
					<view class="info-row-right">{{ detail.Task_Transfer_AssigneeName }}</view>
				</view>
				<view class="withdraw-info-row info-row" v-if="detail.status == 'COMPLETED' || detail.status == 'BANK_REJECTED'">
					<view class="info-row-left">打款状态：</view>
					<view class="info-row-right">{{ detail.status == 'COMPLETED' ? '通过' : '失败' }}</view>
				</view>
        <view class="withdraw-info-row info-row" v-if="detail.status == 'BANK_REJECTED' && detail.payResult">
          <view class="info-row-left">失败原因：</view>
          <view class="info-row-right">
            {{
              detail.payResult !== "null" ? (JSON.parse(detail.payResult).msg || detail.msg || detail.reason) :
                  detail.msg || detail.reason || "请再次检查收款账户是否正常，如有疑问，请咨询联盟商具体失败原因"
            }}
          </view>
        </view>
			</view>
		</view>
	</view>
</template>

<script>
import { invoiceTypeList, processStatusTypeList } from '@/utils/constant.js';
import { withdrawTypeString } from '@/utils/constant.js';

	export default {
		data() {
			return {
				invoiceTypeList,
				processStatusTypeList,
				greenList: ['WAIT_FOR_APPROVAL', 'WAIT_FOR_TRANSFER', 'COMPLETED'],
				redList: ['BANK_REJECTED','REJECTED'],
				withdrawTypeString,
			};
		},
		computed: {
			detail() {
				return this.$store.state.withdraw.shopWithdrawInfo;
			}
		},
		methods: {
			preview(item) {
				let { detail } = this;

				uni.previewImage({
					current: item,
					urls: detail.invoice.url
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
.widthdraw-detail-contain {
	padding: 20px 0 30px;
}
.withdraw-info {
	margin: 0 30rpx 40rpx;
	background-color: #fff;
	border-radius: 20rpx;
	box-shadow: 0px 1px 2px 0px rgba(81, 23, 16, 0.26);
	&:nth-last-child(1) {
		margin-bottom: 0;
	}
	.withdraw-info-header {
		padding: 20rpx 30rpx;
		margin-bottom: 16rpx;
		border-bottom: 1px solid rgba(21,21,21,0.07);
		font-size: 28rpx;
		font-weight: bold;
	}
	.cash-out-info-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.edit-icon {
			display: block;
			width: 40rpx;
			height: 40rpx;
			background-image: url('@/static/images/shopSubpkg/edit.png');
			background-size: 40rpx 40rpx;
		}
	}
	.withdraw-info-content {
		padding: 0 30rpx 30rpx;
		.info-row {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20rpx;
			font-size: 26rpx;
			&:nth-last-child(1) {
				margin-bottom: 0;
			}
			.cash-out-right {
				display: flex;
				align-items: center;
				.cash-out-input {
					width: 200rpx;
					height: 70rpx;
					margin-right: 20rpx;
					border-bottom: 1px solid #ccc;
				}
				.all-btn {
					padding: 8rpx 10rpx;
					border: 1px solid #ccc;
					border-radius: 10rpx;
				}
			}
			.info-row-left {
				width: 130px;
				flex-shrink: 0;
				font-size: 26rpx;
			}
		}
		.avatar {
			width: 40rpx;
			height: 40rpx;
			margin-right: 10rpx;
			border-radius: 50%;
		}
		.invoice-row {
			align-items: flex-start;
			.invoice-radio {
				margin-bottom: 10rpx;
				&:nth-last-child(1) {
					margin-bottom: 0;
				}
			}
			.invoice-title {}
			.invoice-subtitle {
				font-size: 24rpx;
				color: #999;
			}
			.invoice-right {
				display: flex;
				justify-content: space-between;
			}
		}
	}
}

.invoice-pic {
	margin-bottom: 10px;
	border-radius: 12rpx;
	&:nth-last-child(1) {
		margin-bottom: 0;
	}
}
</style>
