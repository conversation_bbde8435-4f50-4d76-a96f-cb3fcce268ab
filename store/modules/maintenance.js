import { findMaintenanceSettingReq } from '@/subpkgDevice/api/maintenance'
import { findBaseOrgDetailReq } from '@/api/baseOrg/baseOrg'
import { getGraphResult } from '@/utils/requestUtil'
import { toast } from '@/utils/common'
import { getOrgRouteFilter } from '@/utils/role'

const state = {
  setting: null,
  staffList: []
}

const mutations = {
  SET_SETTING: (state, setting) => state.setting = setting,
  SET_STAFF_LIST: (state, staffList) => state.staffList = staffList.filter(item => state.setting.userSettings.map(userSetting => userSetting.userId).includes(+item.id))
}

const actions = {
  GetSetting({commit}, orgId) {
    return findMaintenanceSettingReq({orgId, key: "MaintenanceSettingDto"}).then(res => {
      const setting = res.data.findSetting?.object
      commit('SET_SETTING', setting)
      return Promise.resolve(setting)
    })
  },
  async GetStaffList({commit}, orgId) {
    const action = findBaseOrgDetailReq;
    let staffList = [];
    const variables = {
      first: 50,
      after: null,
      filter: {
        orgRouteFilter:getOrgRouteFilter(["PARENT"]),
        orgTypeIn: ["COMPANY", "POSITION"],
        suspend: false,
      }
    };

    return new Promise(async (resolve, reject) => {
      try {
        while (true) {
          const data = await action(variables);
          const graphResult = getGraphResult(data); // 确保该方法已定义
          const list = graphResult.list;

          staffList.push(...list);

          if (graphResult.pageInfo.hasNextPage) {
            variables.after = graphResult.pageInfo.endCursor;
          } else {
            break;
          }
        }
        staffList = staffList.filter(item => item.id)
        staffList.forEach(item => {
          item.value = item.id
          item.text = item.name
        })
        console.log('new_staffList',staffList)

        commit('SET_STAFF_LIST', staffList);
        resolve(staffList);
      } catch (error) {
        toast('获取员工列表失败')
        console.error('获取员工列表失败:', error);
        reject(error);
      }

    });
  }
}
const getters = {
  setting: state => state.setting,
  staffList: state => state.staffList
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
