<script>
export default {
  name: 'm-empty',
  props:['text'],
}
</script>

<template>
  <view class="empty-state">
    <image src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/empty-image.png" style="width: 200rpx; height:200rpx;"/>
    <view class="text">{{text || '暂无数据'}}</view>
  </view>
</template>

<style scoped lang="scss">
.empty-state{
  display: flex;
  flex-direction: column;
  align-items: center;
}
.text{
  font-size: 28rpx;
  color: #CCCCCC;
  text-align: center;
  margin-top: 20rpx;
}
</style>
