## 2.1.2（2023-06-12）
1. [新增]添加`z-index`参数控制层级大小，默认1993
2. [说明]以后该插件只更新`uni_modules`方式的，`zip`方式的不提供更新了
## 2.1.1（2022-09-16）
1. 将插件更新为`uni_modules`方式
## 2.1.0（2022-08-12）

1. 增加`disable`参数，控制是否可以点击，只能应用在数组对象中，见[disabled 的用法](#112-当tabs使用的数组对象的方式特定参数需要注意一下)

```js
export default {
  data() {
    return {
      tabs: [{ id: 1, name: '' }]
    }
  }
}
```

## 2.0.10（2022-01-27）

1. 更新属性line-animation设置为false可以不要动画，这是好多朋友问到，特此加上

## 2.0.9（2020-10-12）

1. 修复 v-tabs 第一次可能出现第一个标签显示不完整的情况
2. 修改了 pages/tabs/order 示例文件

## 2.0.8（2020-09-21）

1. 修复添加 fixed 属性后，滚动条无效
2. 修复选项很少的情况下，下划线计算计算错误
3. 新增 paddingItem 属性，设置选项左右边距（上下边距需要设置 height 属性，或者设置 padding 属性）

## 2.0.7（2020-09-17）

1. 紧急修复 bug，横向滑动不了的情况

## 2.0.6（2020-09-16）

1. 新增 fixed 属性，是否固定在顶部，示例地址：pages/tabs/tabs-static
2. 优化之前的页面结构

## 2.0.5（2020-09-09）

1. 修复 width 错误，dom 加载的时候没有及时获取到 data 属性导致的 。

## 2.0.4（2020-08-29）

1. 优化异步改变 tabs 后，下划线不初始化问题
2. github 地址上有图 2 的源码，需要的自行下载，页面路径：pages/tabs/order.vue

## 2.0.3（2020-08-20）

1. 优化 节点查询 和 选中渲染
2. 优化支付宝中 createSelectorQuery() 的影响

**特别说明：**

> 支付宝中平铺方法和其他方法不能在一个页面中出现，不然有一个显示错误（具体什么原因没查到，有好心的人发现了，望告知一下，感谢

## 2.0.2（2020-08-19）

1. 优化 change 事件触发机制

## 2.0.1（2020-08-16）

1. 修改默认高度为 70rpx
2. 新增属性 bgColor，可设置背景颜色，默认 #fff
3. 新增整个 tab 的 padding 属性，默认 0

## 2.0.0（2020-08-13）

1. 全新的 v-tabs 2.0
2. 支持 H5 小程序 APP
3. 属性高度可配置

## 1.3.2（2020-07-21）

1. 新增 auto 的配置，是否平铺 tab
2. 修复文档上的错误示例（感谢 <EMAIL> 的反馈）

## 1.3.0（2020-07-05）

1. 新增 padding 的可配置
2. 修复 v-model 双向绑定问题
3. 修复初始化下划线没定位的为题

## 1.2.0（2020-06-19）

1. 添加注释
2. 修复 bug

## 1.1.8（2020-06-11）

1. 添加 change 事件
2. 修复插件内容问题
3. 修复下划线不居中问题

## 1.1.6（2020-06-11）

1. 添加 change 事件
2. 修复插件内容问题

## 1.1.4（2020-06-11）

1. 添加 change 事件
2. 修复插件内容问题

## 1.1.2（2020-06-11）

1. 添加 change 事件

## 1.1.1（2020-06-09）

1. 修复小程序端选中的下划线不显示问题
2. 新增 tab 高度设置
3. lineHeight 修改为只支持 String 方式

## 1.1.0（2020-06-09）

1. 修复小程序端选中的下划线不显示问题
2. 新增 tab 高度设置
3. lineHeight 修改为只支持 String 方式

## 1.0.0（2020-06-04）

1. 更新插件1.0.0