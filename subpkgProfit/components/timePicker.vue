<template>
  <view class="time-picker">
    <view class="time-picker-content">
      <uni-data-select
        v-model="timeType"
        :localdata="timeList"
        @change="changeTime"
        :clear="false"
        placeholder="请选择"
      ></uni-data-select>
    </view>
  </view>
</template>

<script>
  import { toast } from '@/utils/common';
  import { getToday, getMonth, getMonsDay, getLastMonthFirstDay, getLastMonthEndDay, getDate} from "@/utils/function.js";

  let today = getToday();

	export default {
		name:"timePicker",
		data() {
			return {
        today,
        date: [],
        type: '',
				timeSelect: [],
        dateStart: '',
        dateEnd: '',
        timeType: 1, // 1 本月  2 本周  3 今日 4 全部 5 上月  6 昨日
        timeList: [
          {
            value: 4,
            text: "全部",
          },
          {
            value: 3,
            text: "今日",
          },
          {
            value: 6,
            text: "昨日",
          },
          {
            value: 2,
            text: "本周",
          },
          {
            value: 1,
            text: "本月",
          },
          {
            value: 5,
            text: "上月",
          },
        ],
			};
		},
		props: {
		},
		methods: {
      changeTime() {

      },
      calcDate(type) {
        let typeTimeStartList = {
          1: getMonth(),
          2: getMonsDay(),
          3: getToday(),
          4: '',
          5: getLastMonthFirstDay(),
        }
        let typeTimeEndList = {
          1: getToday(),
          2: getToday(),
          3: getToday(),
          4: '',
          5: getToday(),
        }
        return { dateStart: typeTimeStartList[type - 1], dateEnd: typeTimeEndList[type - 1] }
      },
		}
	}
</script>

<style lang="scss" scoped>

</style>

