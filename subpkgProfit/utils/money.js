export function formatIncomeAmount(amount) {
  if (!amount) return {
    money: 0,
    unit: ''
  };
  // 将分转换为元
  const amountInYuan = amount / 100;

  if (amountInYuan >= 10000) {
      // 大于10000元，返回万元，保留一位小数
      const amountInTenThousand = amountInYuan / 10000;
      return {
        money: amountInTenThousand.toFixed(1),
        unit: '万'
      }
  } else {
    // 小于等于10000元，返回元，取整
    return {
      money: amountInYuan.toFixed(1),
      unit: ''
    }
  }
}