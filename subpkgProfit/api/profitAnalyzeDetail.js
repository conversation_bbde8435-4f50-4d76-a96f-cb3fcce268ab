import request from "@/utils/request.js";
import { getToday } from "@/utils/function.js";
import { getDayByTimestamp } from "@/utils/function.js";

let today = getToday();

/**
 * 获取组织下的各种列表
 */
export function findOrgFilterList(id) {
	return request({
		url: "/graphql",
		method: "post",
		data: {
			query: `{
				findCurrentOrganization(userId: ${id}){
          ... on Ally {
            listStoreKeeper {
              id,
              value: id,
              text: companyFullName
            }
            listAgent {
              id,
              value: id,
              text: companyFullName,
            }
          }
          ... on Agent {
            listStoreKeeper {
              id,
              value: id,
              text: companyFullName
            }
          }
          listEmployee {
            id,
            value: id,
            text: realName
          }
        }
			}`,
		},
	});
}

export function formatDate(date, split = "-") {
	let year = date.getFullYear();
	let month = date.getMonth() + 1;
	let day = date.getDate();

	month >= 1 && month <= 9 ? (month = "0" + month) : "";
	day >= 0 && day <= 9 ? (day = "0" + day) : "";

	return year + split + month + split + day;
}

function getDayProfitQuery(time) {
  let query = '';

  query += `time1${time.replace(/-/g,"zz")}: dataSet(catalog: ALL, dateStart:"${time}", dateEnd:"${time}") {
    discountManager: discount(operating: true)
    discountEmp: discount(operating: false)
    revenueEmp: revenue(operating: false)
    revenueManager: revenue(operating: true)
    agentManagerProfit: agentProfit(operating: true)
    agentEmpProfit: agentProfit(operating: false)
    allyManagerProfit: allyProfit(operating: true)
    allyEmpProfit: allyProfit(operating: false)
    expenseChannel
    expenseStoreKeeper
    expenseStoreKeeperIntangible
    operationProfit
    expenseChannelIntangible
    expenseService: expenseOverallService
    expenseTax
    countOrder
    countOrderEmp: countValidOrder
    expenseAmortization
    expenseCost
    storeKeeperProfit: storeKeeperProfit(operating: true)
    netRevenueManager: netRevenue(operating: true)
    netRevenueEmp: netRevenue(operating: false)
    revenueRentManager: revenueOfRent(operating: true),
    revenueRentEmp: revenueOfRent(operating: false)
    revenueSellManager: revenueOfSell(operating: true),
    revenueSellEmp: revenueOfSell(operating: false),
    expenseManager: expense(operating: true)
    expenseEmp: expense(operating: false)
    assetRedemptionChannel
    assetReceivableChannel
    allyProfitRateManager: allyProfitRate(operating: true)
    allyProfitRateEmp: allyProfitRate(operating: false)
    storeKeeperFakeProfit: storeKeeperProfit(operating: true)
  }`
  return query;
}

function getMonthRange(dateString) {
  var result = [];
  var currentDate = new Date(); // 获取当前日期
  var inputDate = new Date(dateString); // 将日期字符串转换为日期对象

  var currentMonth = currentDate.getMonth();
  var inputMonth = inputDate.getMonth();
  var currentFullYear = currentDate.getFullYear();
  var inputFullYear = inputDate.getFullYear();

  if (currentMonth === inputMonth && currentFullYear === inputFullYear) {
    // 如果给定日期是当前月份，则返回当前月份的1号到给定日期
    var currentMonthFirstDay = new Date(currentDate.getFullYear(), currentMonth, 1);
    var formattedCurrentMonthFirstDay = formatDate(currentMonthFirstDay);
    result.push({ start: formattedCurrentMonthFirstDay, end: today });
  } else {
    // 如果给定日期不是当前月份，则返回给定月份的1号和最后一天
    var inputMonthFirstDay = new Date(inputDate.getFullYear(), inputMonth, 1);
    var inputMonthLastDay = new Date(inputDate.getFullYear(), inputMonth + 1, 0);
    var formattedInputMonthFirstDay = formatDate(inputMonthFirstDay);
    var formattedInputMonthLastDay = formatDate(inputMonthLastDay);
    result.push({ start: formattedInputMonthFirstDay, end: formattedInputMonthLastDay });
  }

  return result[0];
}

function getMonthProfitQuery(time) {
  let timeRange = getMonthRange(time);
  console.log(timeRange, 'timeRange')
  let query = '';
  query += `time1${timeRange.start.replace(/-/g,"zz")}: dataSet(catalog: ALL, dateStart:"${timeRange.start}", dateEnd:"${timeRange.end}") {
    discountManager: discount(operating: true)
    discountEmp: discount(operating: false)
    revenueEmp: revenue(operating: false)
    revenueManager: revenue(operating: true)
    agentManagerProfit: agentProfit(operating: true)
    agentEmpProfit: agentProfit(operating: false)
    allyManagerProfit: allyProfit(operating: true)
    allyEmpProfit: allyProfit(operating: false)
    expenseChannel
    expenseStoreKeeper
    expenseStoreKeeperIntangible
    operationProfit
    expenseChannelIntangible
    expenseService: expenseOverallService
    expenseTax
    countOrder
    expenseAmortization
    expenseCost
    storeKeeperProfit: storeKeeperProfit(operating: true)
    netRevenueManager: netRevenue(operating: true)
    netRevenueEmp: netRevenue(operating: false)
    revenueRentManager: revenueOfRent(operating: true),
    revenueRentEmp: revenueOfRent(operating: false)
    revenueSellManager: revenueOfSell(operating: true),
    revenueSellEmp: revenueOfSell(operating: false),
    expenseManager: expense(operating: true)
    expenseEmp: expense(operating: false)
    assetRedemptionChannel
    assetReceivableChannel
    allyProfitRateManager: allyProfitRate(operating: true)
    allyProfitRateEmp: allyProfitRate(operating: false)
    storeKeeperFakeProfit: storeKeeperProfit(operating: true)
  }`
  return query;
}


// 获取某个组织日收益详情
export function getDayProfitDetail({ id, time, }) {
  let query = getDayProfitQuery(time);
	return request({
		url: "/graphql",
		method: "post",
		data: {
			query: `{
				findCurrentOrganization(userId: ${id}) {
          id
          companyFullName
          statisticAll: dataSet(catalog: ALL, dateStart:"", dateEnd:"${today}") {
            allyManagerProfit: allyProfit(operating: true)
            allyEmpProfit: allyProfit(operating: false)
            agentManagerProfit: agentProfit(operating: true)
            agentEmpProfit: agentProfit(operating: false)
            netRevenueManager: netRevenue(operating: true)
            netRevenueEmp: netRevenue(operating: false)
            countOrder
          }
          ... on StoreKeeper {
            realName
            developer {
              realName
            }
            agent {
              companyFullName
            }
					}
          ... on Agent {
            realName
            developer {
              realName
            }
					}
          ${query}
				}
			}`,
		},
	});
}

// 获取某个组织月收益详情
export function getMonthProfitDetail({ id, time, }) {
  let query = getMonthProfitQuery(time);
	return request({
		url: "/graphql",
		method: "post",
		data: {
			query: `{
				findCurrentOrganization(userId: ${id}) {
          id
          companyFullName
          statisticAll: dataSet(catalog: ALL, dateStart:"", dateEnd:"${today}") {
            allyManagerProfit: allyProfit(operating: true)
            allyEmpProfit: allyProfit(operating: false)
            agentManagerProfit: agentProfit(operating: true)
            agentEmpProfit: agentProfit(operating: false)
            netRevenueManager: netRevenue(operating: true)
            netRevenueEmp: netRevenue(operating: false)
            countOrder
          }
          ... on StoreKeeper {
            realName
            developer {
              realName
            }
            agent {
              companyFullName
            }
					}
          ... on Agent {
            realName
            developer {
              realName
            }
					}
          ${query}
				}
			}`,
		},
	});
}

// 获取某个人日收益详情
export function getEmpDayProfitDetail({ id, time, }) {
  let query = getDayProfitQuery(time);
	return request({
		url: "/graphql",
		method: "post",
		data: {
			query: `{
				findCurrentEmployee(userId: ${id}) {
          id
          companyFullName: realName
          username
          roleInfo {
            orgInfo {
              companyFullName
            }
          }
          statisticAll: dataSet(catalog: ALL, dateStart:"", dateEnd:"${today}") {
            allyManagerProfit: allyProfit(operating: true)
            allyEmpProfit: allyProfit(operating: false)
            agentManagerProfit: agentProfit(operating: true)
            agentEmpProfit: agentProfit(operating: false)
            netRevenueManager: netRevenue(operating: true)
            netRevenueEmp: netRevenue(operating: false)
            countOrder
          }
          ${query}
				}
			}`,
		},
	});
}

// 获取某个人月收益详情
export function getEmpMonthProfitDetail({ id, time, }) {
  let query = getMonthProfitQuery(time);
	return request({
		url: "/graphql",
		method: "post",
		data: {
			query: `{
				findCurrentEmployee(userId: ${id}) {
          id
          companyFullName: realName
          username
          roleInfo {
            orgInfo {
              companyFullName
            }
          }
          statisticAll: dataSet(catalog: ALL, dateStart:"", dateEnd:"${today}") {
            allyManagerProfit: allyProfit(operating: true)
            allyEmpProfit: allyProfit(operating: false)
            agentManagerProfit: agentProfit(operating: true)
            agentEmpProfit: agentProfit(operating: false)
            netRevenueManager: netRevenue(operating: true)
            netRevenueEmp: netRevenue(operating: false)
            countOrder
          }
          ${query}
				}
			}`,
		},
	});
}