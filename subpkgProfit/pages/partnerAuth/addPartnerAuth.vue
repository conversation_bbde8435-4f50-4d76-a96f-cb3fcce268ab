<template>
  <view>
    <view class="choose-box">
      <view class="input-wrap">
        <view class="auth-to-text">
          <span style="font-size: 14px">授权给: {{ !readyToAdd ? inputOrgName : '' }}</span>
        </view>
        <view class="auth-ally-text">
          <input v-if="readyToAdd" :disabled="existedAuth" v-model="inputOrgName" placeholder="请输入联盟商全名"/>
        </view>
        <view v-if="!existedAuth" class="button-wrap">
          <button @click="search" plain="true">
            <view class="search-icon"></view>
            <view>搜索</view>
          </button>
        </view>
      </view>
      <view class="org-select-input">
        <uni-data-select
            class="select-org-string-op"
            v-model="selectionOrgId"
            v-if="orgOptions && orgOptions.length && !existedAuth"
            :localdata="orgOptions"
            @change="changeOrg"
            placeholder="请选择搜索结果"/>
      </view>

      <view class="choose-bottom" v-if="false">
        <view class="id-input-box">
          <view class="choose-bottom-id">
            id:
          </view>
          <view>{{ selectedOrg.id ? selectedOrg.id : '选择联盟商后自动匹配' }}</view>
        </view>
        <view class="name-input-box">
          <view class="choose-bottom-name">
            负责人:
          </view>
          <view>
            {{ selectedOrg.name ? selectedOrg.name : '选择联盟商后自动匹配' }}
          </view>
        </view>
      </view>
    </view>


    <view class="auth-box">
      <view class="auth-title">
        授予权限
      </view>
      <view class="auth-item" v-for="(auth, key) in authOptions.permissions" :key="key">
        <view class="auth-item-wrap" v-if="auth.show">
          <view class=" auth-item-title">
            <view class=" auth-item-title-1"> {{ auth.desc }}</view>
            <view v-if="!orgAuth.fullAccess" class="auth-item-switch-wrap">
              <zero-switch class="auth-item-switch" size="15" background-color-on="#1980FA" active-color="#fff"
                           :model-value="authed(key)" @change="(b)=>changeAuth(b, key)" :stop-change="auth.forbidden"/>
            </view>

            <view v-else>
              <view class="auth-item-is-authed" v-if="authed(key)">
                已授权
              </view>
              <view class="auth-item-no-authed" v-if="!authed(key)">未授权</view>
            </view>
          </view>
          <view class="auth-desc">
            <view class="auth-desc-item" v-for="(text, i) in auth.authCapabilities" :key="text">
              {{ `${i + 1}. ${text}` }}
            </view>
          </view>
        </view>


      </view>
      <view class="fixed-bottom" v-if="!orgAuth.fullAccess">
        <view class="button-wrapper">
          <view class="delete" v-if="!readyToAdd">
            <button @click="deleteAuth" :disabled="!editable()">删除授权</button>
          </view>
          <view :class="{'add':true, 'forbidden': forbidSubmit()}">
            <button @click="submitAuth" :disabled="!editable()">确认授权</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {queryCompanyByID} from "@/api/subpkg/ally";
import {orgId} from "@/utils/role";
import {parseGraphQLConnection} from "@/utils/graphql";
import {changeAuth, findCompanySimpleInfoPage, queryPartnerById} from "@/subpkgProfit/api/partnerAuth";

export default {
  data() {
    return {
      readyToAdd: false,
      selectionOrgId: undefined,
      existedAuth: false,
      inputOrgName: undefined,
      searchOrgRes: [],
      orgOptions: [],
      orgAuth: [],
      selectedOrg: undefined,
      authOptions: {
        fullAccess: undefined,
        permissions: {
          PERMISSION_EXCHANGE: {
            desc: "订单授权",
            authCapabilities: [
              "订单查询",
              "订单暂停计费、人工结束",
              "订单退款",
              "下载订单历史",
            ],
            open: false,
            show: true
          },
          PERMISSION_ORG: {
            desc: '组织授权',
            authCapabilities: [
              "显示商户及门店联系方式",
              "优惠券与付费会员查询",
              "下载商户及门店明细",
            ],
            open: false,
            show: true,
            forbidden: false
          },
          PERMISSION_VOUCHER: {
            desc: '账务授权',
            authCapabilities: [
              "查询详细营收数据",
              "查询预付款及进场费信息",
              "查询代理商及商户余额",
            ],
            open: false,
            show: true,
            forbidden: true
          }
        }
      },
      changedAuth: {},
      authToMe: false,
      fullAccess: false
    };
  },
  computed: {
    orgId
  },
  onLoad: async function (option) {
    this.readyToAdd = option.selectedOrgId === 'undefined'
    this.authToMe = option.authToMe === 'true'
    this.fullAccess = option.fullAccess === 'true'


    console.log(this.readyToAdd)
    if (this.fullAccess) {
      uni.setNavigationBarTitle({
        title: "查看授权"
      });
    } else if (!this.readyToAdd) {
      uni.setNavigationBarTitle({
        title: "修改授权"
      });
    }

    await this.init(option.selectedOrgId)
  },
  methods: {
    async init(selectedOrgId) {
      console.log(selectedOrgId)
      if (selectedOrgId && selectedOrgId !== 'undefined') {
        this.existedAuth = true
        console.log("selectedOrgId", selectedOrgId)
        const {data} = await queryCompanyByID({id: Number(selectedOrgId)})
        this.selectedOrg = data?.findOrgDetailById
        this.inputOrgName = this.selectedOrg.companyFullName
        await this.getAuth()
        const {permissions, fullAccess} = this.orgAuth
        this.authOptions.fullAccess = fullAccess
        permissions?.forEach(p => {
          if (this.authOptions.permissions[p]) {
            this.authOptions.permissions[p].open = true
          }
        })
      }
    },
    async changeOrg(v) {
      const {authorized, authorizer} = await this.fetchPartner()
      const existed = authorized?.some(auth => auth.ally.id == v);
      const isSuper = authorizer?.some(auth => auth.ally.id == v)
      if (existed) {
        uni.showToast({
          title: '该授权已存在，请返回上级页面选择修改',
          icon: 'none',
          duration: 2000,
          mask: false,
        })
        this.resetSearch()
        return
      }
      // else if (isSuper) {
      //   uni.showToast({
      //     title: '无法授权上级直营',
      //     icon: 'none',
      //     duration: 2000,
      //     mask: false,
      //   })
      //   this.resetSearch()
      //   return
      // }
      this.selectedOrg = this.searchOrgRes.find(org => Number(org.id) === Number(v))
      await this.getAuth()
      const {fullAccess} = this.orgAuth
      this.authOptions.fullAccess = this.fullAccess = fullAccess
      for (let permissionsKey in this.authOptions.permissions) {
        const {forbidden} = this.authOptions.permissions[permissionsKey]
        if (!forbidden) {
          this.authOptions.permissions[permissionsKey].open = true
        }
      }
      console.log(this.authOptions.permissions)
    },
    async fetchPartner() {
      const {data} = await queryPartnerById(this.orgId)
      return data.findPartnerById
    },
    async getAuth() {
      const {authorized, authorizer} = await this.fetchPartner()
      console.log('---')
      console.log(authorized)
      console.log(authorizer)
      console.log(this.selectedOrg.id)
      console.log(this.authToMe)
      if (this.authToMe) {
        console.log('auth to me 123')
        for (let auth of authorizer) {
          if (auth.ally.id == this.selectedOrg.id) {
            this.orgAuth = auth
          }
        }
      } else {
        for (let auth of authorized) {
          if (auth.ally?.id == this.selectedOrg.id) {
            this.orgAuth = auth
          }
        }
      }
      console.log(this.orgAuth)
    },
    authed(permission) {
      const fullAccess = this.authOptions.fullAccess
      console.log(this.orgAuth)
      console.log(this.orgAuth.permissions, permission)
      const permissions = []
      for (let permissionsKey in this.authOptions.permissions) {
        if (this.authOptions.permissions[permissionsKey].open) {
          permissions.push(permissionsKey)
        }
      }
      const permissionsInclude = permissions.includes(permission)
      console.log("permission include", permissionsInclude, "all auth", fullAccess)
      console.log("orgAuth", this.orgAuth)
      return Boolean(permissionsInclude || fullAccess)
    },
    changeAuth(e, k) {
      if (this.authOptions.permissions[k].forbidden) {
        uni.showToast({
          title: '功能暂不可用，即将推出',
          icon: 'none',
          duration: 2000,
          mask: false,
        })
        return
      }
      if (this.orgAuth.fullAccess) {
        uni.showToast({
          title: '无法更改此公司权限：该公司拥有所有访问权',
          icon: 'none',
          duration: 2000,
          mask: false,
        })
        return
      }
      console.log(e, k)
      this.authOptions.permissions[k].open = e
    },
    async submitAuth() {
      const f = async () => {
        if (!this.selectedOrg?.id) {
          uni.showToast({
            title: '请选择要授权的联盟商',
            icon: 'none',
            duration: 2000,
            mask: false,
          })
          return
        }

        if (this.orgId == this.selectedOrg.id) {
          uni.showToast({
            title: '无法授权当前组织',
            icon: 'none',
            duration: 2000,
            mask: false,
          })
          return
        }

        const {code} = await changeAuth({
          authorizerId: this.orgId,
          authorizedId: this.selectedOrg.id,
          permissions
        })
        if (code == 200) {
          uni.showToast({
            title: '操作成功',
            icon: 'success', // 可选值：'success', 'loading', 'none'
            duration: 2000, // 持续时间，单位为ms
            mask: false, // 是否显示透明蒙层，防止触摸穿透
            success: function () {
              console.log('确认授权成功');
              setTimeout(() => {
                uni.navigateBack({
                  delta: 1 // 返回的页面数，1 表示返回上一页
                });
              }, 1500)
            },
          })
        }
      }
      const permissions = []
      for (let permissionsKey in this.authOptions.permissions) {
        if (this.authOptions.permissions[permissionsKey].open) {
          permissions.push(permissionsKey)
        }
      }
      if (!permissions.length) {
        uni.showToast({
          title: '请选择需要修改的权限',
          icon: 'none',
          duration: 2000,
          mask: false,
        })
        return
      }
      uni.showModal({
        title: '提示',
        content: '您确定要授权吗？',
        success: (res) => {
          if (res.confirm) {
            console.log('用户点击确定');
            f()
            // 执行确认操作
          } else if (res.cancel) {
            console.log('用户点击取消');
            // 执行取消操作
          }
        }
      });

    },
    async deleteAuth() {
      const f = async () => {
        const {code} = await changeAuth({
          authorizerId: this.orgId,
          authorizedId: this.selectedOrg.id,
          permissions: []
        })
        if (code == 200) {
          uni.showToast({
            title: '操作成功',
            icon: 'success', // 可选值：'success', 'loading', 'none'
            duration: 2000, // 持续时间，单位为ms
            mask: false, // 是否显示透明蒙层，防止触摸穿透
            success: function () {
              console.log('删除授权成功');
              setTimeout(() => {
                uni.navigateBack({
                  delta: 1 // 返回的页面数，1 表示返回上一页
                });
              }, 1500)
            },
          })
        }
      }
      uni.showModal({
        title: '提示',
        content: '您确定删除授权吗？',
        success: (res) => {
          if (res.confirm) {
            console.log('用户点击确定');
            f()
          } else if (res.cancel) {
            console.log('用户点击取消');
            // 执行取消操作
          }
        }
      });
    },
    async search() {
      this.resetSearch()
      this.inputOrgName = this.inputOrgName?.trim()
      if (!this.inputOrgName) {
        return
      }
      const conn = await findCompanySimpleInfoPage({companyFullName: this.inputOrgName, orgType: 'ALLY'})
      const [list, pageInfo] = parseGraphQLConnection(conn)
      if (list && list.length) {
        this.searchOrgRes = list
        this.orgOptions = list.map(org => ({
          text: org.id + ': ' + org.companyFullName,
          value: org.id,
          data: org
        }))
        // 自动选择第一个结果
        this.selectionOrgId = list[0].id
        await this.changeOrg(list[0].id)
      } else {
        this.searchOrgRes = this.orgOptions = []
        uni.showToast({
          title: '未找到匹配的结果',
          icon: 'none',
          duration: 2000,
          mask: false
        })
      }
    },
    resetSearch() {
      this.searchOrgRes = []
      this.orgOptions = []
      this.selectedOrg = {}
      for (let permissionsKey in this.authOptions.permissions) {
        this.authOptions.permissions[permissionsKey].open = false
      }
    },
    editable() {
      return Boolean(!this.fullAccess && !this.authToMe);
    },
    forbidSubmit() {
      const permissions = []
      for (let permissionsKey in this.authOptions.permissions) {
        if (this.authOptions.permissions[permissionsKey].open) {
          permissions.push(permissionsKey)
        }
      }
      return Boolean(!permissions?.length)
    },
    popDialog(str) {
      uni.showToast({
        title: 'str',
        icon: 'none',
        duration: 2000,
        mask: false
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.choose-box {
  margin-top: 1px;
  font-size: 14px;
  background: #fff;
  padding: 16px 16px 16px 16px;

  .org-select-input {
    margin-top: 8px;
    background: linear-gradient(90deg, rgba(57, 53, 253, 0.1) 0%, rgba(62, 170, 249, 0.1) 100%);

    .select-org-string-op {
      width: 100%;
    }

    .uni-select__input-text {
      text-align: center;
      background: linear-gradient(90deg, #3935FD 0%, #3EAAF9);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

  }

  .input-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;

    //width: 279px;
    max-height: 36px;

    .auth-ally-text {
      flex-grow: 1;

      input {
        box-sizing: border-box;
        padding-left: 12px;
        border-radius: 4px 0 0 4px;
        border: 1px solid #EBE9E7;
        color: #000000;
        font-weight: 400;
        background: #FAFAFA;
        height: 36px;
      }
    }

    .auth-to-text {
      font-size: 14px;
    }

    .button-wrap {
      max-width: 72px;
      height: 36px;
      border: 1px solid #EBE9E7;
      border-left: none;
      box-sizing: border-box;
      flex-grow: 1;


      border-radius: 0 4px 4px 0;

      button {
        //box-sizing: border-box;
        height: 100%;
        width: 100%;
        display: flex;
        font-size: 13px;
        color: #6F6A67;
        justify-content: center;
        align-items: center;
        background: #FFFFFF;
        border: none;
      }

      .search-icon {
        width: 16px;
        height: 16px;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/search-16pt-autho.png");
      }
    }

    span {
      margin-right: 14px;
    }
  }

  .choose-bottom {
    display: flex;

    .choose-bottom-id {
      width: 23px;
    }

    .choose-bottom-name {
      width: 48px;
    }

    justify-content: space-between;
    margin-top: 22px;

    .id-input-box {
      display: flex;
      align-items: center;
      font-size: 12px;

    }

    .name-input-box {
      display: flex;
      align-items: center;
      font-size: 12px;
    }
  }
}

.auth-box {
  padding: 0 12px 100px 12px;

  .auth-title {
    margin: 15px 28px 8px 16px;
    width: 56px;
    height: 20px;
    font-weight: 500;
    font-size: 14px;
    color: #6F6A67;
    line-height: 16px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .auth-item {

    background: #FFFFFF;

    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;

    .auth-item-wrap {
      padding: 16px 16px 12px;
    }

    .auth-item-title {
      border-bottom: 1px solid #d9d9d9;
      padding-bottom: 11px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .auth-item-switch-wrap {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        max-height: 24px;
        max-width: 32px;

        .auth-item-switch {
          height: 100%;
          width: 100%;
        }
      }

      .auth-item-is-authed {
        font-weight: 500;
        font-size: 14px;
        color: #1980FA;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        width: 42px;
        height: 20px;
      }

      .auth-item-no-authed {
        width: 42px;
        height: 20px;

        font-weight: 400;
        font-size: 14px;
        color: #B2B0AF;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .auth-item-title-1 {
        display: flex;
        align-items: center;
        width: 56px;
        height: 20px;
        font-weight: 400;
        font-size: 14px;
        color: #2E2C2B;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .auth-desc {
      margin: 12px 16px 16px 0;

      .auth-desc-item {
        width: fit-content;
        height: 17px;
        font-weight: 400;
        font-size: 12px;
        color: #6F6A67;
        line-height: 14px;
        font-style: normal;
        text-transform: none;

        &:not(:last-child) {
          margin-bottom: 8px;
        }
      }

    }
  }
}

.fixed-bottom {
  padding-top: 8px;
  position: fixed;
  bottom: 0;
  left: 50%;
  width: 100%;
  transform: translateX(-50%);
  text-align: center;
  color: white; /* 这个可以去掉，使用渐变 */
  font-size: 32px;
  letter-spacing: 2px;
  padding-bottom: 38px;
  background: #ffffff;

  .button-wrapper {
    display: flex;
    padding: 0 16px;
    column-gap: 8px;

    button {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .delete, .add {
    flex: 1 1 auto;
    font-size: 14px;
    font-weight: 500;
    width: 167.5px;
    height: 40px;
  }

  .add {
    button {
      font-size: 14px;
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #FFFFFF;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
      border-radius: 4px 4px 4px 4px;
      background: linear-gradient(45deg, #3935FD, #3EAAF9);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: 500;
    }
  }

  .forbidden {
    button {
      font-size: 14px;
      background: #FFFFFF;
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
      border-radius: 4px 4px 4px 4px;
      background: linear-gradient(45deg, #efefef, #efefef);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: 500;
    }
  }

  .delete {
    button {
      background: #FFFFFF;
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
      border-radius: 4px 4px 4px 4px;
      font-size: 14px;
      color: #6F6A67;
    }
  }
}
</style>
