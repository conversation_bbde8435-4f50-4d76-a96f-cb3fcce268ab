<template>
	<view class="ally-customer-service-info">
		<view class="tab-box flex-row">
			<view class="tab-item" :class="{'tab-item-active': tab === 1}" @click="changeTab(1)">
				<view class="tab-item-title">新增客服账号</view>
				<view class="tab-item-info">获取客服链接</view>
			</view>
			<view class="tab-item" :class="{'tab-item-active': tab === 2}" @click="changeTab(2)">
				<view class="tab-item-title">新增接待人员</view>
				<view class="tab-item-info">已有客服账号</view>
			</view>
		</view>
		<view class="info-content" v-if="tab === 1">
			<image src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/wk_icon.ttf"></image>
		</view>
		<view class="info-content" v-if="tab === 2"></view>
	</view>
</template>

<script src="./ally-customer-service-info.js">

</script>

<style lang="scss">
@import './ally-customer-service-info.scss'
</style>
