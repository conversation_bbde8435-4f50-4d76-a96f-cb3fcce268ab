<template>
	<view class="index-partner-detail">
		<mNav
			:isShowBack="true"
			title="详情信息"
			titleColor="#fff"
			topBarColor="linear-gradient(to bottom, #3937fd 0%, #3a57fc 100%)"
			statusBarColor="linear-gradient(to bottom, #3a57fc 0%, #3c7bfb 100%)"
		/>
		<view class="banner">
			<view class="title-row flex-row-between">
				<view class="title flex-row">
					<view class="ally-icon-box">
						<image class="ally-icon" src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-detail-name-icon.png" alt="ally-icon"></image>
					</view>
					<view>
						{{ detail.allyName ? detail.allyName : '' }}
					</view>
				</view>
				<view class="card-icon" v-if="detail.allyName" @click="handleShowTips('cardInfo')"></view>
			</view>
			<view class="flex-row-between">
				<view class="info-row flex-row">
					<view class="info-row-title">负责人：</view>
					<view class="info-row-value" v-if="detail.allyDetail">{{ detail.allyDetail.responsiblePerson.name }}</view>
				</view>
				<view class="info-row flex-row">
					<view class="info-row-title">加盟日：</view>
					<view class="info-row-value">{{ detail.allyDetail && detail.allyDetail.createdDate ? detail.allyDetail.createdDate.slice(0, 10) : '' }}</view>
				</view>
			</view>
		</view>
		<view class="payment-box info-box" v-if="false">
			<view class="info-sub-box">
				<view class="info-sub-row flex-row">
					<view class="info-sub-row-title wx-icon"></view>
					<view class="info-sub-row-right">
						<view>{{ detail.accountWechatMchId }}</view>
						<view class="info-sub-row-company">{{ detail.accountWechatMchName }}</view>
					</view>
				</view>
				<view class="info-sub-row flex-row">
					<view class="info-sub-row-title zfb-icon"></view>
					<view class="info-sub-row-right">
						<view>{{ detail.accountAlipayMchId }}</view>
						<view class="info-sub-row-company">{{ detail.accountAlipayMchName }}</view>
					</view>
				</view>
			</view>
		</view>
		<view class="info-box" v-if="false">
			<view class="info-row flex-row-between">
				<view class="info-row-title">当前等级：</view>
				<view class="info-row-value">{{ detail.level }}</view>
			</view>
			<view class="info-row flex-row-between" v-if="detail.fixed">
				<view class="info-row-title">是否固定定级：</view>
				<view class="info-row-value">{{ detail.fixed ? '是' : '否' }}</view>
			</view>
			<view class="info-row flex-row-between" v-if="detail.fixed">
				<view class="info-row-title">固定定级原因：</view>
				<view class="info-row-value">{{ detail.fixReason }}</view>
			</view>
			<view class="info-row flex-row-between" v-if="detail.fixed && detail.fixUntilDate">
				<view class="info-row-title">固定定级截止日期：</view>
				<view class="info-row-value">{{ detail.fixUntilDate }}</view>
			</view>
			<view class="table-contain">
				<view class="table-box">
					<view class="device-table-tr device-table-title-tr">
						<view class="device-table-th device-table-th-1 device-table-th-row">
							<view>参评值</view>
							<view>(月总收益/设备总数)</view>
						</view>
						<view class="device-table-th device-table-th-2">次月等级</view>
						<view class="device-table-th device-table-th-3">次月净营收率</view>
					</view>
					<view class="device-table-row-tr" v-for="(level, key) in levelList" :key="key" :class="{'device-table-row-tr-active': level.level == detail.level}">
						<view class="device-table-tr">
							<view class="device-table-td device-table-th-1 device-table-td-1">
								<view class="device-table-td-icon" v-if="level.level == detail.level">当前</view>
								<view class="border-bottom">{{ level.promotionAmount }}</view>
								<view class="" style="line-height: 32px;">{{ level.promotionTerminalCount }}台</view>
							</view>
							<view class="device-table-td device-table-th-2 device-table-td-2">{{ level.level }}</view>
							<view class="device-table-td device-table-th-3 device-table-td-3">
								{{ level.sharingRatio }}
							</view>
						</view>
					</view>
					<view class="device-table-empty" v-if="!levelList.length">
						<text>暂无设备数据</text>
					</view>
				</view>
			</view>
		</view>
		<view class="ally-detail-content">
			<view class="level-box flex-row">
				<view class="level-item" @click="toLevelInfo">
					<view class="flex-row-between">
						<view class="flex-row">
							<view class="level-title">当前团队：</view>
							<view class="level">{{ detail.level }}</view>
						</view>
						<view class="info-icon"></view>
					</view>
					<view class="promotion-row flex-row">
						<view class="">{{ detail.all ? formatMoney(detail.all.lastMonthDate.dataset.assetAccountDeposit / 100) : '0' }}元</view>
					</view>
					<view class="flex-row">
						<view class="">{{ detail.all ? detail.all.countTerminal.countAll : '0' }}</view>
						<view class="">台</view>
					</view>
				</view>
				<view class="leve-arrow-icon"></view>
				<view class="level-item next-level-item" v-if="nextLevelDetail.level" @click="toLevelInfo">
					<view class="flex-row-between">
						<view class="flex-row">
							<view class="level-title">晋升目标：</view>
							<view class="level">{{ nextLevelDetail.level }}</view>
						</view>
					</view>
					<view class="promotion-row flex-row">
						<view class="">{{ nextLevelDetail.promotionAmount }}元</view>
					</view>
					<view class="flex-row">
						<view class="">{{ nextLevelDetail.promotionTerminalCount }}</view>
						<view class="">台</view>
					</view>
				</view>
				<view class="level-item next-level-item" v-else>
					<view class="next-level-empty">{{ allyId == orgId ? '您' : ''}}已达最高等级</view>
				</view>
			</view>
			<view class="card">
				<view class="card-title flex-row-between" @click="toAssetDetail">
					<view class="card-title-text">资产规模</view>
					<view class="card-more"></view>
				</view>
				<view class="card-content">
					<view class="card-asset-total flex-row-between">
						<view class="card-asset-total-title">总机柜数</view>
						<view>{{ detail.framework ? detail.framework.countTerminal.countAll : '0' }}台</view>
					</view>
					<view class="card-asset-footer flex-row-between" v-if="detail.lastAssetRelShiftSlip">
						<view class="flex-row">
							<view class="">最近：{{ detail.lastAssetRelShiftSlip ? (detail.lastAssetRelShiftSlip.senderOwnerId === orgId ? '转让' : '购入') : '' }}</view>
							<view class="asset-list flex-row">
								<view class="device-item flex-row" v-if="channelList.channel6">
									<view class="device-item-title device-item-title-6"></view>
									<view class="device-item-value">{{ channelList.channel6 }}</view>
								</view>
								<view class="device-item flex-row" v-if="channelList.channel8">
									<view class="device-item-title device-item-title-8"></view>
									<view class="device-item-value">{{ channelList.channel8 }}</view>
								</view>
								<view class="device-item flex-row" v-if="channelList.channel12">
									<view class="device-item-title device-item-title-12"></view>
									<view class="device-item-value">{{ channelList.channel12 }}</view>
								</view>
							</view>
						</view>
						<view class="">{{ detail.lastAssetRelShiftSlip ? detail.lastAssetRelShiftSlip.createdTime.slice(0, 10) : '' }}</view>
					</view>
				</view>
			</view>
			<view class="card">
				<view class="card-title flex-row-between" @click="toActivateHistory">
					<view class="card-title-text">铺设进度</view>
					<view class="card-more"></view>
				</view>
				<view class="card-content">
					<view class="activate-row flex-row-between">
						<view class="row-title">铺设率</view>
						<view class="activate-rate flex-row">{{ detail.framework ? calc(detail.framework.countTerminal.countWorking, detail.framework.countTerminal.countAll) : '0' }}%</view>
					</view>
					<view class="chart-box">
						<qiun-data-charts canvas2d type="column" :opts="opts" :chartData="chartData" :reshow="reshow"  tooltipFormat="indexAllyDetailToolTips"/>
					</view>
				</view>
			</view>
			<view class="card">
				<view class="card-title flex-row-between" @click="toTurnListOver">
					<view class="card-title-text">收益利润</view>
					<view class="card-more"></view>
				</view>
				<view class="card-content">
					<view class="profit-row flex-row-between">
						<view class="row-title">总利润</view>
						<view class="profit-money flex-row">
							<view class="money-unit">¥</view>
							<view class="money">{{ detail.framework ? formatMoney(detail.framework.dateRange.dataset.allyManagerProfit / 100) : '0' }}</view>
						</view>
					</view>
					<view class="profit-content">
						<view class="profit-item">
							<view class="profit-item-title">总收益</view>
							<view class="profit-item-value">￥{{ detail.framework ? formatMoney(detail.framework.dateRange.dataset.assetAccountDeposit / 100) : '0' }}</view>
						</view>
						<view class="profit-item" @click="handleShowTips('terminalProfit')">
							<view class="profit-item-title flex-row">
								<view>台均流水</view>
								<view class="info-icon"></view>
							</view>
							<view class="profit-item-value">￥{{ detail.framework && detail.framework.countTerminal.countWorking ? (detail.framework.last30Days.dataset.assetAccountDeposit / detail.framework.countTerminal.countWorking / 30 / 100).toFixed(1) : '0' }}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="card">
				<view class="card-title flex-row-between">
					<view class="card-title-text">运维质量</view>
					<view class=""></view>
				</view>
				<view class="card-content">
					<view class="online-rate-row flex-row-between" @click="handleShowTips('onlineRate')">
						<view class="flex-row">
							<view class="online-rate-title">在线率</view>
							<view class="info-icon"></view>
						</view>
						<view class="online-rate">{{ detail.framework ? calc((detail.framework.countTerminal.countWorking - detail.framework.countTerminalDetail.countLongTermOffline), detail.framework.countTerminal.countWorking) : '0'}}%</view>
					</view>
				</view>
			</view>
		</view>
		<Tips :showTips="showTips" :showTipsType="showTipsType" :detail="detail" @closePopup="closePopup" />
	</view>
</template>

<script src="./index-ally-detail.js">
	
</script>

<style lang="scss" scoped>
@import './index-ally-detail.scss'
</style>
