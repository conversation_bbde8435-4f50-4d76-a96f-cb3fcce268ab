<template>
	<view class="tips">
    <uni-popup ref="tipsPopup" :animation="false" :maskClick="false">
			<view class="popup-box">
        <view class="popup-content" v-if="showTipsType == 'terminalProfit'">
          <view class="tip-title">台均流水</view>
          <view class="tip-content">
            <view class="">近30天内平均每台设备的日均流水</view>
          </view>
          <view class="tip-btn" @click="closePopup">关闭</view>
        </view>
				<view class="popup-content" v-if="showTipsType == 'onlineRate'">
          <view class="tip-title">在线率</view>
          <view class="tip-content">
            <view class="">已激活的机柜,在72小时内处于工作中(在线)的比例</view>
          </view>
          <view class="tip-btn" @click="closePopup">关闭</view>
        </view>
        <view class="popup-content" v-if="showTipsType == 'cardInfo'">
          <view class="tip-title">收款信息</view>
          <view class="payment-box">
            <view class="info-sub-box">
              <view class="info-sub-row flex-row">
                <view class="wx-icon-box">
                  <view class="info-sub-row-title wx-icon"></view>
                </view>
                <view class="info-sub-row-right">
                  <view class="flex-row" @click="clipboard(detail.accountWechatMchId)">
                    <view class="">{{ detail.accountWechatMchId }}</view>
                    <view class="clip-icon"></view>
                  </view>
                  <view class="info-sub-row-company">{{ detail.accountWechatMchName }}</view>
                </view>
              </view>
              <view class="info-sub-row flex-row">
                <view class="zfb-icon-box">
                  <view class="info-sub-row-title zfb-icon"></view>
                </view>
                <view class="info-sub-row-right">
                  <view class="flex-row" @click="clipboard(detail.accountAlipayMchId)">
                    <view>{{ detail.accountAlipayMchId }}</view>
                    <view class="clip-icon"></view>
                  </view>
                  <view class="info-sub-row-company">{{ detail.accountAlipayMchName }}</view>
                </view>
              </view>
            </view>
          </view>
          <view class="tip-btn" @click="closePopup">关闭</view>
        </view>
			</view>
		</uni-popup>
	</view>
</template>

<script>

export default {
  props: {
    showTips: {
      type: Boolean,
      default: false,
    },
    showTipsType: {
      type: String,
      default: '',
    },
    detail: {
      type: Object,
      default: {},
    },
  },
  watch: {
    showTips(newVal) {
      if (newVal) {
        this.$refs.tipsPopup.open();
      }
    }
  },
  methods: {
    closePopup() {
      this.$refs.tipsPopup.close();
      this.$emit('closePopup')
    },
    clipboard(text) {
			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: "复制成功",
						icon: "success",
					});
				},
			});
		},
  }
};
</script>

<style lang="scss" scoped>
.popup-box {
  width: 638rpx;
  padding-top: 16px;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  .tip-title {
    margin-bottom: 16px;
    font-size: 32rpx;
    line-height: 44rpx;
    font-weight: bold;
    color: #2E2C2B;
    letter-spacing: 1rpx;
    text-align: center;
  }
  .tip-content {
    margin: 0 32rpx;
    margin-bottom: 16px;
    text-align: center;
    font-size: 14px;
    line-height: 20px;
    color: #2E2C2B;
    letter-spacing: 1rpx;
  }
  .shadow-box {
    padding: 8px 0;
    margin: 12px 32rpx 16px;
    background: #FAFAFA;
    border-radius: 4px;
    color: #6F6A67;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 1rpx;
  }

  .payment-box {
    margin: 0 16px 16px;
    background-color: #fff;
    border-radius: 8px;
    &:nth-last-child(1) {
      margin-top: 12px;
    }
    .info-row {
      margin-bottom: 32rpx;
      &:nth-last-child(1) {
        padding-bottom: 32rpx;
        border-bottom: 1px solid #EBE9E7;
      }
      .info-row-title {
        flex-shrink: 0;
        margin-right: 40rpx;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #6F6A67;
        letter-spacing: 1rpx;
      }
      .info-row-value {
        font-size: 28rpx;
        line-height: 40rpx;
        color: #2E2C2B;
        letter-spacing: 1rpx;
      }
    }
    .info-sub-box {
      .sub-title {
        margin-bottom: 24rpx;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #6F6A67;
        letter-spacing: 1rpx;
      }
      .info-sub-row {
        display: flex;
        margin-bottom: 12px;
        padding-bottom: 12px;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #2E2C2B;
        letter-spacing: 1rpx;
        border-bottom: 1px solid #EBE9E7;
        &:nth-last-child(1) {
          margin-bottom: 0;
          border-bottom: none;
          padding-bottom: 0;
        }
        .info-sub-row-title {
          width: 24px;
          height: 24px;
          background-size: 24px 24px;
        }
        .wx-icon-box,
        .zfb-icon-box {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80rpx;
          height: 104rpx;
          background: rgba(67,183,48,0.1);
          border-radius: 4px 4px 4px 4px;
        }
        .zfb-icon-box {
          background: rgba(25,128,250,0.1);
        }
        .wx-icon {
          background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/wx-icon.png');
        }
        .zfb-icon {
          background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/zfb-icon.png');
        }
        .info-sub-row-right {
          margin-left: 24rpx;
        }
        .info-sub-row-company {
          margin-top: 4px;
        }
        .clip-icon {
          width: 24rpx;
          height: 24rpx;
          margin-left: 8rpx;
          background-size: 24rpx 24rpx;
          background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/stock-terminal-detail.powerbank-status-icon11.png');
        }
      }
    }
  }


  .tip-btn {
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background: linear-gradient( 180deg, #FAFAFA 0%, #FFFFFF 100%);
    color: #6F6A67;
    font-size: 14px;
    letter-spacing: 1px;
  }
}
</style>
