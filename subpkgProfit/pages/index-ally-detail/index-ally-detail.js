import { findPartnerDetail, findLevelMap, findChartData } from '@/subpkgProfit/api/indexAllyDetail.js';
import { orgId } from '@/utils/role.js';
import mNav from "@/components/m-nav/m-nav.vue";
import Tips from './components/Tips.vue';
import {formatMoney} from "@/utils/function";

export default {
  data() {
    return {
      allyId: null,
      detail: {},
      levelList: [],

      levelDetail: {},
      nextLevelDetail: {},

      reshow: false,
      chartData: {},
      opts: {
        color: ["#2AA269","#FF3B30","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
        padding: [10,0,0,0],
        enableScroll: false,
        legend: {},
        dataLabel: false,
        xAxis: {
          disableGrid: true,
          axisLine: false,
          fontSize: 10,
          fontColor: "#e2e2e2",
        },
        yAxis: {
          gridColor: "#FFF",
          splitNumber: 3,
          data: [
            {
              axisLine: false,
              min: 0,
              fontSize: 8,
              fontColor: "#e2e2e2",
            }
          ]
        },
        legend: {
          show: false,
        },
        extra: {
          column: {
            type: "group",
            width: 8,
            activeBgColor: "#000000",
            activeBgOpacity: 0.04,
            linearType: "custom",
            seriesGap: 4,
            linearOpacity: 0.5,
            customColor: [
              "#9DEA3B",
              "#F9733E"
            ]
          },
          tooltip: {
            legendShow: true,
            fontSize: 12,
            showArrow: false,
            bgColor: '#fff',
            bgOpacity: 1,
            borderWidth: 1,
            borderColor: '#1980FA', 
            borderRadius: 4,
            lineHeight: 18,
            gridColor: '#DDDDDD',
            fontColor: '#2E2C2B'
          }
        }
      },
      channelList: {},

      showTips: false,
      showTipsType: '',
    };
  },
  computed: {
    orgId
  },
  components: {
    mNav,
    Tips,
  },
  async onLoad(options) {
    if (options.allyId) {
      this.allyId = options.allyId;
    } else {
      this.allyId = this.orgId;
    }
    await this.findLevelMap();
    this.getChartData();
    this.fetchData();
  },
  methods: {
    async fetchData() {
      const { orgId } = this;
      this.$modal.loading('加载中');
      const res = await findPartnerDetail({ allyId: this.allyId });
      this.$modal.closeLoading();
      this.detail = res.data.findPartnerById;
      this.getChannelList();
      this.getLevelDetail();
    },
    async findLevelMap() {
      const res = await findLevelMap();

      this.levelList = res.data.findLevelMap.reverse();
    },
    async getChartData() {
      let response = await findChartData({ allyId: this.allyId });
      let list = response.data.layTerminalTrend;

      let categories = [];
      let activeList = [];
      let inActiveList = [];
      list.forEach(item => {
        categories.push( item.date.slice(5, 7) );
        activeList.push(item.activeTerminalCount);
        inActiveList.push(item.inactiveTerminalCount);
      })

      let res = {
          categories,
          series: [
            {
              name: "装机",
              data: activeList
            },
            {
              name: "撤机",
              data: inActiveList
            }
          ]
        };

      let max = Math.max(...activeList, ...inActiveList);
      max = (Math.floor(max / 3) + 1) * 3;
      
      this.$set(this.opts.yAxis.data[0], 'max', max);
      this.chartData = JSON.parse(JSON.stringify(res));
    },
    toTurnListOver() {
      uni.navigateTo({
        url: `/subpkgProfit/pages/profit-turnover-list/profit-turnover-list?allyId=${this.allyId}`
      })
    },
    toAssetDetail() {
      uni.navigateTo({
        url: `/subpkgAsset/pages/terminal-transfer-detail/terminal-transfer-detail?allyId=${this.allyId}`
      })
    },
    toActivateHistory() {
      uni.navigateTo({
        url: `/subpkgProfit/pages/activate-history/activate-history?allyId=${this.allyId}`
      })
    },
    calc(numerator, denominator) {
      if (!numerator || !denominator) return 0;

      let rate = (numerator / denominator * 100).toFixed(1);

      return rate >= 0.1 ? rate : 0;
    },
    getChannelList() {
      const { detail } = this;
      const { lastAssetRelShiftSlip } = detail;

      if (!lastAssetRelShiftSlip) return {};
      const { snList } = lastAssetRelShiftSlip;
      let channelList = {
        channel6: 0,
        channel8: 0,
        channel12: 0,
      }
      snList.forEach(item => {
        let channelStr = parseInt(item.slice(2,4), 10);
        channelList['channel' + channelStr]++
      })

      this.channelList = channelList;
    },
    getLevelDetail () {
      const { detail, levelList } = this;
      const level = detail.level;
      let levelListRevert = levelList.reverse();

      levelListRevert.forEach((item, key) => {
        if (item.level == level) {
          console.log(key, 'key')
          this.levelDetail = item;
          this.nextLevelDetail = levelListRevert[key + 1];
        }
      })
    },
    handleShowTips(showTipsType) {
      this.showTipsType = showTipsType;
      this.showTips = true;
    },
    closePopup() {
      this.showTips = false;
    },
    toLevelInfo() {
      uni.navigateTo({
        url: `/subpkgProfit/pages/level-info/level-info?allyId=${this.allyId}`
      })
    },
    formatMoney,
  },
}
