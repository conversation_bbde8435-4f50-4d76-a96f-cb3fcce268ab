.index-partner-detail {
  padding-bottom: 80px;
}

.info-icon {
  margin-right: 0;
}

.banner {
  padding-top: 16px;
  padding-left: 24rpx;
  padding-right: 24rpx;
  min-height: 156px;
  background: linear-gradient(to bottom, #3c7bfb 0%, #5bb6f8 50%, rgba(62,170,249,0) 100%);
  box-sizing: border-box;

  .info-row {
    color: #fff;
    font-size: 28rpx;
    line-height: 40rpx;
    .info-row-title {
      color: rgba(255,255,255,0.8);
    }
  }
}

.title-row {
  min-height: 40px;
  margin-bottom: 24rpx;
  border-radius: 8px;
}
.title {
  color: #fff;
  font-size: 36rpx;
  line-height: 50rpx;
  font-weight: bold;
  .ally-icon-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    margin-right: 24rpx;
    background: linear-gradient( 180deg, rgba(255,255,255,0.75) 0%, rgba(255,255,255,0.9) 50%, #FFFFFF 100%);
    box-shadow: inset 0px 0px 0px 1px #FFFFFF;
    border-radius: 8px 8px 8px 8px;
  }
  .ally-icon {
    width: 24px;
    height: 24px;
  }
}
.card-icon {
  flex-shrink: 0;
  width: 48rpx;
  height: 48rpx;
  margin-left: 20rpx;
  background-size: 48rpx 48rpx;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-detail-card-icon.png');
}

.info-box {
  margin: 0 24rpx;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 8px;
  &:nth-last-child(1) {
    margin-top: 12px;
  }
  .info-row {
    margin-bottom: 32rpx;
    &:nth-last-child(1) {
      padding-bottom: 32rpx;
      border-bottom: 1px solid #EBE9E7;
    }
    .info-row-title {
      flex-shrink: 0;
      margin-right: 40rpx;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #6F6A67;
      letter-spacing: 1rpx;
    }
    .info-row-value {
      font-size: 28rpx;
      line-height: 40rpx;
      color: #2E2C2B;
      letter-spacing: 1rpx;
    }
  }
  .info-sub-box {
    .sub-title {
      margin-bottom: 24rpx;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #6F6A67;
      letter-spacing: 1rpx;
    }
    .info-sub-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      padding-bottom: 12px;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #2E2C2B;
      letter-spacing: 1rpx;
      border-bottom: 1px solid #EBE9E7;
      &:nth-last-child(1) {
        margin-bottom: 0;
        border-bottom: none;
        padding-bottom: 0;
      }
      .info-sub-row-title {
        width: 24px;
        height: 24px;
        background-size: 24px 24px;
      }
      .wx-icon {
        background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/wx-icon.png');
      }
      .zfb-icon {
        background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/zfb-icon.png');
      }
      .info-sub-row-right {
        text-align: right;
      }
      .info-sub-row-company {
        margin-top: 4px;
      }
    }
  }
}

.payment-box {
  margin-top: -54px;
}

.table-contain {
  padding-top: 32rpx;
  border-top: 1px solid #EBE9E7;
}

.table-box {
  border: 1px solid #EBE9E7;
  border-radius: 4px;
  .device-table-title-tr {
    color: #6F6A67;
    // border-top: 1px solid #EBE9E7;
    // border-radius: 4px 4px 0 0;
    background-color: #f5f5f5;
  }
  .device-table-tr {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 24rpx;
    border-bottom: 1px solid #EBE9E7;
    &:nth-last-child(1) {
      border-bottom: none;
    }
    .device-table-th {
      color: #6F6A67;
      text-align: center;
      border-right: 1px solid #EBE9E7;
      &:nth-last-child(1) {
        border-right: none;
      }
    }
    .device-table-td {
      text-align: center;
      border-right: 1px solid #EBE9E7;
      &:nth-last-child(1) {
        border-right: none;
      }
    }
    .device-table-th-1 {
      width: 318rpx;
      text-align: center;
    }
    .device-table-th-row {
      padding: 8px 0;
    }
    .device-table-td-1 {
      position: relative;
      line-height: 33px;
      .device-table-td-icon {
        position: absolute;
        left: 16rpx;
        top: 26px;
        font-size: 10px;
        line-height: 14px;
        background: linear-gradient(4.264097479267824e-7deg, #3935FD 0%, #3EAAF9 100%);
        font-weight: bold;
        background-clip: text;
        color: transparent;
        letter-spacing: 1rpx;
      }
      .border-bottom {
        border-bottom: 1px solid #EBE9E7;
      }
    }
    .device-table-th-2 {
      width: 136rpx;
      line-height: 50px;
    }
    .device-table-td-2 {
      line-height: 66px;
    }
    .device-table-th-3 {
      width: 184rpx;
      font-size: 24rpx;
      line-height: 50px;
    }
    .device-table-td-3 {
      line-height: 66px;
    }
  }
  .device-table-row-tr {
    background-color: #fafafa;
    border-bottom: 1px solid #EBE9E7;
    &:nth-last-child(1) {
      border-radius: 0 0 4px 4px;
      border-bottom: none;
      .device-table-tr {
        border-radius: 0 0 4px 4px;
      }
    }
  }
  .device-table-row-tr-active {
    background: linear-gradient( 270deg, rgba(62,170,249,0.06) 0%, rgba(57,53,253,0.06) 100%);
  }

  .device-table-empty {
    text-align: center;
    line-height: 80px;
    color: #999;
    font-size: 24rpx;
    letter-spacing: 1rpx;
  }
}


.ally-detail-content {
  margin: -54px 24rpx 0;

  .level-box {
    padding: 24rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 8px;
    .level-item {
      width: 302rpx;
      padding: 16rpx 24rpx;
      box-sizing: border-box;
      background: linear-gradient( 180deg, rgba(62,170,249,0.08) 0%, rgba(62,170,249,0) 100%);
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #EBE9E7;
      font-size: 28rpx;
      line-height: 40rpx;
      .level-title {
        font-size: 28rpx;
        line-height: 40rpx;
        color: #6F6A67;
      }
      .promotion-row {
        margin: 24rpx 0;
      }
    }
    .next-level-item {
      background: linear-gradient( 180deg, rgba(57,53,253,0.04) 0%, rgba(57,53,253,0) 100%);
      border: 1px dashed #EBE9E7;
      
      .level-title {
        background: linear-gradient(4.269277749501954e-7deg, #3935FD 0%, #3EAAF9 100%);
        background-clip: text;
        color: transparent;
        font-weight: bold;
      }

      .next-level-empty {
        background: linear-gradient(4.269277749501954e-7deg, #3935FD 0%, #3EAAF9 100%);
        background-clip: text;
        color: transparent;
        font-weight: bold;
        line-height: 168rpx;
        text-align: center;
      }
    }
    .leve-arrow-icon {
      flex-shrink: 0;
      width: 32rpx;
      height: 32rpx;
      margin: 0 8rpx;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-level-arrow-icon.png');
      background-size: 32rpx 32rpx;
    }
  }
  .card {
    margin-bottom: 24rpx;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    .card-title {
      margin: 0 32rpx;
      padding: 24rpx 0rpx; 
      border-bottom: 1px solid #EBE9E7;
      color: #2E2C2B;
      font-size: 28rpx;
      font-weight: bold;
      line-height: 40rpx;
    }
    .card-more {
			flex-shrink: 0;
			width: 16rpx;
			height: 28rpx;
			background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-right-arrow.png");
			background-size: 16rpx 28rpx;
		}
    .card-content {
      .card-asset-total {
        margin: 24rpx 32rpx;
        color: #2E2C2B;
        font-size: 28rpx;
        line-height: 40rpx;
        letter-spacing: 1rpx;
        .card-asset-total-title {
          color: #6F6A67;
        }
      }
      .card-asset-footer {
        padding: 24rpx 32rpx;
        background: linear-gradient( 180deg, #FAFAFA 0%, #FFFFFF 100%);
        font-size: 24rpx;
        line-height: 34rpx;
        .asset-list {
          margin-left: 16rpx;
          padding: 4rpx 8rpx;
          border: 1px solid #EBE9E7;
          border-radius: 8rpx;
          .device-item {
            margin-right: 32rpx;
            &:nth-last-of-type(1) {
              margin-right: 0;
            }
          }
          .device-item-title {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
            background-size: 32rpx 32rpx;
          }
          .device-item-title-6 {
            background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/score-history-record-device-6.png');
          }
          .device-item-title-8 {
            background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/score-history-record-device-8.png');
          }
          .device-item-title-12 {
            background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/score-history-record-device-12.png');
          }
          .device-item-value {
            font-size: 24rpx;
            line-height: 34rpx;
            color: #2E2C2B;
          }
        }
      }





      .activate-row,
      .profit-row {
        padding: 24rpx 32rpx;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #2E2C2B;
        .row-title {
          color: #6F6A67;
        }
      }
      .profit-content {
        display: flex;
        padding: 24rpx 0;
        margin: 0 32rpx 24rpx;
        background: #FAFAFA;
        border-radius: 4px 4px 4px 4px;
        .profit-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .profit-item-title {
            margin-bottom: 8rpx;
            color: #6F6A67;
            font-size: 24rpx;
            line-height: 34rpx;
          }
          .profit-item-value {
            font-size: 32rpx;
            line-height: 44rpx;
            color: #2E2C2B;
            font-weight: bold;
          }
        }
        .profit-item:nth-of-type(1) {
          position: relative;
          &::after {
            position: absolute;
            right: 0;
            top: 26rpx;
            display: block;
            content: "";
            width: 2rpx;
            height: 32rpx;
            background-color: #EBE9E7;
          }
        }
      }

      .chart-box {
        height: 90px;
        margin: 0 32rpx 24rpx;
      }

      .online-rate-row {
        padding: 24rpx 32rpx;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #2E2C2B;
        .online-rate-title {
          color: #6F6A67;
        }
      }
    }
  }
}