<template>
  <view class="profit-turnover-wrapper">
    <view class="switch" v-if="false">
      <view class="desc">显示数据标题</view>
      <view>
        <zero-switch v-model="dataTitleVisible" size="16"/>
      </view>
    </view>
    <view class="profit-turnover-list-container" v-if="calList && calList.length">
      <view v-for="(item, i) of calList" :key="i">
        <view class="profit-turnover-list-item">
          <view class="title">
            <view class="title-year-month">
              {{ item.timeYearString }}
            </view>
            <view class="profit-container">
              <view class="profit-container-name" v-if="dataTitleVisible">收益</view>
              <view class="profit-container-money" :class="{'blue-text': i === 0}">{{
                  formatMoney(item.turnover)
                }}
              </view>
            </view>

          </view>
          <view class="content">

            <view class="content-item-wrapper">
              <view class="content-item">
                <view class="content-item-num">{{ '￥' + item.profit }}</view>

                <view class="content-item-title" v-if="dataTitleVisible">利润</view>
              </view>
            </view>

            <view class="content-item-wrapper">
              <view class="content-item">
                <view class="content-item-num">
                  {{ item.turnover <= 0 ? '--' : ((item.earningsYield * 100).toFixed(0) + "%") }}
                </view>
                <view class="content-item-title" v-if="dataTitleVisible">利润率</view>
              </view>
            </view>

            <view class="content-item-wrapper">
              <view class="content-item">
                <view class="content-item-num">
                  {{ item.incomeTerminalCount > 0 ? ('￥' + Number(item.profitPerTm).toFixed(1)) : '--' }}
                </view>
                <view class="content-item-title" v-if="dataTitleVisible">台均</view>
              </view>

            </view>

          </view>
        </view>
        <view class="divider" v-if="i !== calList.length - 1"></view>
      </view>

    </view>

    <view style="color: #B2B0AF; font-size: 14px;text-align: center;margin-top: 24px">已经到底了</view>
  </view>

</template>

<script src="./profit-turnover-list.js"/>


<style lang="scss" scoped>
@import '@/subpkgProfit/pages/profit-turnover-list/profit-turnover-list.scss';
</style>
