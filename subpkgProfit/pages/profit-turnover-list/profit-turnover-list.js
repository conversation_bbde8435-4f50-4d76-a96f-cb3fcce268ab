import {orgId} from "@/utils/role";
import {queryAllyMonthlyIncome, queryAllyMonthlyIncomeTerminal} from "@/subpkgProfit/api/allyMonthly";
import {parseGraphQLList} from "@/utils/graphql";
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";
import {formatMoney} from "@/utils/function";

export default {
    data() {
        return {
            companyId: null,
            list: [],
            allyMonthlyIncomeTerminal: [],
            dataTitleVisible: true,
        };
    },
    computed: {
        orgId,
        calList() {
            return this.list.map(({time, profit, turnover}) => {
                const [year, month] = time.split('-');
                const incomeTerminalCount = this.monthlyIncomeTerminalMap[time] ?? 0;
                profit /= 100
                turnover /= 100
                return {
                    year,
                    month,
                    profit,
                    turnover,
                    earningsYield: profit / turnover,
                    timeYearString: this.isCurrentYearMonth(time) ? '本月' : `${year}年${+month}月`,
                    profitPerTm: turnover / incomeTerminalCount / this.getDaysInMonth(time),
                    incomeTerminalCount
                };
            });
        },
        monthlyIncomeTerminalMap() {
            return this.allyMonthlyIncomeTerminal.reduce((m, {date, assetCount}) => {
                m[date] = assetCount || 0;
                return m;
            }, {});
        }
    },
    watch: {
        dataTitleVisible(val) {
            console.log("show data title: ", val)
        }
    },
    async onLoad(options) {
        this.companyId = options.allyId || this.orgId
        console.log(options.companyId, this.companyId)
        await this.init()
    },
    methods: {
        formatMoney,
        async init() {
            this.list = await this.fetchList()
            this.allyMonthlyIncomeTerminal = await this.fetchAllyMonthlyIncomeTerminal()
        },
        async fetchList() {
            if (!this.companyId) return []
            const res = await queryAllyMonthlyIncome({allyId: Number(this.companyId), first: 100});
            const [list] = parseGraphQLList(res)
            return list
        },
        async fetchAllyMonthlyIncomeTerminal() {
            const res = await queryAllyMonthlyIncomeTerminal({allyId: Number(this.companyId), first: 100});
            const [list] = parseGraphQLList(res)
            return list
        },
        isCurrentYearMonth(inputTime) {
            const time = dayjs()
            const input = dayjs(inputTime)
            return time.year() === input.year() && time.month() === input.month()
        },
        getDaysInMonth(t) {
            return dayjs(t).daysInMonth()
        }
    }
}
