* {
  box-sizing: border-box;
}

.profit-turnover-wrapper {
  padding-bottom: 50px;
  .switch {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 12px;
    .desc {
      font-weight: 400;
      font-size: 12px;
      color: #B2B0AF;
      line-height: 14px;
      text-align: center;
      margin-right: 8px;
    }
  }

  .profit-turnover-list-container {
    min-height: 100%;
    margin: 12px 12px 0;
    padding: 16px;
    border-radius: 8px 8px 8px 8px;
    background-color: #FFFFFF;

    .profit-turnover-list-item {
      overflow: hidden;
      //background-color: #FFFFFF;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #EBE9E7;
      margin-bottom: 12px;

      .title {
        display: flex;
        justify-content: space-between;
        height: 44px;
        border-bottom: 1px solid #EBE9E7;

        .title-year-month {
          display: flex;
          align-items: center;
          font-weight: 500;
          margin-left: 12px;
          font-size: 14px;
          color: #2E2C2B;
        }
      }

      .profit-container {
        display: flex;
        align-items: center;
        margin-right: 12px;

        .profit-container-name {
          font-weight: 400;
          font-size: 14px;
          color: #2E2C2B;
        }

        .profit-container-money {
          font-weight: 500;
          font-size: 14px;
          display: flex;
          align-items: center;
          height: 20px;
          justify-content: center;

          margin-left: 8px;

          &::before {
            content: "￥";
            font-weight: 400;
            font-size: 14px;
            color: #2E2C2B;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

      .content {
        display: flex;
        background: #FAFAFA;
        align-items: center;
        justify-content: space-evenly;
        padding: 8px 0;

        .content-item-wrapper {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;

          .content-item {
            display: flex;
            flex-direction: column;
            justify-content: center;

            .content-item-num {
              text-align: center;
              font-weight: 400;
              font-size: 14px;
              color: #6F6A67;
            }

            .content-item-title {
              font-size: 12px;
              color: #B2B0AF;
              min-width: 90px;
              text-align: center;
              margin-top: 4px;
            }
          }
        }
      }
    }
  }

}


.blue-text {
  background-clip: text;
  color: transparent;
  background: linear-gradient(90deg, #3935FD 0%, #3EAAF9 100%);
  background-clip: text;
  -webkit-background-clip: text;
}

.divider{
  border-bottom: 1px solid #EBE9E7;
  margin-bottom: 12px;
}
