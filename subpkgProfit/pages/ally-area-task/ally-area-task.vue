<template>
  <view class="ally-area-task">
    <view class="my-task" v-if="taskDetail && taskDetail.target">
      <view class="flex-row-between">
        <view class="title">我的任务</view>
        <view class="end-date flex-row">
          <view class="">截止日期：</view>
          <view class="">{{ taskDetail.taskEndDate }}</view>
        </view>
      </view>
      <view class="task-content flex-row">
        <view class="task-num">{{ taskDetail.progress ? taskDetail.progress : 0 }}</view>
        <view class="target-num">/{{ taskDetail.target ? taskDetail.target : 0 }}</view>
      </view>
      <view class="task-footer" v-if="exclusiveOverviewList.length">
        <view class="task-item flex-row-between" v-for="(item, key) in exclusiveOverviewList" :key="key" @click="toDeivceList(item.areaCode)">
          <view class="task-area">{{ item.name }}</view>
          <view class="flex-row">
            <view class="">设备概览</view>
            <view class="more-icon"></view>
          </view>
        </view>
        
      </view>
    </view>
    <view class="my-task task-empty" v-else>
      <view class="task-empty-title" v-if="false">我的任务</view>
      <view class="task-empty-content">暂未签约</view>
      <view class="task-empty-tips">如有意向，请联系悟空商务沟通签约事宣</view>
    </view>
    <view class="prev-box">
      <view class="prev-btn" @click="toParent" v-if="parentCode.length > 1">上一级</view>
    </view>
    <view class="chart-box">
      <qiun-data-charts type="map" canvas2d="" :chartData="chartData" :opts="opts" :inScrollView="true" :pageScrollTop="pageScrollTop" @getIndex="getIndex" :tooltipShow="false" />
    </view>
    <view class="legend-box flex-row-between">
      <view class="legend-item flex-column">
        <view class="legend-item-block legend-item-block-forbid"></view>
        <view class="legend-item-title">不可签约</view>
      </view>
      <view class="legend-item flex-column">
        <view class="legend-item-block legend-item-block-able"></view>
        <view class="legend-item-title">未签约</view>
      </view>
      <view class="legend-item flex-column">
        <view class="legend-item-block legend-item-block-disabled"></view>
        <view class="legend-item-title">已被签约</view>
      </view>
      <view class="legend-item flex-column">
        <view class="legend-item-block legend-item-block-own"></view>
        <view class="legend-item-title">被我签约</view>
      </view>
    </view>
    <view class="select-box flex-row-between">
      <view class="select-china" @click="toChina">全国</view>
      <view class="sep-color">/</view>
      <view class="select-province">
        <luyj-select-lay optionsDirection="top" :value="provinceCode" name="provinceCode" placeholder="请选择" :options="provinceList" slabel="text" @selectitem="changeProvince">
        </luyj-select-lay>
      </view>
      <view class="sep-color">/</view>
      <view class="select-city">
        <luyj-select-lay optionsDirection="top" :value="cityCode" name="cityCode" placeholder="请选择" :options="cityList" slabel="text" @selectitem="changeCity">
        </luyj-select-lay>
      </view>
      <view class="sep-color">/</view>
      <view class="select-city">
        <luyj-select-lay optionsDirection="top" :value="districtCode" name="cityCode" placeholder="请选择" :options="districtList" slabel="text" @selectitem="changeDistrict">
        </luyj-select-lay>
      </view>
    </view>
    <view class="sign-data flex-row-between" v-if="parentCode.length > 1">
      <view class="sign-data-type">
        <view class="sign-type-1" v-if="!signedOrgId">{{districtName1 ? districtName1 : '未签约'}}</view>
        <view class="sign-type-2" v-else-if="signedOrgId == orgId">被我签约</view>
        <view class="sign-type-3" v-else-if="signedOrgId != orgId">已被签约</view>
      </view>
      <view class="sign-target" v-if="!signedOrgId">月目标：{{ planTarget }}</view>
      <view class="sign-company" v-else>{{ signedCompany }}</view>
    </view>
  </view>
</template>

<script src="./ally-area-task.js">

</script>

<style>
page {
  background-color: #000000;
  background: linear-gradient( 180deg, rgba(25,128,250,0.2) 0%, rgba(25,128,250,0.04) 100%), #000000;
}
</style>

<style lang="scss" scoped>
@import './ally-area-task.scss'
</style>
