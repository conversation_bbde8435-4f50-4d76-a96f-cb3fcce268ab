import { findAreaList, findTaskDetail, findOrgAdcode } from "@/subpkgProfit/api/allyAreaTask";
import { orgId } from "@/utils/role";
import {toast} from "@/utils/common";

export default {
  data() {
    return {
      loadingMap: false,
      taskDetail: {},
      opts: {
        padding: [0, 0, 0, 0],
        dataLabel: true,
        enableScroll: false,
        fontSize: 8,
        fontColor: '#fff',
        extra: {
          map: {
            border: true,
            mercator: true, // 是否进行WGS84转墨卡托投影(开启后可能会造成tooltip不跟手，建议自行转换)
            borderWidth: .5,
            borderColor: '#444444',
            fillOpacity: 1,
            activeBorderColor: '#fff',
            activeFillColor: '#00BCFB',
            activeFillOpacity: 1
          }
        }
      },
      parentCode: [100000],
      // 地图数据
      chartData: {}, // 地图数据 目前只支持geojson格式
      pageScrollTop: 0, // 页面时候必须传入pageScrollTop，inScrollView传true，否则会导致地图某些事件不触发
      currentOrgExclusiveAreaList: [],
      exclusiveAreaList: [],
      notOptionAreaList: [],
      myExclusive: false,

      exclusiveOverviewList: [],  // 任务列表

      signedOrgId: '',
      signedCompany: '',
      planTarget: '',

      provinceCode: '',
      provinceList: [],
      cityCode: '',
      cityList: [],
      districtCode: '',
      districtName: '',
      districtList: [],
    };
  },
  computed: {
    orgId,
    districtName1() {
      const { cityCode, provinceCode, provinceList, cityList } = this;
      if (this.districtName) return this.districtName;

      let list;
      if (cityCode) {
        list = cityList.filter(item => item.value == cityCode);
        if (list.length) return list[0].text;
        return ''
      }

      if (provinceCode) {
        list = provinceList.filter(item => item.value == provinceCode);
        if (list.length) return list[0].text;
        return ''
      }
      return ''
    }
  },
  onLoad() {
    this.fetchInitProvince();
    this.fetchMyTask();
  },
  onPageScroll(e) {
    this.pageScrollTop = e.scrollTop
  },
  methods: {
    async fetchMyTask() {
      let res = await findTaskDetail(this.orgId);
      this.taskDetail = res.data.exclusiveAreaInfo;

      this.exclusiveOverviewList = res.data.exclusiveAreaInfo.exclusiveOverviewList;
    },
    async fetchAreaList(adCode) {
      const res = await findAreaList({ parentCode: adCode, orgId: this.orgId });
      const exclusiveOverview = res.data.areaExclusiveOverview;

      this.exclusiveAreaList = exclusiveOverview.exclusiveAreaList;
      this.currentOrgExclusiveAreaList = exclusiveOverview.currentOrgExclusiveAreaList;
      this.notOptionAreaList = exclusiveOverview.notOptionAreaList;
      this.myExclusive = exclusiveOverview.myExclusive;

      this.signedOrgId = exclusiveOverview.signedOrgId ? exclusiveOverview.signedOrgId : null;
      this.signedCompany = exclusiveOverview.signedOrgDetail ? exclusiveOverview.signedOrgDetail.companyFullName : '';
      this.planTarget = exclusiveOverview.planTarget;
    },
    async drawChina(notDrawChina) {
      await this.fetchAreaList(this.addAdcodeZero());
      const response = await uni.request({
        method: 'get',
        timeout: 10000,
        url: `https://oss-wukong-web.oss-cn-shenzhen.aliyuncs.com/erptest/bigScreen/map-geojson/100000.json`,
      })

      const [error, res] = response;
      let series = res.data.features;
      // 这里循环一下series，把需要的数据增加到serie的属性中，fillOpacity是根据数据来显示的颜色层级透明度

      series = this.drawAreaSeries(series);
      this.getProvinceAndCityList(series);

      if (!notDrawChina) {
        this.chartData = {
          series: series
        };
      }
    },
    async fetchMapData(adCode) {
      if (!adCode) adCode = '100000';
      this.$modal.loading('加载中');
      try {
        await this.fetchAreaList(this.addAdcodeZero(adCode));
      } catch(e) {
        console.log('报错了')
      }
      
      this.$modal.closeLoading();
      const This = this;

      let fileName = '';
      if (this.parentCode.length > 2) {
        fileName = `${this.parentCode[1]}/${adCode}.geoJson`;
      } else {
        fileName = `${adCode}.json`;
        this.cityCode = '';
        this.cityList = [];
        this.districtCode = '';
        this.districtName = '';
        this.districtList = [];
      }
      uni.request({
        method: 'get',
        timeout: 10000,
        url: `https://es.wukongcd.com/erptest/bigScreen/map-geojson/${fileName}`,
      }).then(response => {
        const [error, res] = response;

        let series = res.data.features;
        // 这里循环一下series，把需要的数据增加到serie的属性中，fillOpacity是根据数据来显示的颜色层级透明度
        series = This.drawAreaSeries(series);
        This.getProvinceAndCityList(series);
        This.drawCharts(series);
        setTimeout(() => {
          This.loadingMap = false;
        }, 1000)
        
      })
    },
    async fetchInitProvince() {
      await this.drawChina(true);
      let res = await findOrgAdcode(this.orgId);

      if (res.data.findOrgDetailById.companyLocation) {
        const adcode = res.data.findOrgDetailById.companyLocation.provinceCode;
        this.provinceCode = parseInt((adcode + '').slice(0, 6), 10);
        this.parentCode = [100000, this.provinceCode];
        this.fetchMapData(this.provinceCode);
      } else {
        this.toChina();
      }
    },
    getProvinceAndCityList(series) {
      const { parentCode, notOptionAreaList } = this;

      if (parentCode.length == 1) {
        // 添加省
        this.provinceList = [];
        series.forEach(item => {
          if (item.properties.name) {
            const province = {};
            province.text = item.properties.name;
            province.value = item.properties.adcode;
            province.disable = notOptionAreaList.indexOf(+this.addAdcodeZero(province.value)) > -1;
            this.provinceList.push(province);
          }
        })
      } else if (parentCode.length == 2) {
        // 添加市
        this.cityList = [];
        series.forEach(item => {
          const city = {};
          city.text = item.properties.name;
          city.value = item.properties.adcode;
          city.disable = notOptionAreaList.indexOf(+this.addAdcodeZero(city.value)) > -1;
          this.cityList.push(city);
        })
      } else if (parentCode.length == 3) {
        // 添加县
        this.districtList = [];
        series.forEach(item => {
          const district = {};
          district.text = item.properties.name;
          district.value = item.properties.adcode;
          this.districtList.push(district);
        })
      }
    },
    changeProvince(index, province) {
      this.provinceCode = province.value;
      this.parentCode = [100000, this.provinceCode];
      this.cityCode = '';
      this.cityList = [];
      this.districtCode = '';
      this.districtName = '';
      this.districtList = [];

      this.fetchMapData(this.provinceCode);
    },
    changeCity(index, city) {
      this.cityCode = city.value;
      this.parentCode = [100000, this.provinceCode, this.cityCode];
      this.districtCode = '';
      this.districtName = '';
      this.districtList = [];

      this.fetchMapData(this.cityCode);
    },
    async changeDistrict(index, district) {
      this.districtCode = district.value;
      this.districtName = district.text;
      this.$modal.loading('加载中');
      const res = await findAreaList({ parentCode: this.districtCode + '000000', orgId: this.orgId });
      this.$modal.closeLoading();
      const exclusiveOverview = res.data.areaExclusiveOverview;

      this.signedOrgId = exclusiveOverview.signedOrgId ? exclusiveOverview.signedOrgId : null;
      this.signedCompany = exclusiveOverview.signedOrgDetail ? exclusiveOverview.signedOrgDetail.companyFullName : '';
      this.planTarget = exclusiveOverview.planTarget;
    },
    drawCharts(series) {
      this.chartData = { series: series };
    },
    drawAreaSeries(series) {
      const { currentOrgExclusiveAreaList, exclusiveAreaList, notOptionAreaList } = this;

      series.forEach((item, key) => {
        series[key].value = ''
        series[key].fillOpacity = 1;
        const adcode = item.properties.adcode;

        // 本组织签约
        if (currentOrgExclusiveAreaList && currentOrgExclusiveAreaList.indexOf(+this.addAdcodeZero(adcode)) > -1) {
          series[key].color = "#1980FA"
        } else if (exclusiveAreaList && exclusiveAreaList.indexOf(+this.addAdcodeZero(adcode)) > -1) {
          // 已签约
          series[key].color = "#6330F5"
        } else if (notOptionAreaList && notOptionAreaList.indexOf(+this.addAdcodeZero(adcode)) > -1) {
          // 不可签约
          series[key].color = "#2F3133"
        } else {
          series[key].color = "#000000";
        }
      })

      return series;
    },
    // 点击地图获取点击的索引
    getIndex(e) {
      console.log('点击地图', e);
      if (this.loadingMap) return;
      
      const { currentIndex, opts } = e;
      const { series } = opts;

      const selectArea = series[currentIndex];
      const selectAreaCode = selectArea.properties.adcode;

      if (this.parentCode.length >= 3) {
        // toast('暂无下级数据')
        this.changeDistrict(0, this.districtList[currentIndex]);
        return;
      }

      if (this.notOptionAreaList.indexOf(+this.addAdcodeZero(selectAreaCode)) > -1) {
        uni.showModal({
					content: '当前区域不可签约哦~',
					showCancel: false
				});
        return;
      }

      this.loadingMap = true;
      if (this.parentCode.length == 2) {
        this.cityCode = selectAreaCode;
      } else if (this.parentCode.length == 1) {
        this.provinceCode = selectAreaCode;
      }

      const adCode = this.getAdcode({adCode: selectArea.properties.adcode, type: 'next'});

      this.fetchMapData(adCode);
    },
    getAdcode({adCode, type}) {
      if (type == 'next') {
        this.parentCode.push(adCode);
        return adCode;
      } else if (type == 'prev') {
        this.parentCode.pop();
        return this.parentCode[this.parentCode.length - 1];
      }
    },
    addAdcodeZero(adCode) {
      if (!adCode) return '100000000000'

      return adCode + '000000';
    },
    toChina() {
      this.parentCode = [100000];
      this.provinceCode = '';
      this.cityCode = '';
      this.cityList = [];
      this.drawChina();
    },
    toParent() {
      const adCode = this.getAdcode({adCode: '', type: 'prev'});

      if (this.parentCode.length == 2) {
        this.cityCode = '';
      } else if (this.parentCode.length == 1) {
        this.provinceCode = '';
      }
      this.fetchMapData(adCode);
    },
    toDeivceList(parentCode) {
      uni.navigateTo({
        url: `/subpkgProfit/pages/ally-area-device/ally-area-device?parentCode=${parentCode}`
      })
    },
  },
}
