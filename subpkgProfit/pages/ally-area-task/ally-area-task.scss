::v-deep .uni-select {

}
::v-deep .uni-select__input-box {
	height:72rpx !important;
}
::v-deep .uni-select__input-text {
	font-size: 28rpx !important;
  line-height: 40rpx !important;
  color: #fff !important;
}
::v-deep .uni-select-lay-item {
	text-align: center !important;
  color: #333 !important;
}
::v-deep .uni-select-lay .uni-select-lay-select .uni-select-lay-input.active {
  color: #fff !important;
}
::v-deep .uni-select-lay .uni-select-lay-select .uni-select-lay-icon::before {
  display: none !important;
}
::v-deep .uni-select-lay .uni-select-lay-select .uni-select-lay-input {
  color: #fff !important;
}

::v-deep .uni-select__selector-empty {
  color: #333 !important;
}

::v-deep .uni-select-lay-select {
  border: 1px solid rgba(255,255,255,0.16) !important;
}

.flex-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ally-area-task {
  padding-top: 16px;
  padding-bottom: 30px;
  background-color: #000000;
  background: linear-gradient( 180deg, rgba(25,128,250,0.2) 0%, rgba(25,128,250,0.04) 100%), #000000;
}

.my-task {
  margin: 0px 24rpx 0;
  padding: 32rpx;
  background: rgba(255,255,255,0.04);
  box-shadow: inset 0px 0px 0px 1px rgba(255,255,255,0.16), 0px 2px 4px 0px rgba(0,0,0,0.25);
  border-radius: 8px 8px 8px 8px;
  .title {
    color: #fff;
    font-size: 28rpx;
    line-height: 40rpx;
    font-weight: bold;
  }
  .end-date {
    color: rgba(255,255,255,0.5);
    font-size: 28rpx;
    line-height: 40rpx;
  }
  .task-content {
    justify-content: center;
    align-items: flex-end;
    margin: 24rpx 0;
    .task-num {
      font-size: 64rpx;
      line-height: 84rpx;
      color: #fff;
      font-weight: bold;
    }
    .target-num {
      font-size: 48rpx;
      font-weight: bold;
      line-height: 64rpx;
      color: rgba(255,255,255,0.25);
    }
  }
  .task-footer {
    color: rgba(255,255,255,0.5);
    font-size: 24rpx;
    line-height: 34rpx;
    .more-icon {
      &::before {
        font-family: "Wukong Font", serif;;
        content: '\e934';
        font-size: 28rpx;
        line-height: 40rpx;
        color: #B2B0AF;
      }
    }
  }

  .task-item {
    padding: 24rpx 0;
    border-top: 1px solid rgba(255,255,255,0.1);
    &:nth-last-child(1) {
      padding-bottom: 0;
    }
  }
}

.task-empty {
  text-align: center;
  color: #fff;
}
.task-empty-title {
  font-size: 28rpx;
  line-height: 40rpx;
  font-weight: bold;
}
.task-empty-content {
  margin: 24rpx 0;
  font-size: 44rpx;
  line-height: 66rpx;
  font-weight: bold;
}
.task-empty-tips {
  padding-top: 24rpx;
  font-size: 24rpx;
  line-height: 40rpx;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.prev-box {
  height: 56rpx;
}
.prev-btn {
  position: relative;
  width: 140rpx;
  height: 56rpx;
  margin-left: 24rpx;
  margin-top: 24rpx;
  color: #fff;
  border: 1px solid #fff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background: rgba(25,128,250,0.1);
  box-shadow: inset 0px 0px 0px 1px rgba(255,255,255,0.1);
  border-radius: 8px 8px 8px 8px;
  text-align: center;
  font-size: 28rpx;
  line-height: 40rpx;
  z-index: 2;
  box-sizing: border-box;
}

.chart-box {
  width: 90vw;
  height: 630rpx;
  margin: 0 auto 24rpx;
}

.legend-box {
  margin: 0 24rpx;
  .legend-item {
    width: 163rpx;
    font-size: 24rpx;
    color: rgba(255,255,255,0.5);
    .legend-item-block {
      width: 100%;
      height: 16rpx;
      border-radius: 8rpx;
      box-shadow: inset 0px 0px 0px 1px #444444;
    }
    .legend-item-block-forbid {
      background: #2F3133;
    }
    .legend-item-block-able {
      background: #000;
    }
    .legend-item-block-disabled {
      background: #6330F5;
    }
    .legend-item-block-own {
      background: #1980FA;
    }
    .legend-item-title {
      margin-top: 8rpx;
    }
  }
}

.select-box {
  margin: 24rpx 24rpx 0;
  color: #fff;
  font-size: 28rpx;
  line-height: 40rpx;

  .select-china {
    text-decoration: underline;
  }
  .sep-color {
    color: #B2B0AF;
  }
  .select-province,
  .select-city,
  .select-district {
    width: 170rpx;
  }
}

.sign-data {
  padding: 32rpx;
  margin: 24rpx 24rpx 0;
  background: rgba(255,255,255,0.04);
  box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.5), inset 0px 0px 0px 1px rgba(255,255,255,0.16);
  border-radius: 8px 8px 8px 8px;
  .sign-data-type {
    font-size: 32rpx;
    line-height: 44rpx;
    font-weight: bold;
  }
  .sign-type-1 {
    color: #fff;
  }
  .sign-type-2 {
    background: linear-gradient(4.2625615022220847e-7deg, #3EAAF9 0%, #3935FD 100%);
    background-clip: text;
    color: transparent;
  }
  .sign-type-3 {
    background: linear-gradient(180deg, #6330F5 0%, #9B5EFF 100%);
    background-clip: text;
    color: transparent;
  }
  .sign-company {
    flex-shrink: 0;
    font-size: 28rpx;
    line-height: 40rpx;
    color: rgba(255,255,255,0.5);
  }
  .sign-target {
    flex-shrink: 0;
    color: #fff;
    font-size: 28rpx;
    line-height: 40rpx;
  }
}