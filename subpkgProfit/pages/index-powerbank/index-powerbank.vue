<template>
	<view class="index-powerbank">
		<mNav
			title="宝管理"
			titleColor="#fff"
			topBarColor="linear-gradient(to bottom, #2ba368 0%, #49b55d 100%);"
      statusBarColor="linear-gradient(to bottom, #49b55d 0%, #64c652 100%);"
		/>
		<view class="banner-bg"></view>
		<view class="banner-box">
			<view class="banner-content">
				<view class="banner-item" @click="toPowerbankEquity">
					<view class="banner-item-title">宝权益</view>
					<view class="banner-item-value flex-row">
            <view class="">{{ countPowerbankEquity.countEquity ? countPowerbankEquity.countEquity : 0 }}</view>
            <view class="more-icon"></view>
          </view>
				</view>
				<view class="banner-item">
					<view class="banner-item-title flex-row" @click="handleShowTips('countInboundInfo')">
            <view>总{{ countInbound < 0 ? "流出" : "流入" }}</view>
            <view class="popup-info-icon"></view>
          </view>
					<view class="banner-item-value" :class="[countInbound < 0 ? 'danger-color' : 'success-color']">{{ countInbound }}</view>
				</view>
			</view>
      <view class="pay-order-list">
        <view class="pay-order-item flex-row" v-for="(item, key) in payOrderList" :key="item.id">
          <view class="pay-order-title flex-row">
            <view class="pay-order-icon"></view>
            <view class="pay-order-title-text">多宝购买单</view>
          </view>
          <view class="pay-order-content">
            <view class="pay-order-content-title">总金额</view>
            <view class="flex-row">
              <view class="pay-order-money">¥{{ item.totalAmount / 100 }}</view>
              <view class="pay-order-num" v-if="item.slip && item.slip.remark">({{ item.slip.remark }})</view>
            </view>
          </view>
          <view class="pay-order-time">
            <view class="pay-order-time-title">已付款</view>
            <view class="pay-order-time-text">¥{{ item.currentAmount / 100 }}</view>
          </view>
        </view>
      </view>
		</view>
		<view class="powerbank-detail">
			<view class="powerbank-status">
				<view class="powerbank-status-title flex-row" @click="toPowerbankManagePage(2)">
					<view class="powerbank-status-title-text">运行宝</view>
					<view class="powerbank-status-title-value">{{ countPowerbank.countWorking + countPowerbank.countRented ? countPowerbank.countWorking + countPowerbank.countRented : 0 }}</view>
					<view class="powerbank-status-title-icon"></view>
				</view>
				<view class="powerbank-status-content powerbank-operating-content flex-row-between">
          <view class="progress-block flex-row-between" :class="{'progress-red-block': calcWorkingChannelRatio < 60}" @click="handleShowTips('operationInfo')">
            <view class="progress-content" :class="{'progress-content-green': calcWorkingChannelRatio >= 60, 'progress-content-red': calcWorkingChannelRatio < 60 }" :style="{ '--bg-width': calcWorkingChannelRatio ? calcWorkingChannelRatio + '%' : 0 }" ></view>
            <view class="flex-row">
              <view class="progress-title">运行宝仓比</view>
              <view class="popup-info-icon"></view>
            </view>
            <view class="progress-value">{{ calcWorkingChannelRatio }}%</view>
          </view>
          <view class="progress-block flex-row-between" :class="{'progress-red-block': calcAllChannelRatio < 60}" @click="handleShowTips('ratioInfo')">
            <view class="progress-content" :class="{'progress-content-green': calcAllChannelRatio >= 60, 'progress-content-red': calcAllChannelRatio < 60 }" :style="{ '--bg-width': calcAllChannelRatio ? calcAllChannelRatio + '%' : 0}"></view>
            <view class="flex-row">
              <view class="progress-title">宝仓比</view>
              <view class="popup-info-icon"></view>
            </view>
            <view class="progress-value">{{ calcAllChannelRatio }}%</view>
          </view>
        </view>
			</view>
      <view class="powerbank-status">
        <view class="powerbank-status-title flex-row" @click="toPowerbankManagePage(2)">
          <view class="powerbank-status-title-text">库存宝</view>
          <view class="powerbank-status-title-value">{{ countPowerbank.countStockCompany + countPowerbank.countStockPersonal ? countPowerbank.countStockCompany + countPowerbank.countStockPersonal : 0 }}</view>
          <view class="powerbank-status-title-icon"></view>
        </view>
        <view class="powerbank-status-content powerbank-stock-content" :class="[isAlly ? 'flex-row-between' : 'flex-row', !isAlly ? 'powerbank-stock-agent' : '']">
          <view class="powerbank-stock-item flex-column-center">
            <view class="powerbank-stock-item-title">公司库存</view>
            <view class="powerbank-stock-item-value">
              <text>{{ allySelfCountPowerbank.countStockCompany }}</text>
            </view>
          </view>
          <view class="powerbank-stock-item flex-column-center">
            <view class="powerbank-stock-item-title">个人库存</view>
            <view class="powerbank-stock-item-value">
              <text>{{ allySelfCountPowerbank.countStockPersonal }}</text>
            </view>
          </view>
          <view class="powerbank-stock-item flex-column-center" v-if="isAlly">
            <view class="powerbank-stock-item-title">代理库存</view>
            <view class="powerbank-stock-item-value">
              <text>{{ agentCountPowerbank.countStockCompany + agentCountPowerbank.countStockPersonal ? agentCountPowerbank.countStockCompany + agentCountPowerbank.countStockPersonal : 0 }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="powerbank-status">
        <view class="flex-row-between" @click="toPowerbankMissPage(1)">
          <view class="powerbank-status-title powerbank-miss-title flex-row">
            <view class="powerbank-status-title-text">失踪宝</view>
            <view class="powerbank-status-title-value">{{ calcMissNumber }}</view>
            <view class="powerbank-status-title-icon"></view>
          </view>
          <view class="today-miss flex-row" v-if="countTodayPowerbank && countTodayPowerbank.countMiss">
            <view class="">今日失踪：</view>
            <view class="">{{ countTodayPowerbank.countMiss }}</view>
          </view>
        </view>
        <view class="powerbank-status-content powerbank-miss-content flex-row">
          <view class="flex-column-center miss-item" @click="toPowerbankMissPage(1)">
            <view class="miss-item-title">运行失踪</view>
            <view class="miss-item-value flex-row">
              <view class="">{{ countPowerbank.countMissAtStore ? countPowerbank.countMissAtStore : 0 }}</view>
              <view class="more-icon"></view>
            </view>
          </view>
          <view class="flex-column-center miss-item" @click="toPowerbankMissPage(2)">
            <view class="miss-item-title">库存失踪</view>
            <view class="miss-item-value flex-row">
              <view class="">{{ countPowerbank.countAll ? countPowerbank.countMissAtCompanyHub + countPowerbank.countMissAtPersonalHub : 0 }}</view>
              <view class="more-icon"></view>
            </view>
          </view>
          <view class="flex-column-center miss-item" @click="toPowerbankMissPage(3)">
            <view class="miss-item-title">人工失踪</view>
            <view class="miss-item-value flex-row">
              <view class="">{{ countPowerbank.countLostByEndOrder + countPowerbank.countLostByRefund ? countPowerbank.countLostByEndOrder + countPowerbank.countLostByRefund : 0 }}</view>
              <view class="more-icon"></view>
            </view>
          </view>
        </view>
      </view>
      <view class="powerbank-status">
        <view class="powerbank-status-title flex-row">
          <view class="powerbank-status-title-text">其他</view>
          <view class="powerbank-status-title-value">{{ countPowerbank.countSold + countPowerbank.countStockRepair ? countPowerbank.countSold + countPowerbank.countStockRepair : 0 }}</view>
        </view>
        <view class="powerbank-status-content powerbank-miss-content powerbank-miss-sub-content flex-row">
          <view class="flex-column-center miss-item" @click="toPowerbankManagePage(2)">
            <view class="miss-item-title">已售出</view>
            <view class="miss-item-value flex-row">
              <view class="">{{ countPowerbank.countSold }}</view>
              <view class="more-icon"></view>
            </view>
          </view>
          <view class="flex-column-center miss-item" @click="toPowerbankManagePage(2)">
            <view class="miss-item-title">返修中</view>
            <view class="miss-item-value flex-row">
              <view class="">{{ countPowerbank.countStockRepair }}</view>
              <view class="more-icon"></view>
            </view>
          </view>
          <view class="flex-column-center miss-item">

          </view>
        </view>
      </view>
    </view>
    <view class="powerbank-function flex-row">
      <view class="powerbank-function-item" @click="toTransfer(1)">
        <view class="powerbank-function-icon powerbank-function-icon1"></view>
        <view class="powerbank-function-text">库存宝转让</view>
      </view>
      <view class="powerbank-function-item" @click="toTransfer(2)">
        <view class="powerbank-function-icon powerbank-function-icon2"></view>
        <view class="powerbank-function-text">在柜宝过户</view>
      </view>
      <view class="powerbank-function-item" @click="toTransfer(3)">
        <view class="powerbank-function-icon powerbank-function-icon3"></view>
        <view class="powerbank-function-text">变更保管人</view>
      </view>
    </view>
    <Tips :showTips="showTips" :showTipsType="showTipsType" :countPowerbank="countPowerbank" @closePopup="closePopup" />
    <mTabbar v-if="isShowTabbar"/>
	</view>
</template>

<script src="./index-powerbank.js">

</script>

<style lang="scss" scoped>
@import './index-powerbank.scss';
</style>
