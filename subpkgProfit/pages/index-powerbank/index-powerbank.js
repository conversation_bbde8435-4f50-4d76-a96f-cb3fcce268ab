import mNav from "@/components/m-nav/m-nav.vue";
import mTabbar from "@/components/m-tabbar/m-tabbar.vue";
import { findAllyDetail, findPayOrderList, findAllySelfPowerbankDetail, findAgentDetail, findAgentPowerbankDetail } from "@/subpkgProfit/api/indexPowerbank";
import { orgId, isAlly } from "@/utils/role";
import { flatNodeList } from "@/utils/function";
import Tips from './components/Tips';
import { findAgentSelfPowerbankDetail } from "../../api/indexPowerbank";

export default {
  data() {
    return {
      showTips: false,
      showTipsType: '',

      isShowTabbar: false,
      payOrderList: [],
      countPowerbankEquity: {}, // 默认范围的权益数
      countPowerbank: {},
      countTerminal: {},
      allySelfCountPowerbank: {},
      agentCountPowerbank: {},
    };
  },
  onLoad() {
    uni.hideTabBar();
    setTimeout(() => {
        this.isShowTabbar = true;
    }, 100)

    this.fetchLastPayRecord();
    this.fetchData();
  },
  async onPullDownRefresh() {
    let res = await this.fetchData();
    uni.stopPullDownRefresh();
  },
  components: {
    mNav,
    mTabbar,
    Tips,
  },
  computed: {
    orgId,
    countInbound() {
      const { countPowerbank, countPowerbankEquity } = this;

      if (!countPowerbank.countAll && !countPowerbankEquity.countEquity) return 0;

      return countPowerbank.countAll - countPowerbankEquity.countEquity;
    },
    calcWorkingChannelRatio() {
      const { countPowerbank, countTerminal } = this;

      if (!(countPowerbank.countWorking + countPowerbank.countRented) || !countTerminal.sumWorkingChannel) return 0;

      return this.calRatio(countPowerbank.countWorking + countPowerbank.countRented, countTerminal.sumWorkingChannel);
    },
    calcAllChannelRatio() {
      const { countPowerbank, countTerminal } = this;
      if (!(countPowerbank.countWorking + countPowerbank.countRented+ countPowerbank.countStockCompany + countPowerbank.countStockPersonal) || !countTerminal.sumChannel) return 0;

      return this.calRatio((countPowerbank.countWorking + countPowerbank.countRented + countPowerbank.countStockCompany + countPowerbank.countStockPersonal), countTerminal.sumChannel)
    },
    calcMissNumber() {
      const { countPowerbank } = this;

      if (!((countPowerbank.countMissAtCompanyHub + countPowerbank.countMissAtPersonalHub + countPowerbank.countMissAtStore) + countPowerbank.countLostByEndOrder + countPowerbank.countLostByRefund)) return '0'

      return (countPowerbank.countMissAtCompanyHub + countPowerbank.countMissAtPersonalHub + countPowerbank.countMissAtStore) + countPowerbank.countLostByEndOrder + countPowerbank.countLostByRefund;
    },
    isAlly,
  },
  methods: {
    async fetchLastPayRecord() {
      let res = await findPayOrderList({ allyId: this.orgId });
      const deductionInfo = res.data.getDeductionInfo;

      this.payOrderList = deductionInfo && deductionInfo.stage != 'COMPLETED' ? [res.data.getDeductionInfo] : [];
    },
    async fetchData() {
      console.log(111)
      const findAllyDetailApi = this.isAlly ? findAllyDetail : findAgentDetail;
      const findAllySelfPowerbankDetailApi = this.isAlly ? findAllySelfPowerbankDetail : findAgentSelfPowerbankDetail;

      let res = findAllyDetailApi({ allyId: this.orgId }).then(res => {
        this.countPowerbank = res.data.findPartnerById.allyAndAgent.countPowerbank;
        this.countPowerbankEquity = res.data.findPartnerById.allyAndAgent.countPowerbankEquity;
        this.countTerminal = res.data.findPartnerById.allyAndAgent.countTerminal;
      })
      
      if (this.isAlly) {
        findAgentPowerbankDetail({ allyId: this.orgId }).then(res => {
          this.agentCountPowerbank = res.data.findPartnerById.agent.countPowerbank;
        })
      }

      findAllySelfPowerbankDetailApi({ allyId: this.orgId }).then(res => {
        this.allySelfCountPowerbank = res.data.findPartnerById.allySelf.countPowerbank;
      })
    },
    async fetchAgentData() {
      
    },
    toPowerbankManagePage(tab) {
      uni.navigateTo({
        url: `/pages/powerbank-manage/powerbank-manage?tab=${tab}`,
      })
    },
    toPowerbankEquity() {
      uni.navigateTo({
        url: `/subpkgDevice/pages/powerbank-equity/powerbank-equity`,
      })
    },
    toPowerbankMissPage(tab) {
      uni.navigateTo({
        url: `/subpkgProfit/pages/miss-management/asset-miss-stat/asset-miss-stat?tab=${tab}`
      })
    },
    toTransfer(transferType) {
      uni.navigateTo({
        url: `/subpkgDevice/pages/powerbank-equity-transfer/powerbank-equity-transfer?transferType=${transferType}`
      })
    },
    calRatio(state, stateAll) {
			if (!stateAll) return '0'
			if (!state) {
				return "0";
			}
			return (
				((state / stateAll) * 100).toFixed(1)
			);
		},
    handleShowTips(showTipsType) {
      this.showTipsType = showTipsType;
      this.showTips = true;
    },
    closePopup() {
        this.showTips = false;
    },
  }
}
