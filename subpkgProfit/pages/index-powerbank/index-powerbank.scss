.index-powerbank {
  // padding-bottom: 90px;
}

.banner-bg {
  height: 236px;
  background: linear-gradient(to bottom, #64c652 0%, #b5ed6e 50%, #f5f5f5 100%);
  z-index: -1;
}

.popup-info-icon {
  margin-left: 4rpx;
  &::before {
    font-family: "Wukong Font", serif;
    display: block;
    content: "\e904";
    font-size: 32rpx;
    color: #6F6A67;
    font-weight: normal;
  }
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.banner-box {
  margin: -220px 24rpx 0;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  .banner-content {
    display: flex;
    align-items: center;
    height: 184rpx;
    box-shadow: inset 0px 0px 0px 1px #FFFFFF;
    background: linear-gradient(to bottom, #bbe8a8 0%, #f0fbe4 50%, #fff 100%);
    border-radius: 8px 8px 8px 8px;
    .banner-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .banner-item-title {
        margin-bottom: 16rpx;
        font-size: 28rpx;
        line-height: 40rpx;
        font-weight: bold;
        color: #2E2C2B;
        letter-spacing: 1rpx;
      }
      .banner-item-value {
        font-size: 48rpx;
        line-height: 64rpx;
        font-weight: bold;
        color: #2E2C2B;
        .more-icon {
          width: 32rpx;
          height: 32rpx;
          background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-right-icon.png');
          background-size: 32rpx 32rpx;
        }
      }
      .danger-color {
        color: #FF3B30;
      }
      .success-color {
        color: #32a766;
      }
    }
  }
  .pay-order-list {
    .pay-order-item {
      padding: 16rpx 0;
      background: linear-gradient( 180deg, #FAFAFA 0%, #FFFFFF 100%);
      .pay-order-title {
        position: relative;
        width: 224rpx;
        justify-content: center;
        &::after {
          position: absolute;
          right: 0;
          top: 0;
          width: 1rpx;
          height: 48rpx;
          background-color: #EBE9E7;
        }
        .pay-order-icon {
          margin-right: 8rpx;
          &::before {
            font-family: "Wukong Font", serif;
            content: "\e916";
            color: #F9733E;
            font-size: 32rpx;
          }
        }
        .pay-order-title-text {
          background: linear-gradient(4.2688682312579694e-7deg, #F9983E 0%, #FD5935 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: bold;
          font-size: 24rpx;
        }
      }
      .pay-order-content {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 286rpx;
        &::before {
          content: "";
          display: block;
          position: absolute;
          top: 50%;
          left: 0;
          width: 1rpx;
          height: 48rpx;
          background-color: #EBE9E7;
          transform: translateY(-24rpx);
        }
        &::after {
          content: "";
          display: block;
          position: absolute;
          top: 50%;
          right: 0;
          width: 1rpx;
          height: 48rpx;
          background-color: #EBE9E7;
          transform: translateY(-24rpx);
        }
        .pay-order-content-title {
          margin-bottom: 4rpx;
          font-size: 20rpx;
          color: #6F6A67;
        }
        .pay-order-money {
          font-size: 24rpx;
          color: #2E2C2B;
          font-weight: bold;
        }
        .pay-order-num {
          font-size: 24rpx;
          color: #2E2C2B;
        }
      }
      .pay-order-time {
        flex: 1;
        padding-right: 32rpx;
        font-size: 24rpx;
        justify-content: flex-end;
        text-align: right;
        .pay-order-time-title {
          margin-bottom: 4rpx;
          color: #6F6A67;
        }
        .pay-order-time-text {}
      }
    }
  }
}

.powerbank-detail {
  margin: 24rpx;
  background-color: #fff;
  border-radius: 8px;
  .powerbank-status {
    padding: 32rpx;
    border-bottom: 1rpx solid #EBE9E7;
    &:nth-last-child(1) {
      border-bottom: none;
    }
    .today-miss {
      padding: 4rpx 8rpx;
      background: rgba(255,59,48,0.1);
      border-radius: 8rpx;
      font-size: 24rpx;
      line-height: 34rpx;
      color: #FF3B30;
    }
    .powerbank-status-title {
      margin-bottom: 16rpx;
      .powerbank-status-title-text {
        margin-right: 24rpx;
        color: #6F6A67;
        font-size: 28rpx;
        line-height: 40rpx;
        font-weight: bold;
        letter-spacing: 1rpx;
      }
      .powerbank-status-title-value {
        font-size: 28rpx;
        line-height: 40rpx;
        font-weight: bold;
        color: #2E2C2B;
      }
      .powerbank-status-title-icon {
        width: 32rpx;
        height: 32rpx;
        background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-right-icon.png');
        background-size: 32rpx 32rpx;
      }
    }
    .powerbank-miss-title {
      margin-bottom: 0;
    }
  }
  .powerbank-operating-content {
    .progress-block {
      position: relative;
      width: 310rpx;
      height: 66rpx;
      padding: 16rpx 24rpx;
      border: 1px solid #43B730;
      box-sizing: border-box;
      border-radius: 8rpx;
      overflow: hidden;
      font-size: 24rpx;
      .progress-content {
        position: absolute;
        left: 0;
        top: 0;
        width: 0%;
        height: 66rpx;
        transition: .6s;
      }
      .progress-content-green {
        width: var(--bg-width);
        background: linear-gradient( 90deg, rgba(67,183,48,0.15) 0%, rgba(157,234,59,0.25) 100%);
      }
      .progress-content-red {
        width: var(--bg-width);
        background: linear-gradient( 90deg, rgba(255,59,48,0.25) 0%, rgba(249,115,62,0.25) 100%);
      }
    }
    .progress-red-block {
      border: 1px solid #FF3B30;
    }
  }
  .powerbank-stock-content {
    .powerbank-stock-item {
      width: 202rpx;
      box-sizing: border-box;
      border: 1px solid #EBE9E7;
      border-radius: 8rpx;
      overflow: hidden;
      .powerbank-stock-item-title {
        width: 100%;
        padding: 8rpx 0;
        background: linear-gradient( 90deg, rgba(42,162,105,0.1) 0%, rgba(157,234,59,0.1) 100%);
        border-bottom: 1px solid #EBE9E7;
        text-align: center;
        font-size: 24rpx;
        line-height: 34rpx;
        color: #2E2C2B;
      }
      .powerbank-stock-item-value {
        padding: 16rpx 0;
        font-size: 28rpx;
        line-height: 40rpx;
      }
    }
  }
  .powerbank-stock-agent {
    .powerbank-stock-item {
      margin-right: 24rpx;
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
  }
  .powerbank-miss-content {
    margin-top: 24rpx;
    border: 1px solid #EBE9E7;
    border-radius: 8rpx;
    .miss-item {
      flex: 1;
      padding: 16rpx 0 20rpx;
      .miss-item-title {
        margin-bottom: 12rpx;
        font-size: 26rpx;
        line-height: 36rpx;
        color: #6F6A67;
      }
      .miss-item-value {
        font-size: 28rpx;
        line-height: 40rpx;
        color: #2E2C2B;
        font-weight: bold;
      }
      .more-icon {
        width: 32rpx;
        height: 32rpx;
        background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-right-icon.png');
        background-size: 32rpx 32rpx;
      }
    }
  }
  .powerbank-miss-sub-content {
    // margin: 0 32rpx;
    // padding-bottom: 8rpx;
    .miss-item {
      position: relative;
      &::after {
        position: absolute;
        right: 0;
        top: 30rpx;
        width: 1rpx;
        height: 32rpx;
        background: #EBE9E7;
      }
    }
  }
}

.powerbank-function {
  margin: 24rpx;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  .powerbank-function-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    .powerbank-function-icon {
      font-size: 32rpx;
      height: 32rpx;
      line-height: 32rpx;
      &::before {
        display: block;
        font-family: "Wukong Font", serif;
        content: "\e93c";
        font-size: 32rpx;
        line-height: 32rpx;
      }
    }
    .powerbank-function-icon2 {
      &::before {
        content: "\e93d"
      }
    }
    .powerbank-function-icon3 {
      &::before {
        content: "\e93e"
      }
    }
    .powerbank-function-text {
      margin-top: 16rpx;
      font-size: 26rpx;
      line-height: 40rpx;
      color: #2E2C2B;
    }
  }
}