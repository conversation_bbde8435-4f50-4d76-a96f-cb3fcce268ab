<template>
	<view class="tips">
    <uni-popup ref="tipsPopup" :animation="false" :maskClick="false">
			<view class="popup-box">
        <view class="popup-content" v-if="showTipsType == 'countInboundInfo'">
          <view class="tip-title">流入/流出</view>
          <view class="tip-content">
            <view class="">宝的流入流出=持有宝数-宝权益数</view>
            <view class="">大于0，表示有净流入</view>
            <view class="">小于0，表示有净流出</view>
          </view>
          <view class="tip-btn" @click="closePopup">关闭</view>
        </view>
				<view class="popup-content" v-if="showTipsType == 'operationInfo'">
          <view class="tip-title">运行宝仓比</view>
          <view class="tip-content">
            <view class="">运行宝仓比 = 运行宝/已激活的机柜仓道数之和</view>
          </view>
          <view class="tip-btn" @click="closePopup">关闭</view>
        </view>
        <view class="popup-content" v-if="showTipsType == 'ratioInfo'">
          <view class="tip-title">宝仓比</view>
          <view class="tip-content">
            <view class="">宝仓比=(运行宝+库存宝)/所有机柜仓道数之和</view>
          </view>
          <view class="tip-btn" @click="closePopup">关闭</view>
        </view>
			</view>
		</uni-popup>
	</view>
</template>

<script>

export default {
  props: {
    showTips: {
      type: Boolean,
      default: false,
    },
    showTipsType: {
      type: String,
      default: '',
    },
    countPowerbank: {
      type: Object,
      default: {}
    }
  },
  watch: {
    showTips(newVal) {
      if (newVal) {
        this.$refs.tipsPopup.open();
      }
    }
  },
  computed: {
    calcKeepAll() {
      const { countPowerbank } = this;

      let sum = (countPowerbank.countWorking + countPowerbank.countRented) + countPowerbank.countStock + (countPowerbank.countMiss + countPowerbank.countLostByEndOrder + countPowerbank.countLostByRefund) + countPowerbank.countSold;

      return sum ? sum : 0;
    },
  },
  methods: {
    closePopup() {
      this.$refs.tipsPopup.close();
      this.$emit('closePopup')
    },
  }
};
</script>

<style lang="scss" scoped>
.popup-box {
  width: 638rpx;
  padding-top: 16px;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  .tip-title {
    margin-bottom: 16px;
    font-size: 32rpx;
    line-height: 44rpx;
    font-weight: bold;
    color: #2E2C2B;
    letter-spacing: 1rpx;
    text-align: center;
  }
  .tip-content {
    padding: 0 24rpx;
    margin-bottom: 16px;
    text-align: center;
    font-size: 14px;
    line-height: 20px;
    color: #2E2C2B;
    letter-spacing: 1rpx;
  }
  .shadow-box {
    padding: 8px 0;
    margin: 12px 32rpx 16px;
    background: #FAFAFA;
    border-radius: 4px;
    color: #6F6A67;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 1rpx;
  }

  .tip-btn {
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background: linear-gradient( 180deg, #FAFAFA 0%, #FFFFFF 100%);
    color: #6F6A67;
    font-size: 14px;
    letter-spacing: 1px;
  }
}
</style>
