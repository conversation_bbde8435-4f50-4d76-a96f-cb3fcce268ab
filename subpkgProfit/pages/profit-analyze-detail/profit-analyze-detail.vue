<template>
	<view class="profit-analyze-list">
		<mNav title="收益分析" titleColor="#fff" :isShowBack="true" topBarColor="transparent" statusBarColor="transparent" bgImage="https://oss-wukong-web.oss-cn-shenzhen.aliyuncs.com/test/subpkgProfit-profit-analyze-list-bg.png" />
		<view class="search-box" :style="{'--nav-height': -navHeight + 'px', 'top': navHeight + 'px'}">
			<view class="search-box-content">
				<view class="time-tab flex-row">
					<view class="time-tab-item" :class="{'time-tab-item-active': timeType == 1}" @click="changeTimeType(1)">按日</view>
					<view class="time-tab-item" :class="{'time-tab-item-active': timeType == 2}" @click="changeTimeType(2)">按月</view>
				</view>
				<view class="select-box flex-row" v-if="isManager">
					<view class="select-type-box">
						<uni-data-select v-model="type" :localdata="typeList" @change="changeType" @showSelectChange="showSelectChange" :clear="false" placeholder="请选择"></uni-data-select>
					</view>
					<view class="select-name-box">
						<luyj-select-lay :value="filterId" name="storekeeper" placeholder="请选择" :options="filterList" slabel="text" @selectitem="changeFilter" @showSelectChange="showSelectChange"></luyj-select-lay>
					</view>
				</view>
			</view>
		</view>
		<view class="profit-box" v-if="detail.statisticTotal">
			<view class="profit-time flex-row" :class="{'profit-time-zindex': !isShowSelectList}" @click="changeDate">
				<text class="profit-header-time">{{ timeType == 1 ? time.slice(0, 10) : time.slice(0, 7) }}</text>
				<text class="profit-time-arrow"></text>
			</view>
			<view class="store-info" v-if="!showType">
				<view class="store-info-header">
					<text class="store-info-name">{{detail.companyFullName}}( id: {{ detail.id }})</text>
				</view>
			</view>
			<view class="store-info" v-if="showType == 1">
				<view class="store-info-header">
					<text class="store-info-name">{{detail.companyFullName}}( id: {{ detail.id }})</text>
					<text class="store-info-contract">{{detail.distance}}</text>
				</view>
				<view class="store-info-content flex-row-between mgb12">
					<view class="flex-row">
						<text>负责人：</text>
						<text>{{ detail.realName }}</text>
					</view>
					<view v-if="false">
						<text>合作：</text>
						<text>{{ detail.contract.signedDate }}</text>
					</view>
				</view>
				<view class="store-info-content flex-row-between">
					<view class="flex-row" v-if="detail.agent">
						<text>代理：</text>
						<text>{{ detail.agent.companyFullName }}</text>
					</view>
					<view>
						<text>BD：</text>
						<text>{{ detail.developer.realName }}</text>
					</view>
				</view>
			</view>
			<view class="store-info" v-if="showType == 2">
				<view class="store-info-header">
					<text class="store-info-name">{{detail.companyFullName}}( id: {{ detail.id }})</text>
					<text class="store-info-phone">{{detail.username}}</text>
				</view>
				<view class="store-info-content flex-row-between">
					<text class="store-info-company">{{detail.roleInfo.orgInfo.companyFullName}}</text>
				</view>
			</view>
			<view class="store-info" v-if="showType == 3">
				<view class="store-info-header">
					<text class="store-info-name">{{detail.companyFullName}}( id: {{ detail.id }})</text>
					<text class="store-info-contract">{{detail.distance}}</text>
				</view>
				<view class="store-info-content flex-row-between">
					<view class="flex-row">
						<text>负责人：</text>
						<text>{{ detail.realName }}</text>
					</view>
					<view v-if="false">
						<text>合作：</text>
						<text>{{ detail.contract.signedDate }}</text>
					</view>
				</view>
			</view>
			<view class="store-sum flex-row-between">
				<view class="store-sum-item" v-if="false">
					<view class="store-sum-item-title">设备数</view>
					<view class="store-sum-item-value">{{ detail.countTerminal.countAll }}</view>
				</view>
				<view class="store-sum-item">
					<view class="store-sum-item-title">订单数</view>
					<view class="store-sum-item-value" v-if="hasProfitOperationPermi">{{ detail.statisticAll.countOrder }}</view>
					<view class="store-sum-item-value" v-else>{{ detail.statisticAll.countOrderEmp }}</view>
				</view>
				<view class="store-sum-item">
					<view class="store-sum-item-title">总实收</view>
					<view class="store-sum-item-value" v-if="hasProfitOperationPermi">￥{{ detail.statisticAll.netRevenueManager / 100 }}</view>
					<view class="store-sum-item-value" v-else>￥{{ detail.statisticAll.netRevenueEmp / 100 }}</view>
				</view>
				<view class="store-sum-item">
					<view class="store-sum-item-title">我方收益</view>
					<view class="store-sum-item-value">￥
						<text>{{ getAllProfit(detail.statisticAll) }}</text>
					</view>
				</view>
			</view>
			<view class="store-data-box">
				<view class="store-data-item flex-row-between">
					<view class="store-data-item-title">
						主营业务收入
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money" v-if="hasProfitOperationPermi">{{ detail.statisticTotal.revenueManager / 100 }}</text>
						<text class="store-data-money" v-else>{{ detail.statisticTotal.revenueEmp / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between">
					<view class="store-data-item-title">
						VIP优惠
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money" v-if="hasProfitOperationPermi">{{ detail.statisticTotal.discountManager / 100 }}</text>
						<text class="store-data-money" v-else>{{ detail.statisticTotal.discountEmp / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between">
					<view class="store-data-item-title color-black">
						订单实收
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money color-black" v-if="hasProfitOperationPermi">{{ detail.statisticTotal.netRevenueManager / 100 }}</text>
						<text class="store-data-money color-black" v-else>{{ detail.statisticTotal.netRevenueEmp / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item-line"></view>
				<view class="store-data-item flex-row-between">
					<view class="store-data-item-title">
						我方分润
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money">{{ getProfit(detail.statisticTotal) }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between" v-if="detail.statisticTotal.expenseCost">
					<view class="store-data-item-title">
						充电宝成本
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money">{{ detail.statisticTotal.expenseCost / 100}}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between" v-if="isAlly">
					<view class="store-data-item-title">
						居间服务费
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money">{{ detail.statisticTotal.agentEmpProfit / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between" v-if="isAlly && detail.statisticTotal.expenseChannelIntangible && hasProfitOperationPermi">
					<view class="store-data-item-title">
						居间现金折扣
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money">{{ -detail.statisticTotal.expenseChannelIntangible / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between">
					<view class="store-data-item-title">
						商户服务费
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money">{{ detail.statisticTotal.storeKeeperFakeProfit / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between" v-if="detail.statisticTotal.expenseStoreKeeperIntangible && hasProfitOperationPermi">
					<view class="store-data-item-title">
						商户现金折扣
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money">{{ -detail.statisticTotal.expenseStoreKeeperIntangible / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between">
					<view class="store-data-item-title">
						系统服务费
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money">{{ detail.statisticTotal.expenseService / 100 }}</text>
					</view>
				</view>
				<view class="store-data-item flex-row-between store-data-item-profit">
					<view class="store-data-item-title color-black">
						我方收益
					</view>
					<view class="store-data-item-value">
						<text class="store-data-unit">￥</text>
						<text class="store-data-money color-black">{{ getAllProfit(detail.statisticTotal) }}</text>
					</view>
				</view>
			</view>
		</view>
		<lingfeng-timepicker ref="dayPop" :startTime="time" type="date" @change="changeTime"></lingfeng-timepicker>
		<lingfeng-timepicker ref="monthPop" type="year-month" :defaultData="defaultData" @change="changeTime"></lingfeng-timepicker>
	</view>
</template>

<script src="./profit-analyze-detail.js"></script>

<style>
page {
	background-color: #fff;
}
</style>

<style lang="scss" scoped>
@import './profit-analyze-detail.scss'
</style>
