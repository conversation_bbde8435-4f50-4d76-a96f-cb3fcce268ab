import { navHeight, orgId, isAgentType, isAlly, isGoldenAlly, isManager } from '@/utils/role.js';
import mNav from '@/components/m-nav/m-nav.vue';
import { getDayProfitDetail, findOrgFilterList, getMonthProfitDetail, getEmpDayProfitDetail, getEmpMonthProfitDetail } from '@/subpkgProfit/api/profitAnalyzeDetail.js';
import { formatMoney, getToday, getMonth } from '@/utils/function.js';
import { hasProfitOperationPermi, } from "@/utils/permissionList.js";

let today = getToday();
export default {
  data() {
    return {
      filterId: '',
      time: '',
      timeType: 1,  // 1日 2月
      type: 1,  // 1商户2BD3代理商4联盟商
      showType: 1,  // 展示信息时需要获取数据后再更新type
      storeKeeperList: [],
      staffList: [],
      agentList: [],
      detail: {},
      defaultData: {
        month: today.slice(0,7)
      },
      isShowSelectList: false,  // 是否展示下拉选择框
    };
  },
  components: {
    mNav,
  },
  computed: {
    navHeight,
    typeList() {
      let arr = [{
        text: '商户',
        value: 1,
      }, {
        text: 'BD',
        value: 2,
      }];

      if (this.isAlly) {
        arr.push({
          text: '代理商',
          value: 3,
        });
      }

      // if (this.isGoldenAlly) {
      //   arr.push({
      //     text: '联盟商',
      //     value: 4,
      //   });
      // }
      return arr;
    },
    filterList() {
      let { agentList, staffList, storeKeeperList, type } = this;

      if (type == 1) return storeKeeperList;
      if (type == 2) return staffList;
      if (type == 3) return agentList;
    },
    orgId,
    isAgentType,
    isAlly,
    isGoldenAlly,
    hasProfitOperationPermi,
    isManager
  },
  filters: {
    formatMoney
  },
  async onLoad(options) {
    this.time = options.time;
    this.timeType = options.timeType;
    this.filterId = options.id;
    this.type = this.filterId != this.orgId ? parseInt(options.type, 10) : 0;
    this.showType = this.type;
    this.detail = await this.fetchData({ time: this.time });

    let res = await this.fetchFilterList();
    this.storeKeeperList = res.listStoreKeeper;
    this.agentList = res.listAgent;
    this.staffList = res.listEmployee;
  },
  methods: {
    async fetchData(data) {
      let { timeType } = this;
      let params = {
        id: this.filterId || this.orgId,
        time: data.time,
      }
      let res;

      let dayQuery;
      let monthQuery;
      let queryString;
      if ([0, 1, 3, 4].indexOf(this.type) > -1) {
        dayQuery = getDayProfitDetail;
        monthQuery = getMonthProfitDetail;
        queryString = 'findCurrentOrganization';
      } else if ([2].indexOf(this.type) > -1) {
        dayQuery = getEmpDayProfitDetail;
        monthQuery = getEmpMonthProfitDetail;
        queryString = 'findCurrentEmployee';
      }

      if (timeType == 1) res = await dayQuery(params);
      else if (timeType == 2) res = await monthQuery({ id: this.filterId || this.orgId, time: this.time })
      let resData = res.data[queryString];
      let array = [];

      for(let key in resData) {
        if (key.indexOf('time') > -1) {
          resData.time = timeType == 1 ? key.slice(5).replace(/zz/g,"-") : key.slice(5).replace(/zz/g,"-").slice(0, 7);
          resData.statisticTotal = resData[key]
        }
      }
      return resData;
    },
    async fetchFilterList() {
      let res = await findOrgFilterList(this.orgId);

      return res.data.findCurrentOrganization;
    },
    async changeTimeType(type) {
      // this.filterId = '';
      this.timeType = type;
      this.time = today;
      this.detail = await this.fetchData({ time: getToday(), });
    },
    changeType() {
      // this.filterId = '';
    },
    async changeFilter(index, filter) {
      this.filterId = filter.value;
      uni.pageScrollTo({
				scrollTop: 0,
				duration: 0,
			})

      this.detail = await this.fetchData({ time: getToday(), });
      this.showType = this.type;
    },
    async changeDate() {
      let { timeType } = this;

      if (timeType == 1) {
        this.$refs.dayPop.show();
      } else {
        this.$refs.monthPop.show();
      }
    },
    async changeTime(val){
      if (val.indexOf('undefined') > -1) {
        val = getMonth();
      }
      if (!this.timeType && !val)	return;
      this.time = this.timeType == 1 ? val : val + '-01';
      this.defaultData.month = val;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      });
      this.detail = await this.fetchData({ time: this.time, });
    },
    showSelectChange(val) {
      this.isShowSelectList = val;
    },
    getProfit(data) {
      if (this.isAlly) return data.allyEmpProfit / 100;
      if (this.isAgentType) return data.agentEmpProfit / 100;
    },
    getAllProfit(data) {
      if (this.isAlly) {
        if (this.hasProfitOperationPermi)
          return data.allyManagerProfit / 100;
        else 
          return data.allyEmpProfit / 100;
      }
      if (this.isAgentType) {
        if (this.hasProfitOperationPermi)
          return data.agentManagerProfit / 100;
        else
          return data.agentEmpProfit / 100;
      }
    },
    getOperateProfit(data) {
      if (this.isAlly) return data.operationProfit / 100;
      if (this.isAgentType) return data.expenseChannelIntangible / 100;
    },
    getPreviousDay(dateString) {
      var currentDate = new Date(dateString);  // 将日期字符串转换为日期对象
    
      currentDate.setDate(currentDate.getDate() - 1);  // 设置日期为前一天
    
      // 格式化日期为 "YYYY-MM-DD" 格式
      var formattedDate = currentDate.toISOString().split('T')[0];
    
      return formattedDate;
    },
  },
}