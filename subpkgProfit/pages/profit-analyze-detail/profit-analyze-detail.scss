::v-deep .m-nav-container {
  position: fixed !important;
  width: 100% !important;
}

::v-deep .uni-select {
  width: 188rpx !important;
	border: 1px solid #FFFFFF !important;
	height: 30px !important;
  border-radius: 15px 2px 2px 15px !important;
  padding-left: 18px !important;
}
::v-deep .uni-select__input-text {
	color: #fff !important;
	font-size: 24rpx !important;
}
::v-deep .uni-icons {
	width: 24rpx;
	height: 16rpx;
	background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/storekeeper-list-slide-down.png');
	background-size: 24rpx 16rpx;
	color: transparent !important;
	background-repeat: no-repeat;
	background-position: center center;
}

::v-deep .uni-select-lay {
	height: 30px !important;
	border-radius: 0 15px 15px 0;
}
::v-deep .uni-select-lay-select {
  padding-left: 36rpx !important;
  height: 30px !important;
	border: 1px solid #fff !important;
  border-radius: 0 15px 15px 0 !important;
}
::v-deep .uni-select-lay-input {
  color: #fff !important;
  font-size: 24rpx !important;
}

::v-deep .uni-select-lay-placeholder {
  /* WebKit browsers */
  color: #fff !important;
}
::v-deep .uni-select-lay-icon::before {
  background-color: transparent !important;
}
::v-deep .uni-select-lay-icon text {
  border: none !important;
  width: 24rpx !important;
  height: 16rpx !important;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/profit-analyze-icon.png');
  background-size: 24rpx 16rpx;
}

::v-deep .vue-ref {
  padding-bottom: 0 !important;
}


.search-box {
  position: fixed;
  left: 0;
  width: 100%;
  height: 140px;
  padding-top: 20px;
  background-image: url('https://oss-wukong-web.oss-cn-shenzhen.aliyuncs.com/test/subpkgProfit-profit-analyze-list-bg.png');
  background-size: 100% 224px;
  background-position: 0 var(--nav-height);
  background-repeat: no-repeat;
  box-sizing: border-box;
  .search-box-content {
    padding-left: 24rpx;
    .time-tab {
      width: 320rpx;
      height: 22px;
      margin-bottom: 22px;
      border: 1px solid #fff;
      box-sizing: border-box;
      border-radius: 10px;
      overflow: hidden;
      .time-tab-item {
        width: 160rpx;
        border-right: 1px solid #fff;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        color: #fff;
        box-sizing: border-box;
        &:nth-last-child(1) {
          border-right: none;
        }
      }
      .time-tab-item-active {
        background-color: #fff;
        color: #FF8540;
      }
    }
    .select-box {
      margin-right: 24rpx;
      .select-type-box {
        margin-right: 8rpx;
      }
      .select-name-box {
        flex: 1;
      }
    }
  }
}

.profit-time {
  margin: 0 24rpx;
  padding-left: 36rpx;
  .profit-header-time {
    color: rgba(45,45,45,0.9);
    font-size: 28rpx;
  }
  .profit-time-arrow {
    display: block;
    width: 32rpx;
    height: 16rpx;
    margin-left: 20rpx;
    background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/profit-bill-all-time-arrow.png');
    background-size: 32rpx 16rpx;
  }
}
.profit-time-zindex {
  position: relative;
  z-index: 10;
}


.profit-box {
  padding-top: 201px;
  .store-info {
    margin: 0 24rpx 14rpx;
    padding: 26rpx 36rpx 20rpx;
    border-bottom: 1px solid rgba(45,45,45,0.1);
    .store-info-header {
      display: flex;
      align-items: center;
      margin-bottom: 26rpx;
      .store-info-name {
        font-size: 32rpx;
        line-height: 44rpx;
        color: rgba(45,45,45,0.9);
        font-weight: bold;
      }
      .store-info-phone {
        margin-left: 32rpx;
        font-size: 24rpx;
        line-height: 34rpx;
        color: rgba(45,45,45,0.9);
      }
      .store-info-company {
        font-size: 24rpx;
        line-height: 34rpx;
        color: rgba(45,45,45,0.9);
      }
    }
    .store-info-content {
      font-size: 24rpx;
      line-height: 34rpx;
      color: rgba(45,45,45,0.9);
    }
    .mgb12 {
      margin-bottom: 12rpx;
    }
  }
  .store-sum {
    margin: 0 64rpx 20rpx;
    .store-sum-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      .store-sum-item-title {
        font-size: 24rpx;
        line-height: 34rpx;
        color: rgba(45,45,45,0.7);
      }
      .store-sum-item-value {
        font-size: 24rpx;
        line-height: 34rpx;
      }
    }
  }
  .store-data-box {
    margin: 0 24rpx;
    padding: 20rpx 36rpx 22rpx;
    color: rgba(45,45,45,0.6);
    border: 1px solid rgba(45,45,45,0.1);
    border-radius: 16rpx;
    letter-spacing: 2rpx;
    .store-data-item {
      margin-bottom: 16rpx;
      font-size: 24rpx;
      line-height: 34rpx;
      .store-data-unit {
        font-size: 20rpx;
      }
      .color-black {
        color: #2D2D2D;
      }
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }
    .store-data-item-line {
      height: 2rpx;
      margin-top: 20rpx;
      margin-bottom: 20rpx;
      background: rgba(45,45,45,0.1);
    }
    .store-data-item-profit {
      font-size: 28rpx;
      line-height: 40rpx;
      .store-data-unit {
        font-size: 20rpx;
      }
    }
  }
}