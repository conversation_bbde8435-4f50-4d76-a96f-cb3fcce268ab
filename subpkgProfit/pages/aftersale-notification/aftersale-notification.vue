<template>
	<view class="aftersale-notification">
		<mNav
			title="维修清单"
			topBarColor="transparent"
			statusBarColor="transparent"
			:bgImage="'https://oss-wukong-web.oss-cn-shenzhen.aliyuncs.com/mp/%E8%83%8C%E6%99%AF%402x.png'"
    />
		<view class="aftersale-bg"></view>
		<view class="aftersale-banner">
			<view class="aftersale-rule">
				<view class="rule-icon"></view>
				<view class="rule-text">售后规则</view>
			</view>
			<view class="aftersale-sum">
				<view class="aftersale-sum-title">送修宝</view>
				<view class="aftersale-sum-num">30</view>
			</view>
			<view class="aftersale-detail">
				<view class="aftersale-sender">
					<view class="aftersale-title">送修方</view>
					<view class="">江西悟空快充有限公司</view>
				</view>
				<view class="aftersale-receiver">
					<view class="aftersale-title">售后方</view>
					<view class="">东莞市创明福兴电子科技有限公司</view>
				</view>
			</view>
		</view>
		<view class="table-box">
			<view class="table-content">
				<view class="table-header flex-row-between">
					<view class="table-tr-1">宝SN</view>
					<view class="table-tr-2">型号</view>
					<view class="table-tr-3">送修备注</view>
				</view>
				<view class="table-row flex-row-between">
					<view class="table-tr-1">2107012330727013135326</view>
					<view class="table-tr-2">二代</view>
					<view class="table-tr-3">送修备注</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script src="./aftersale-notification.js">
</script>

<style lang="scss" scoped>
@import './aftersale-notification.scss'
</style>