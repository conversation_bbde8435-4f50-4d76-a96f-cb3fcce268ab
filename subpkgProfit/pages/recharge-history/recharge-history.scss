.recharge-history-contain {
  padding-top: 20px;
  padding-bottom: 120px;
}

.tab-box {
  display: flex;
  margin: 0 24rpx 20px;
  .tab-item {
    margin-right: 30rpx;
    font-size: 24rpx;
    &:nth-last-child(1) {
      margin-right: 0;
    }
  }
  .tab-item-active {
    position: relative;
    font-weight: bold;
    color: $global-color-primary;
    &::after {
      position: absolute;
      left: 50%;
      bottom: -10rpx;
      content: "";
      transform: translateX(-50%);
      display: block;
      width: 100%;
      height: 4rpx;
      background-color: $global-color-primary;
    }
  }
}

.list {
  margin: 0 24rpx;
  padding: 10rpx 24rpx;
  border-radius: 16rpx;
  background-color: #fff;
  .recharge-item {
    padding: 20rpx 10rpx;
    border-bottom: 1px solid #eee;
    font-size: 26rpx;
    letter-spacing: 2rpx;
    &:nth-last-child(1) {
      border-bottom: none;
    }
    .recharge-item-header {
      margin-bottom: 20rpx;
    }
    .recharge-item-time {
      color: #666;
    }
    .recharge-item-state {}
    .recharge-item-product {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }
    .recharge-item-money {
      font-size: 28rpx;
      font-weight: bold;
    }
    .blue {
      color: #409EFF;
    }
  }
}

.list-empty {
  margin: 0 24rpx;
  border-radius: 16rpx;
  line-height: 320rpx;
  font-size: 28rpx;
  letter-spacing: 2rpx;
  color: #999;
  background-color: #fff;
  text-align: center;
}

.no-more-tips {
  margin-top: 20px;
  font-size: 28rpx;
  letter-spacing: 2rpx;
  color: #999;
  text-align: center;
}

.red {
  color: #cd2525;
}
.green {
  color: #008000;
}
.blue {
  color: #409EFF;
}