<template>
	<view class="recharge-history-contain">
		<view class="tab-box">
			<view class="tab-item" :class="{'tab-item-active': item.value == catalog}" v-for="(item, key) in tabList" :key="item.value" @click="changeCatalog(item)">{{ item.text }}</view>
		</view>
		<view class="list" v-if="list.length">
			<view class="recharge-item" v-for="(item, key) in list" :key="item.id">
				<view class="recharge-item-header flex-row-between">
					<view class="recharge-item-time">{{ item.createdDate.slice(0, 10) }} {{ item.createdDate.slice(11, 19) }}</view>
					<view class="recharge-item-state" :class="{'green': item.state == 'PAID', 'red': item.state == 'CREATED'}" v-if="item.type != 'CONSUME'">{{ item.stateString }}</view>
					<view class="recharge-item-state blue" v-else>消费</view>
				</view>
				<view class="recharge-item-content"></view>
				<view class="recharge-item-footer flex-row-between">
					<view class="recharge-item-product">{{ item.rechargeGoods.fullName }}</view>
					<view class="recharge-item-money" :class="{'green': item.state == 'PAID', 'red': item.state == 'CREATED'}" v-if="item.type != 'CONSUME'">￥{{ item.paymentOrder.amount / 100 }}</view>
					<view class="recharge-item-money blue" v-else>{{ item.times }}次</view>
				</view>
			</view>
		</view>
		<view class="list-empty" v-else>暂无购买记录哦~</view>
		<view class="no-more-tips" v-if="list.length && !pageInfo.hasNextPage">没有更多了哦~</view>
	</view>
</template>

<script src="./recharge-history.js">
</script>

<style lang="scss" scoped>
@import './recharge-history.scss'
</style>
