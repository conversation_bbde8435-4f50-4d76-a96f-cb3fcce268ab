import { getRechargeHistory, } from '@/subpkgProfit/api/recharge-history';
import { flatNodeList } from '@/utils/function';
import { orgId } from '@/utils/role';

export default {
  data() {
    return {
      catalog: null,
      tabList: [{
        text: '全部',
        value: null
      },{
        text: '手机号验证',
        value: 'MOBILE'
      }, {
        text: '一般广告',
        value: 'AD_NORMAL'
      }, {
        text: '视频广告',
        value: 'AD_VIDEO'
      }],
      list: [],
      pageInfo: {},
    };
  },
  computed: {
    orgId,
  },
  async onLoad() {
    this.list = await this.fetchData();
  },
  async onReachBottom() {
		console.log("触底了");
		if (!this.pageInfo.hasNextPage) return;
		this.$modal.loading("加载中");
		let list = await this.fetchData();
    this.list = [...this.list, ...list];
		this.$modal.closeLoading();
	},
  async onPullDownRefresh() {
		this.pageInfo = {};
    this.list = await this.fetchData();
		uni.stopPullDownRefresh();
	},
  methods: {
    async fetchData() {
      let res = await getRechargeHistory({ cursor: this.pageInfo.endCursor, orgId: this.orgId, catalog: this.catalog });

      this.pageInfo = res.data.findRechargeOrder.pageInfo;
      return flatNodeList(res.data.findRechargeOrder.edges);
    },
    async changeCatalog(item) {
      this.pageInfo = {};
      this.catalog = item.value;
      this.list = await this.fetchData();
    },
  },
}