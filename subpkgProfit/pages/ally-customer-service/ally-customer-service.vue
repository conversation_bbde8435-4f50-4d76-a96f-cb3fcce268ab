<template>
  <view class="ally-customer-service">
    <view class="set-content">
      <view class="set-mode flex-row">
        <view class="set-title">客服模式</view>
        <view class="set-mode-list flex-row">
          <view class="set-mode-item flex-row" v-for="(item, key) in modeList" :key="key"
                @click="changeMode(item)">
            <view class="set-mode-icon" :class="{ 'set-mode-icon-active': item.value == mode }"></view>
            <view class="set-mode-text">{{ item.text }}</view>
          </view>
        </view>
      </view>
      <block v-if="mode == 'PLATFORM'">
        <view class="platform-tips">平台自动处理客诉</view>
        <view class="platform-sub-tips flex-row">
          <view class="warn-icon"></view>
          <view class="">费用相关客诉，平台将自动退费</view>
        </view>
        <view class="set-btn flex-row" v-if="!initSetting.object.mode || initSetting.object.mode == 'PLATFORM'">
          <view class="bind-icon"></view>
          <view class="bind-btn">当前模式</view>
        </view>
        <view class="set-btn" @click="$refs.tipsPopup.open()" v-else>
          <view class="bind-btn">选择当前模式</view>
        </view>
      </block>
      <block v-if="mode == 'PREMIUM'">
        <view class="premium-tips">请联系客服经理开通！</view>
        <view class="set-btn flex-row" v-if="initSetting.object.mode && initSetting.object.mode == 'PREMIUM'">
          <view class="bind-icon"></view>
          <view class="bind-btn">当前模式</view>
        </view>
        <view class="set-btn" @click="toCompanyChat" v-else>
          <view class="bind-btn">去联系</view>
        </view>
      </block>
      <block v-if="mode == 'SELF_MANAGED'">
        <view class="set-org flex-row">
          <view class="set-title">
            <uni-data-select :localdata="typeList" :clear="false" v-model="type"
                             @change="changeType"></uni-data-select>
          </view>
          <view class="set-org-content">
            <next-search-select
                class="select-input"
                :multiple="false"
                :list="list"
                label-key="text"
                value-key="value"
                :placeholder="'请选择' + getOrgTypeName "
                :title="getOrgTypeName"
                v-model:value="selectOrgId"
                @search="fetchFilterList"
                @change="(v)=>selectOrgId = v.value"
                selectColor="rgba(45,45,45,0.9)"
                :showSearchBtn="true"
            ></next-search-select>
          </view>
        </view>
        <view class="set-url flex-row">
          <view class="set-title">当前链接</view>
          <view class="set-url-content flex-row">
            <input class="url-input" type="text" placeholder="请填写客服链接" v-model="url"></input>
            <view class="set-url-icon"></view>
          </view>
        </view>
        <view class="set-btn" @click="bind">
          <view class="bind-btn">确认绑定</view>
        </view>
      </block>
    </view>
    <view class="line"></view>
    <view class="custom-setting">
      <view class="custom-setting-title">配置列表</view>
      <view class="custom-setting-list" v-if="customList.length">
        <block v-for="(item, key) in customList" :key="key">
          <movable-area class="area">
            <movable-view class="area-item" :x="item.swipeX" direction="horizontal"
                          @change="onChange($event, key)" out-of-bounds="true" animation="true"
                          @touchend="swipeEnd(key)" @touchstart="swipeStart(key)">
              <view class="custom-setting-item custom-content">
                <view class="flex-row">
                  <view class="org-type">{{
                      item.orgType == 'STOREKEEPER' ? '商户' : item.orgType ==
                      'STORE' ? '门店' : item.id == orgId ? '联盟商' : '代理'
                    }}
                  </view>
                  <view class="org-name">{{ item.companyFullName }}</view>
                </view>
                <view class="flex-row custom-setting-footer"
                      v-if="(item.orgConfig && item.orgConfig.setting && item.orgConfig.setting.object && item.orgConfig.setting.object.mode == 'SELF_MANAGED') || (item.orgConfig && item.orgConfig.setting && item.orgConfig.setting.object && !item.orgConfig.setting.object.mode && item.orgConfig.setting.object.url)">
                  <view class="custom-setting-footer-title">客服：</view>
                  <view class="custom-setting-footer-url" @click="toChat(item)">{{
                      item.orgConfig.setting.object.url
                    }}
                  </view>
                  <view class="clip-icon" @click="clipboard(item.orgConfig.setting.object.url)">
                  </view>
                </view>
                <view class="flex-row custom-setting-footer" v-else>
                  <view class="custom-setting-footer-title">{{
                      item.orgConfig.setting.object.mode == 'PREMIUM' ? '专业客服' : '平台处理'
                    }}
                  </view>
                </view>
              </view>
              <view class="custom-operate" @click="unbind(item)">
                <view class="operate-btn">取消绑定</view>
              </view>
            </movable-view>
          </movable-area>
        </block>
      </view>
      <view class="custom-setting-list-empty" v-else>
        <view class="">暂无客服配置</view>
      </view>
    </view>
    <button open-type="contact" :session-from="sessionForm" send-message-title="测试">联系客服</button>
    <uni-popup ref="tipsPopup" :animation="false" :maskClick="false">
      <view class="popup-box">
        <view class="popup-content">
          <view class="tip-title">{{ mode == 'PLATFORM' ? '选择平台处理模式' : '自行处理' }}</view>
          <view class="tip-content" v-if="mode == 'PLATFORM'">
            <view class="">平台将自动处理客诉</view>
            <view class="shadow-box flex-row">
              <view class="shadow-box-icon"></view>
              <view>费用相关客诉，平台将自动退费</view>
            </view>
          </view>
          <view class="tip-content" v-else>
            <view class="">确定要绑定企业微信客服吗</view>
          </view>
          <view class="btn-box flex-row">
            <view class="btn-item" @click="$refs.tipsPopup.close()">取消</view>
            <view class="btn-item confirm-btn" @click="bindFunc">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="unBindTipsPopup" :animation="false" :maskClick="false">
      <view class="popup-box">
        <view class="popup-content">
          <view class="tip-title">解除绑定</view>
          <view class="tip-content">
            <view class="">确定要解绑企业微信客服吗</view>
          </view>
          <view class="btn-box flex-row">
            <view class="btn-item" @click="$refs.unBindTipsPopup.close()">取消</view>
            <view class="btn-item confirm-btn" @click="unbindFunc">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script src="./ally-customer-service.js">

</script>

<style lang="scss" scoped>
@import './ally-customer-service.scss'
</style>
