import {findCustomList, findOrgSetting, findSelfSetting,} from "@/subpkgProfit/api/allyCustomerService.js";
import {batchSetDirectKeyApi, setDirectKeyApi} from "@/api/common.js";
import {orgId, orgName} from "@/utils/role.js";
import {flatNodeList, sleep} from "@/utils/function.js";
import {toast} from "@/utils/common";
import {findOrgList} from "../../api/allyCustomerService";
import {parseGraphQLConnection} from "@/utils/graphql";

export default {
    data() {
        return {
            showSelect: true,
            initSetting: {},
            mode: "PLATFORM",
            variables: {},
            renderCount: 0,
            modeList: [
                {
                    text: "平台处理",
                    value: "PLATFORM",
                },
                {
                    text: "专业客服",
                    value: "PREMIUM",
                },
                {
                    text: "自行处理",
                    value: "SELF_MANAGED",
                },
            ],
            type: "ally",
            typeList: [
                {
                    text: "联盟商",
                    value: "ally",
                },
                {
                    text: "代理",
                    value: "agent",
                },
                {
                    text: "商户",
                    value: "storekeeper",
                },
                {
                    text: "门店",
                    value: "store",
                },
            ],
            selectOrgId: "",
            url: "",
            searchOrgName: "",
            list: [],
            customList: [],
            pageInfo: {},
            loadingList: false,

            unbindItem: {},
        };
    },
    async onLoad() {
        let res = await this.fetchSelfSetting();
        this.list = [{text: this.orgName, value: this.orgId}];
        this.customList = await this.fetchCustomList();
    },
    async onReachBottom() {
        if (!this.pageInfo.hasNextPage || this.loadingList) return;

        let list = await this.fetchCustomList();
        this.customList = [...this.customList, ...list];
    },
    computed: {
        orgId,
        orgName,
        getOrgTypeName() {
            switch (this.type) {
                case "ally":
                    return "联盟商";
                case "agent":
                    return "代理";
                case "storekeeper":
                    return "商户";
                case "store":
                    return "门店";
            }
        },
        sessionForm() {
            const { orgName, orgId } = this;
            const name = this.$store.state.user.name;
            const json = JSON.stringify({
                terminalSn: 1108211230227000216750
            })
            return `B端小程序|${name}|''|${json}`
        },
    },
    methods: {
        findOrgList,
        inputOrgName(v) {
            this.searchOrgName = v
        },
        async fetchSelfSetting() {
            let res = await findSelfSetting(this.orgId);
            let initSetting = res.data.findOrgDetailById.orgConfig.setting;

            if (initSetting.object && initSetting.object.mode) {
                this.mode = initSetting.object.mode;
                if (this.mode == "SELF_MANAGED") {
                    this.url = initSetting.object.url;
                    this.selectOrgId = this.orgId;
                }
            } else {
                if (initSetting.object && initSetting.object.url) {
                    this.mode = "SELF_MANAGED";
                } else {
                    this.mode = "PLATFORM";
                }
            }
            this.initSetting = initSetting;
        },
        async fetchFilterList(orgName) {
            const {type} = this;
            let tagIds;
            let orgType;
            if (type == "agent") {
                tagIds = "[3, 4, 5, 6]";
                orgType = "COMPANY";
            }
            if (type == "storekeeper") {
                tagIds = "[7, 8, 9, 10]";
                orgType = "STOREKEEPER";
            }
            if (type == "store") {
                tagIds = null;
                orgType = "STORE";
            }
            this.variables = {orgId: this.orgId, tagIds, orgType, fuzzySearch: orgName};
            const [list] = parseGraphQLConnection(await findOrgList(this.variables))
            if (type == "store") {
                list.forEach((item) => {
                    item.text = item.name;
                });
            }
            this.list = list;
        },
        async fetchCustomList() {
            this.loadingList = true;
            let res = await findCustomList(this.orgId);
            this.loadingList = false;
            return flatNodeList(res.data.findOrgDetailPaging.edges);
        },
        changeMode(item) {
            this.mode = item.value;
        },
        async changeType(type) {
            this.renderCount++;
            this.showSelect = false;

            this.selectOrgId = null;
            this.type = type;
            this.url = "";
            if (this.type == "ally") {
                // 联盟商时，返回自己
                this.list = [{text: this.orgName, value: this.orgId}];
                return;
            }

            this.$nextTick(() => {
                this.renderCount++; // 更新 key
                this.showSelect = true; // 重新挂载
            });

            this.fetchFilterList();
        },
        async changeSelectOrg(index, org) {
            this.selectOrgId = org.value;
            this.$modal.loading("加载中");
            let res = await findOrgSetting(this.selectOrgId);
            let customSetting = res.data.findOrgDetailById.orgConfig.setting;
            if (
                customSetting &&
                customSetting.object &&
                customSetting.object.mode == "SELF_MANAGED"
            ) {
                this.url = customSetting.object.url;
            } else {
                if (
                    customSetting &&
                    customSetting.object &&
                    !customSetting.object.mode &&
                    customSetting.object.url
                ) {
                    this.url = customSetting.object.url;
                } else {
                    this.url = "";
                }
            }
            this.$modal.closeLoading();
        },
        validate() {
            const {selectOrgId, url} = this;
            if (!selectOrgId) {
                toast("请选择组织");
                return;
            }
            if (!url) {
                toast("请填写链接");
                return;
            }
            if (url.indexOf("https://work.weixin.qq.com") != 0) {
                toast("客服链接有误");
                return;
            }
            return true;
        },
        bind() {
            if (!this.validate()) return;

            this.$refs.tipsPopup.open();
        },
        async bindFunc() {
            const {selectOrgId, url, mode} = this;

            let orgId = selectOrgId;
            let inputUrl = url.replace(/\s+/g, "");
            if (mode != "SELF_MANAGED") {
                // 不是自行设置时，说明是给组织设置;
                orgId = this.orgId;
                inputUrl = "";
            }

            this.$modal.loading("设置中");
            let setUrlRes = await setDirectKeyApi({
                mode,
                orgId,
                key: "CustomerServiceSettingDto",
                url: inputUrl,
            });
            if (this.orgId == orgId) {
                this.fetchSelfSetting();
            }
            await sleep(2000);
            this.url = "";
            this.$modal.msgSuccess("设置成功");
            this.$refs.tipsPopup.close();
            this.pageInfo = {};
            this.customList = await this.fetchCustomList();
        },
        unbind(item) {
            this.$refs.unBindTipsPopup.open();
            this.unbindItem = item;
        },
        async unbindFunc() {
            let item = this.unbindItem;
            this.$modal.loading("取消中");
            let setUrlRes = await batchSetDirectKeyApi({
                orgIds: [item.id],
                key: "CustomerServiceSettingDto",
                object: null,
            });
            this.$refs.unBindTipsPopup.close();
            await sleep(2000);
            this.$modal.msgSuccess("取消成功");

            this.pageInfo = {};
            this.customList = await this.fetchCustomList();
        },
        toChat(item) {
            wx.openCustomerServiceChat({
                extInfo: {url: item.orgConfig.setting.object.url},
                corpId: "ww3c8c7a923c73539f",
                success(res) {
                },
                fail(e) {
                    console.log(e, "e");
                    toast("调用失败");
                },
            });
        },
        toCompanyChat() {
            wx.openCustomerServiceChat({
                extInfo: {url: "https://work.weixin.qq.com/kfid/kfc79509319e90eb68e"},
                corpId: "ww3c8c7a923c73539f",
                success(res) {
                },
                fail(e) {
                    console.log(e, "e");
                    toast("调用失败");
                },
            });
        },
        clipboard(content) {
            uni.setClipboardData({
                data: content,
                success: () => {
                    uni.showToast({
                        title: "复制成功",
                        icon: "success",
                    });
                },
            });
        },
        swipeStart(index) {
            this.customList.forEach((item, i) => {
                if (index != i) this.$set(this.customList[i], "swipeX", 0);
            });
        },
        swipeEnd(index) {
            let This = this;
            this.$set(this.customList[index], "swipeX", this.customList[index].oldX);
            setTimeout(function () {
                if (This.customList[index].oldX < -10) {
                    This.$set(This.customList[index], "swipeX", -100);
                }

                if (This.customList[index].oldX > -55) {
                    This.$set(This.customList[index], "swipeX", 0);
                }
            }, 100);
        },
        onChange(e, index) {
            this.$set(this.customList[index], "oldX", e.detail.x);
        },
    },
};
