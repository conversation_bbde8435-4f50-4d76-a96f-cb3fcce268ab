<template>
	<view class="ally-area-device">
		<view class="ally-area-list-box">
			<view class="ally-area-title">{{ detail.areaName }}</view>
			<view class="ally-area-item flex-row-between" v-for="(item, key) in list" :key="item.id">
				<view class="ally-name">{{ item.companyFullName }}</view>
				<view class="ally-area-item-value">{{  item.activeTerminalSum }}台</view>
			</view>
		</view>
	</view>
</template>

<script src="./ally-area-device.js">
	
</script>

<style>
page {
  background-color: #000000;
  background: linear-gradient( 180deg, rgba(25,128,250,0.2) 0%, rgba(25,128,250,0.04) 100%), #000000;
}
</style>

<style lang="scss" scoped>
@import './ally-area-device.scss'
</style>
