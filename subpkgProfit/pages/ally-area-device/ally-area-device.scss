.ally-area-device {
  padding-top: 32rpx;
  padding-bottom: 20px;
  background-color: #000000;
  background: linear-gradient( 180deg, rgba(25,128,250,0.2) 0%, rgba(25,128,250,0.04) 100%), #000000;
}
.ally-area-list-box {
  margin: 0rpx 24rpx 0rpx;
  padding: 8rpx 32rpx;
  color: #fff;
  font-size: 28rpx;
  line-height: 40rpx;
  background: rgba(255,255,255,0.04);
  box-shadow: inset 0px 0px 0px 1px rgba(255,255,255,0.16), 0px 2px 4px 0px rgba(0,0,0,0.5);
  border-radius: 8px;
}

.ally-area-title {
  padding-top: 32rpx;
  padding-bottom: 24rpx;
  font-weight: bold;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.ally-area-item {
  padding: 24rpx 0;
  margin: 0 32rpx;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  &:nth-last-child(1) {
    border-bottom: none;
  }
  .ally-name {
    max-width: 420rpx;
  }
  .ally-area-item-value {
    color: rgba(255,255,255,0.75);
  }
}