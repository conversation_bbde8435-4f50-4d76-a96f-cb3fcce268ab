import { findDeviceList } from '@/subpkgProfit/api/ally-area-device.js';
import { orgId } from "@/utils/role";

export default {
  data() {
    return {
      detail: {},
      parentCode: '',
      list: [],
    };
  },
  async onLoad(options) {
		this.parentCode = options.parentCode;
		
    await this.fetchData();
	},
  computed: {
    orgId,
  },
  methods: {
    async fetchData() {
      let res = await findDeviceList({ orgId: this.orgId, parentCode: this.parentCode });

      this.detail = res.data.currentAreaOrgAndTerminal;
      this.list = res.data.currentAreaOrgAndTerminal.areaOrgDTOList;
    },
  },
}