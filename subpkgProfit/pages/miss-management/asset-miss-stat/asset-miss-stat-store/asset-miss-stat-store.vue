<template>
  <view>
    <view class="content">
      <view class="org-info">
        <view class="org-info-left">
          <view class="org-info-title">{{ storeDetail.name }}</view>
          <view class="org-info-address">
            <view class="org-info-address-name">{{ storeDetail.storeAddress.fullAddress }}</view>
            <view class="org-info-distance">
              <view class="org-info-distance-icon"></view>
              <view class="org-info-distance-data">{{ distance }}</view>
            </view>
          </view>
          <view class="org-info-phone">{{ storeDetail.manager.phone }}</view>
        </view>
        <view class="org-info-right"></view>
      </view>
      <view class="search-box">
        <view class="search-icon">
        </view>
        <view class="search-input">
          <view class="input-wrap">
            <uni-easyinput placeholder="请输入设备SN" @clear="clearSearch" v-model="inputSearchStr"/>
          </view>
          <view class="search-button" @click="search">搜索</view>
        </view>
        <!--        <view class="search-group-type-wrap">-->
        <!--          {{ tab === 1 ? '按失踪源' : tab === 2 ? '按责任人' : ' ' }}-->
        <!--        </view>-->
      </view>
      <view class="miss-content">
        <view class="miss-count">充电宝：{{ missStats.POWERBANK.length || 0 }}</view>
        <view class="miss-asset" v-for="(infoList, tmSn, index) of pbGroupByTmMap" :key="index">
          <view class="miss-asset-terminal-title">
            <view>机柜：</view>
            <view>{{ (!tmSn || tmSn == 'undefined') ? '未知' : tmSn }}</view>
          </view>
          <view class="miss-item-wrap">
            <view class="miss-pb-item" v-for="(pb, i) in infoList" :key="i" @click="toPbDetailPage(pb.tracking.sn)">
              <view class="miss-item-sn">
                <view class="miss-item-sn-left">
                  {{ pb.tracking.sn }}
                </view>
                <view class="miss-item-sn-right">
                </view>
              </view>
              <view>{{ "仓道" + (pb.tracking.position || '未知') }}</view>
              <view class="miss-date">
                <view class="miss-data-year">
                  {{ parseYear(pb.tracking.reportingAt) }}
                </view>
                <view class="miss-data-month-day">
                  {{ parseMonthDay(pb.tracking.reportingAt) }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";
import {toast} from "@/utils/common";
import {calcDistanceLL} from "@/utils/tool";
import {findMissTracking} from "@/subpkgProfit/api/asset-miss";
import {queryStoreDetail} from "@/subpkg/api/miss-management";
import {orgId} from "@/utils/role";

export default {
  data() {
    return {
      storeId: undefined,
      longitude: undefined,
      latitude: undefined,
      currentLongitude: undefined,
      currentLatitude: undefined,
      storeDetail: {},
      inputSearchStr: undefined,
      missStats: {
        POWERBANK: [],
        TERMINAL: [],
        CHARGE_CABLE: []
      },
    };
  },
  computed: {
    orgId,
    pbGroupByTmMap() {
      return this.missStats.POWERBANK.reduce((acc, pb) => {
        const key = pb.tracking.tmSn;
        acc[key] = acc[key] || [];
        acc[key].push(pb);
        return acc;
      }, {});
    },
    storeLongitude() {
      return this.storeDetail?.storeAddress?.longitude
    },
    storeLatitude() {
      return this.storeDetail?.storeAddress?.latitude
    },
    distance() {
      if (this.storeLatitude && this.storeLongitude && this.latitude && this.longitude) {
        const distance = calcDistanceLL(
            this.latitude,
            this.longitude,
            this.storeLatitude,
            this.storeLongitude
        );
        return distance.m < 1000 ? distance.m + "m" : distance.km.toFixed(2) + "km"
      }
      return 'loading...'
    },
  },
  async onLoad(option) {
    this.storeId = option?.storeId;
    await Promise.all([this.fetchStore(), this.getLocation()]);
    await this.fetchMissStat()
    console.log(this.missStats)
  },
  methods: {
    async fetchMissStat() {
      await Promise.all([this.fetchPosition('POWERBANK'), this.fetchPosition('TERMINAL'), this.fetchPosition('CHARGE_CABLE')])
    },
    async getLocation() {
      await uni.getLocation({
        type: "gcj02",
        success: ({latitude, longitude}) => {
          console.log(latitude, longitude)
          this.latitude = latitude;
          this.longitude = longitude;
        },
        fail: () => {
          toast("获取地理位置失败~");
        },
      });
    },
    async fetchStore() {
      const {data} = await queryStoreDetail({orgId: Number(this.storeId)})
      this.storeDetail = data?.findOrgDetailById
    },
    async fetchPosition(type) {
      const queryData = {keeperIds: Number(this.storeId), type, keeperTypes: ['MISS_AT_STORE'], first: 1000}
      if (this.inputSearchStr) {
        queryData.sns = this.inputSearchStr
      }
      const {data} = await findMissTracking(queryData);
      this.missStats[type] = data?.assetPosition.edges.map(edge => edge.node)
    },
    parseMonthDay(t) {
      return dayjs(t).format('MM/DD').toString()
    },
    parseYear(t) {
      return dayjs(t).format('YYYY').toString()
    },
    toPbDetailPage(sn) {
      uni.navigateTo({
        url: `/subpkg/pages/stock-powerbank-detail-new/stock-powerbank-detail-new?type=powerbank&powerbankSn=${sn}`
      })
    },
    async search() {
      await this.fetchMissStat()
    },
    async clearSearch() {
      this.inputSearchStr = ''
      await this.fetchMissStat()
    }
  }
}
</script>


<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.content {
  padding: 0 14px;

  .search-box {
    position: relative;
    margin: 12px 0;
    height: 44px;
    display: flex;

    .search-icon {
      position: absolute;
      background: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/search-16pt%403x-asset-miss.png") no-repeat;
      width: 16px;
      height: 16px;
      background-size: contain;
      left: 12px;
      top: 50%;

      transform: translateY(-50%);
    }

    .search-input {
      display: flex;
      background: #fff;
      border-radius: 8px 8px 8px 8px;
      padding: 4px;
      width: 100%;
      font-weight: 400;
      font-size: 14px;
      color: #B2B0AF;

      .search-button {
        flex-basis: 60px;
        flex-shrink: 0;
        background: #FFFFFF;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #EBE9E7;
        font-weight: 400;
        font-size: 14px;
        color: #2E2C2B;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 4px;
      }

      .input-wrap {
        background: #fafafa;
        width: 100%;
        padding-left: 28px;

        &::v-deep .uni-easyinput__content-input {
          background: #fafafa;
          border: none;
        }

        &::v-deep .is-input-border {
          background: #fafafa;
          border: none;
        }
      }
    }

    .search-group-type-wrap {
      width: 64px;
      font-weight: 400;
      display: flex;
      align-items: center;
      margin-left: 16px;
      font-size: 12px;
      color: #6F6A67;
    }
  }


  .org-info {
    margin-top: 12px;
    padding: 16px 16px 0;
    background: #fff;
    border-radius: 8px;
    display: flex;

    .org-info-left {
      .org-info-title {
        font-weight: 500;
        font-size: 16px;
        color: #2E2C2B;
      }

      .org-info-address {
        display: flex;
        width: 100%;
        justify-content: space-between;

        .org-info-address-name {
          font-weight: 400;
          font-size: 12px;
          color: #2E2C2B;
          margin: 12px 0;
          max-width: 2/3;
        }


        .org-info-distance {
          font-weight: 400;
          font-size: 12px;
          color: #B4B8C0;
          display: flex;
          align-items: center;
          margin-left: 13px;

          .org-info-distance-icon {
            background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/Slice%20240-map%403x.png");
            background-repeat: no-repeat;
            text-align: center;
            width: 16px;
            height: 16px;
            margin-right: 4px;
            background-size: contain;
          }
        }
      }

      .org-info-phone {
        font-weight: 400;
        font-size: 12px;
        color: #2E2C2B;
        padding-bottom: 16px;
      }
    }
  }

  .miss-content {
    background: #fff;
    min-height: fit-content;
    margin: 12px 0;
    border-radius: 8px;
    padding: 16px 16px 0;

    .miss-count {
      background: rgba(99, 48, 245, 0.1);
      padding: 2px 8px;
      width: fit-content;
      font-weight: 400;
      font-size: 13px;
      color: #6330F5;
      border-radius: 4px;
    }

    .miss-asset {
      padding-top: 12px;

      &:not(:last-child) {
        border-bottom: 1px solid #E1E1E1;
      }

      .miss-asset-terminal-title {
        display: flex;
        font-weight: 400;
        font-size: 14px;
        color: #6F6A67;
        margin-bottom: 12px;
      }
    }

    .miss-item-wrap {

      .miss-pb-item {
        display: flex;
        font-weight: 400;
        font-size: 12px;
        color: #2E2C2B;
        padding-bottom: 12px;
        align-items: center;
        justify-content: space-between;

        .miss-item-sn {
          display: flex;
          align-items: center;

          .miss-item-sn-left {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .miss-item-sn-right {
            background: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/arrow-16-right%403x-asset-miss%20%282%29.png") no-repeat;
            width: 16px;
            height: 16px;
            background-size: contain;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .miss-date {
          .miss-data-year, .miss-data-month-day {
            text-align: right;
          }
        }
      }
    }
  }
}
</style>
