<template>
  <view>

    <view class="content">
      <view class="top-box">
        <view class="top-box-inner">
          <view class="emp-name"> {{ emp.companyFullName ? emp.companyFullName : (emp.name || '未知组织') }}</view>
          <view class="miss-stat">
            <view class="miss-stat-left">失踪设备：</view>
            <view class="miss-stat-right"> {{ missCountInfo.expiredCount ? missCountInfo.expiredCount : 0 || missStats.POWERBANK.length }}</view>
          </view>
        </view>
      </view>

      <view class="miss-content" v-if="missStats.POWERBANK.length || missCountInfo && missCountInfo.expiredCount">
        <view class="miss-count">
          <span>失踪充电宝</span>
          <span class="miss-count-num">{{ missCountInfo.expiredCount ? missCountInfo.expiredCount : 0 || missStats.POWERBANK.length   }}</span>
        </view>
        <view class="miss-list-title">
          <view class="miss-list-title-item miss-list-title-store-name">设备SN</view>
          <view class="miss-list-title-item miss-list-title-time">丢失时间</view>
        </view>
        <view class="miss-asset" v-for="(pbInfo, index) of missStats.POWERBANK" :key="index">
          <view class="miss-item-wrap" @click="toPbDetailPage(pbInfo.sn)">
            <view class="miss-item-sn">
              <view class="miss-pb-item miss-item-sn-left">
                {{ pbInfo.sn }}
              </view>
              <view class="miss-item-sn-right">
              </view>
            </view>

            <view class="miss-pb-item miss-date">
              <view class="miss-data-year">
                {{ parseYear(pbInfo.tracking ? pbInfo.tracking.reportingAt : null) }}
              </view>
              <view class="miss-data-month-day">
                {{ parseMonthDay(pbInfo.tracking ? pbInfo.tracking.reportingAt : null) }}
              </view>

            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {queryPositionByID} from "@/api/subpkg/ally";
import {findMissTracking, findStockMiss} from "@/subpkgProfit/api/asset-miss";
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";
import {orgId} from "@/utils/role";
import {parseGraphQLConnection} from "@/utils/graphql";

export default {
  data() {
    return {
      empId: undefined,
      emp: {},
      missCountInfo: {},
      missStats: {
        POWERBANK: [],
        TERMINAL: [],
        CHARGE_CABLE: []
      },
      pageInfo: {}
    };
  },
  async onLoad(options) {
    this.empId = Number(options?.empId);
    this.emp = await this.fetchEmp(this.empId)
    this.missCountInfo = await this.fetchStockMiss() || {}
    this.missStats['POWERBANK'] = await this.fetchMissAsset(['POWERBANK'], ['MISS_AT_PERSONAL_HUB', 'MISS_AT_COMPANY_HUB'])
    this.missStats['TERMINAL'] = await this.fetchMissAsset(['TERMINAL'], ['MISS_AT_PERSONAL_HUB', 'MISS_AT_COMPANY_HUB'])
    this.missStats['CHARGE_CABLE'] = await this.fetchMissAsset(['CHARGE_CABLE'], ['MISS_AT_PERSONAL_HUB', 'MISS_AT_COMPANY_HUB'])
    console.log(this.missStats)
  },
  async onReachBottom() {
    console.log('reachBottom...')
    if (this.pageInfo['POWERBANK'].hasNextPage) {
      const res = await this.fetchMissAsset(['POWERBANK'], ['MISS_AT_PERSONAL_HUB', 'MISS_AT_COMPANY_HUB'])
      this.missStats['POWERBANK'] = [...this.missStats['POWERBANK'], ...res]
    } else {
      uni.showToast({
        icon: 'none',
        title: '已经到底了~'
      })
    }
  },
  computed: {
    orgId
  },
  methods: {
    async fetchEmp(empId) {
      const {data} = await queryPositionByID({id: empId})
      return data?.findOrgDetailById
    },
    async fetchStockMiss() {
      const conn = await findStockMiss({
        holderIds: this.emp.orgType === 'COMPANY' ? this.emp.id : this.orgId,
        keeperIds: this.empId,
        includeSub: true,
      })
      const [dataList, pageInfo, errors] = parseGraphQLConnection(conn)
      return dataList[0]
    },
    async fetchMissAsset(type, keeperTypes) {
      const queryData = {
        keeperIds: Number(this.empId),
        // holderIds: this.emp.orgType === 'COMPANY' ? this.emp.id : this.orgId,
        type,
        keeperTypes
      }
      if (this.pageInfo[type]?.endCursor) {
        queryData.after = this.pageInfo[type].endCursor
      }
      const conn = await findMissTracking(queryData);
      const [dataList, pageInfo] = parseGraphQLConnection(conn)

      this.pageInfo[type[0]] = pageInfo
      return dataList
    },
    parseMonthDay(t) {
      return t ? dayjs(t).format('MM/DD').toString() : '-'
    },
    parseYear(t) {
      return t ? dayjs(t).format('YYYY').toString() : '-'
    },
    toPbDetailPage(sn) {
      uni.navigateTo({
        url: `/subpkg/pages/stock-powerbank-detail-new/stock-powerbank-detail-new?type=powerbank&powerbankSn=${sn}`
      })
    },
  },
}
</script>

<style lang="scss">
* {
  box-sizing: border-box;
}

.content {
  padding: 0 14px 28px;

  .top-box {
    height: 66px;
    margin-top: 12px;
    background: #fff;
    border-radius: 8px;
    display: flex;
    align-items: center;

    .top-box-inner {
      height: 34px;
      display: flex;
      margin: 0 16px;
      justify-content: space-between;
      width: 100%;

      .emp-name {
        font-weight: 500;
        font-size: 16px;
        color: #2E2C2B;
        display: flex;
        align-items: center;
      }

      .miss-stat {
        display: flex;
        align-items: center;

        .miss-stat-left {
          font-weight: 400;
          font-size: 14px;
          color: #2E2C2B;
          margin-right: 8px
        }

        .miss-stat-right {
          font-weight: 600;
          font-size: 24px;
          background-image: linear-gradient(180deg, #9B5EFF 0%, #4615D5 100%);
          background-clip: text;
          color: transparent;
        }
      }
    }


  }

  .miss-content {
    background: #fff;
    margin: 12px 0;
    border-radius: 8px;
    padding: 16px 16px 0;

    .miss-list-title {
      margin-top: 12px;
      display: flex;

      .miss-list-title-item {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 13px;
        color: #B2B0AF;
        line-height: 15px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .miss-list-title-store-name {
        flex: 2;
        justify-content: start;
      }

      .miss-list-title-time {
        display: flex;
        justify-content: flex-end;
      }
    }


    .miss-count {
      font-weight: 500;
      font-size: 14px;
      color: #2E2C2B;
      display: flex;
      align-items: center;
      height: 29px;
      padding-bottom: 12px;
      border-bottom: 1px solid #EBE9E7;

      .miss-count-num {
        font-weight: 500;
        font-size: 14px;
        background-image: linear-gradient(180deg, #9B5EFF 0%, #4615D5 100%);
        background-clip: text;
        color: transparent;
        margin-left: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .miss-asset {
      padding-top: 12px;

      &:last-child {
        padding-bottom: 12px;
      }

      .miss-asset-terminal-title {
        display: flex;
        font-weight: 400;
        font-size: 14px;
        color: #6F6A67;
        margin-bottom: 12px;
      }
    }

    .miss-item-wrap {
      display: flex;
      justify-content: space-between;

      .miss-pb-item {
        display: flex;
        font-weight: 400;
        font-size: 12px;
        color: #2E2C2B;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .miss-item-sn {
          display: flex;
          align-items: center;

          .miss-item-sn-left {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .miss-item-sn-right {
            background: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/arrow-16-right%403x-asset-miss%20%282%29.png") no-repeat;
            width: 16px;
            height: 16px;
            background-size: contain;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .miss-date {
          .miss-data-hour-minute, .miss-data-month-day {
            text-align: right;
          }
        }
      }
    }
  }
}
.miss-item-sn {
  display: flex;
  align-items: center;

  .miss-item-sn-left {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .miss-item-sn-right {
    background: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/arrow-16-right%403x-asset-miss%20%282%29.png") no-repeat;
    width: 16px;
    height: 16px;
    background-size: contain;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
