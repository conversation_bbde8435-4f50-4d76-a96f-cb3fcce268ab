<template>
  <view>
    <view class="content">

      <view class="tab-box">
        <view class="tab-item-wrap" :class="{ 'tab-item-wrap-active': tab === 1 }">
          <view class="tab-item" :class="{ 'tab-item-active': tab === 1 }" @click="changeType(1)">运行失踪</view>
        </view>
        <view class="tab-item-wrap" :class="{ 'tab-item-wrap-active': tab === 2 }">
          <view class="tab-item" :class="{ 'tab-item-active': tab === 2 }" @click="changeType(2)">库存失踪</view>
        </view>
        <view class="tab-item-wrap" :class="{ 'tab-item-wrap-active': tab === 3 }">
          <view class="tab-item" :class="{ 'tab-item-active': tab === 3 }" @click="changeType(3)">人工失踪</view>
        </view>
      </view>

      <view class="all-stat">
        <view class="all-stat-left">
          <view class="all-stat-title">{{ tabDesc[tab] }}</view>
          <view class="all-stat-count">
            {{ tab === 1 ? workingMissCount : tab === 2 ? stockMissCount : manualMissCount }}
          </view>
        </view>
        <view class="all-stat-right">

        </view>
      </view>

      <view class="search-box">
        <view class="search-icon">
        </view>
        <view class="search-input">
          <view class="input-wrap">
            <uni-easyinput @clear="clearSearch" placeholder="请输入搜索内容" v-model="inputSearchStr"/>
          </view>
          <view class="search-button" @click="search">搜索</view>
        </view>
        <!--        <view class="search-group-type-wrap">-->
        <!--          {{ tab === 1 ? '按失踪源' : tab === 2 ? '按责任人' : ' ' }}-->
        <!--        </view>-->
      </view>

      <view class="miss-at-store-list miss-list" v-if="tab === 1">
        <view class="miss-list-title">
          <view class="miss-list-title-item miss-list-title-store-name">门店名</view>
          <view class="miss-list-title-item">门店管理人/BD</view>
          <view class="miss-list-title-item miss-list-title-count">失踪数量</view>
        </view>
        <view class="miss-list-item-wrap">
          <view class="miss-list-item" v-for="(row, i) in missList.missAtStore" :key="i"
                @click="navigateToStoreMissStat(row.orgDetail.id)">
            <view class="miss-list-org-name">{{
                ['POSITION', 'COMPANY'].includes(row.orgDetail ? row.orgDetail.orgType : '') ? '-' : row.orgDetail.name || '未知组织'
              }}
            </view>
            <view class="miss-list-org-manager">{{
                ['POSITION', 'COMPANY'].includes(row.orgDetail ? row.orgDetail.orgType : '') ? row.orgDetail.name : row.orgDetail.manager.name || '未知组织'
              }}
            </view>
            <view class="miss-list-asset-count">{{ row.missAtStoreCount }}</view>
          </view>
        </view>

      </view>

      <view class="miss-at-hub-list miss-list" v-if="tab === 2">
        <view class="miss-list-title">
          <view class="miss-list-title-item miss-list-title-store-name">库</view>
          <view class="miss-list-title-item miss-list-title-count">失踪数量</view>
        </view>
        <view class="miss-list-item-wrap">
          <view class="miss-list-item" v-for="(row, i) in missList.missAtHub" :key="i"
                @click="navigateToPersonalHubMissStat(row.org)">
            <view class="miss-list-org-name">{{ row.org ? row.org.companyFullName || row.org.name : '' }}</view>
            <view class="miss-list-asset-count">{{ row.expiredCount }}</view>
          </view>
        </view>

      </view>
    </view>
  </view>
</template>

<script>
import {findStockMiss, findStoreMiss} from "@/subpkgProfit/api/asset-miss";
import {orgId, isAlly} from "@/utils/role";
import {queryOrg} from "@/api/subpkg/ally";
import {queryOrgWithAssetStats} from "@/subpkg/api/miss-management";
import {parseGraphQLConnection} from '@/utils/graphql'

export default {
  data() {
    return {
      tab: 1,
      tabDesc: {
        1: '运行失踪',
        2: '库存失踪',
        3: '人工失踪'
      },
      type: 'POWERBANK',
      stats: {
        POWER_BANK: {
          count: {
            current: {
              countAll: 0,
              countLost: 0,
              countLostByEndOrder: 0,
              countLostByRefund: 0,
              countMiss: 0,
              countMissAtCompanyHub: 0,
              countMissAtPersonalHub: 0,
              countMissAtRepairHub: 0,
              countMissAtRootHub: 0,
              countMissAtStore: 0,
              countMissAtUser: 0,
              countRented: 0,
              countSold: 0,
              countStock: 0,
              countStockCompany: 0,
              countStockPersonal: 0,
              countStockRepair: 0,
              countStockRoot: 0,
              countSuspendByCompanyReport: 0,
              countWorking: 0
            },
            todayIncrement: {
              countAll: 0,
              countWorking: 0,
              countStockRoot: 0,
              countStockCompany: 0,
              countStockPersonal: 0,
              countStockRepair: 0,
              countRented: 0,
              countSold: 0,
              countMissAtRootHub: 0,
              countMissAtCompanyHub: 0,
              countMissAtStore: 0,
              countMissAtPersonalHub: 0,
              countMissAtUser: 0,
              countMissAtRepairHub: 0,
              countLostByEndOrder: 0,
              countLostByRefund: 0,
              countSuspendByCompanyReport: 0,
              countStock: 0,
              countMiss: 0,
              countLost: 0,
              countEquity: 0,
              countFailure: 0,
              countFatal: 0,
              countInbound: 0,
              utilityRate: 0,
            }
          }
        },
        TERMINAL: {
          count: {}
        }
      },
      pageInfo: {
        missAtStore: {},
        missAtHub: {}
      },
      missList: {
        missAtStore: [],
        missAtHub: []
      },
      inputSearchStr: '',
      searchResult: undefined,
    };
  },
  computed: {
    orgId,
    isAlly,
    workingMissCount() {
      return this.stats.POWER_BANK.count.current.countMissAtStore
    },
    stockMissCount() {
      return this.stats.POWER_BANK.count.current.countMissAtCompanyHub + this.stats.POWER_BANK.count.current.countMissAtPersonalHub
    },
    manualMissCount() {
      return this.stats.POWER_BANK.count.current.countLostByRefund + this.stats.POWER_BANK.count.current.countLostByEndOrder
    }
  },
  onLoad: async function (option) {
    console.log(option.tab)
    await this.initMissToHandle()
    this.tab = Number(option?.tab) || 1
    await this.initTab(this.tab)
  },
  onReachBottom() {
    this.loadMore(this.tab)
  },
  methods: {
    async search() {
      let orgTypes = []
      if (this.tab === 1) {
        orgTypes = ['STORE']
      } else if (this.tab === 2) {
        orgTypes = ['COMPANY', 'POSITION']
      }
      this.searchResult = []
      this.resetPageInfo()
      this.resetMissList()
      if (this.inputSearchStr.trim()) {
        await this.searchOrg(this.inputSearchStr, orgTypes)
      } else {
        await this.initTab(this.tab)
        return
      }
      if (this.searchResult?.length) {
        switch (this.tab) {
          case 1: {
            this.missList.missAtStore = await this.findStoreMiss()
            break;
          }
          case 2: {
            this.missList.missAtHub = await this.findHubMiss()
          }
        }
      }
    },
    resetPageInfo() {
      for (let pageInfoKey in this.pageInfo) {
        this.pageInfo[pageInfoKey] = {}
      }
    },
    resetMissList() {
      for (let missListKey in this.missList) {
        this.missList[missListKey] = []
      }
    },
    async initMissToHandle() {
      const {framework} = await this.getOrgAssetStats()
      this.initCurrentAssetStats(framework)
      // this.initTodayIncrementStats(framework)
    },
    async getOrgAssetStats() {
      let scope;
      if (this.isAlly) scope = 'ALLY_SELF';
      else scope = 'AGENT';
      const {data} = await queryOrgWithAssetStats({orgId: this.orgId, scope})
      console.log(data)
      return data.findOrgDetailById
    },
    initCurrentAssetStats(f) {
      const {countPowerbank} = f
      this.stats.POWER_BANK.count.current = countPowerbank
    },
    initTodayIncrementStats(f) {
      const {countPowerbank} = f.dateRange.countNewAsset
      this.stats.POWER_BANK.count.todayIncrement = countPowerbank
    },
    async findStoreMiss() {
      const queryData = {
        holderIds: this.orgId,
        after: this.pageInfo.missAtStore?.endCursor,
        type: this.type,
        includeSub: true
      }
      queryData['keeperIds'] = this.searchResult?.map(item => item.id)
      const [list, pageInfo] = parseGraphQLConnection(await findStoreMiss(queryData))
      this.pageInfo.missAtStore = pageInfo
      return list
    },
    async findHubMiss() {
      const queryData = {
        holderIds: this.orgId,
        after: this.pageInfo.missAtHub?.endCursor,
        type: this.type,
        includeSub: true
      }
      queryData['keeperIds'] = this.searchResult?.map(item => item.id)
      let {data} = await findStockMiss(queryData)
      const {edges, pageInfo} = data.missAtHubAssetsStats
      this.pageInfo.missAtHub = pageInfo
      return edges.map(edge => edge.node)
    },
    async initTab(tab) {
      this.searchResult = []
      for (let pageInfoKey in this.pageInfo) {
        this.pageInfo[pageInfoKey] = {}
      }
      switch (tab) {
        case 1: {
          this.missList.missAtStore = await this.findStoreMiss()
          break;
        }
        case 2: {
          this.missList.missAtHub = await this.findHubMiss()
        }
      }
    },
    async changeType(tab) {
      this.tab = tab
      this.inputSearchStr = ''
      this.searchResult = []
      for (let key in this.missList) {
        this.missList[key] = []
      }
      await this.initTab(this.tab)
    },
    async loadMore(tab) {
      console.log(tab, this.pageInfo)
      switch (tab) {
        case 1: {
          if (this.pageInfo.missAtStore.hasNextPage) {
            this.missList.missAtStore = [...this.missList.missAtStore, ...await this.findStoreMiss()]
          }
          break;
        }
        case 2: {
          if (this.pageInfo.missAtHub.hasNextPage) {
            this.missList.missAtHub = [...this.missList.missAtHub, ...await this.findHubMiss()]
          }
        }
      }
    },
    async searchOrg(orgName, orgTypes) {
      orgName = orgName?.trim()
      const conn = await queryOrg({
        orgId: this.orgId,
        fuzzySearch: orgName,
        routeTypes: ['PARENT', 'DIRECT_SALE_GOLD_ALLY', 'DIRECT_GOLD_ALLY', 'ASSIST_GOLD_ALLY', 'AGENT_CHANNEL_ALLY', 'AGENT_PROVIDER_ALLY', 'AGENT_INVESTOR_ALLY', 'AGENT_PARTNER_ALLY', 'DIRECT', 'ASSIST', 'INDIRECT'],
        orgTypes
      })
      const [list] = parseGraphQLConnection(conn)
      this.searchResult = list.map(item => ({...item, id: Number(item.id)}))
    },
    clearSearch() {
      this.initTab(this.tab)
    },
    navigateToStoreMissStat(storeId) {
      console.log("navigateToStoreMissStat, storeId", storeId);
      uni.navigateTo({url: `/subpkgProfit/pages/miss-management/asset-miss-stat/asset-miss-stat-store/asset-miss-stat-store?storeId=${storeId}`})
    },
    navigateToPersonalHubMissStat(org) {
      if (!org) return;
      console.log("navigateToPersonalHubMissStat, empId", org.id);
      uni.navigateTo({url: `/subpkgProfit/pages/miss-management/asset-miss-stat/asset-miss-stat-personal/asset-miss-stat-personal?empId=${org.id}`})
    }
  },
}
</script>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}

.content {
  padding: 0 14px 14px;

  .tab-box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    padding: 2px;
    background-color: #EBEBEB;
    box-sizing: border-box;
    border-radius: 6px;
    margin: 12px auto;

    .tab-item-wrap-active {
      background-color: #fff;
      border-radius: 4px;
    }

    .tab-item-wrap {
      flex: 1;
    }

    .tab-item {
      height: 28px;
      color: #979494;
      line-height: 28px;
      font-size: 14px;
      letter-spacing: 1rpx;
      text-align: center;
    }

    .tab-item-active {
      background: linear-gradient(180deg, #9B5EFF, #4615D5);
      background-clip: text;
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.04);
      border-radius: 4px 4px 4px 4px;
      font-weight: bold;
      color: transparent;
    }
  }

  .all-stat {
    height: 96px;
    border-radius: 8px;
    background: #fff;
    margin-bottom: 12px;
    display: flex;

    .all-stat-left {
      flex: 1;
      margin: 16px 0 16px 16px;

      .all-stat-title {
        font-weight: 500;
        font-size: 16px;
        color: #2E2C2B;
        margin-bottom: 8px;
      }

      .all-stat-count {
        font-weight: 600;
        font-size: 24px;
        color: transparent;
        line-height: 28px;

        background-clip: text;
        -webkit-background-clip: text;

        background-image: linear-gradient(180deg, #9B5EFF 0%, #4615D5 100%);

        border-radius: 0;
      }
    }

    .all-stat-right {
      flex: 1;
      margin: 16px 16px 16px 0;
    }
  }

  .search-box {
    position: relative;
    margin: 12px 0;
    height: 44px;
    display: flex;

    .search-icon {
      position: absolute;
      background: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/search-16pt%403x-asset-miss.png") no-repeat;
      width: 16px;
      height: 16px;
      background-size: contain;
      left: 12px;
      top: 50%;

      transform: translateY(-50%);
    }

    .search-input {
      display: flex;
      background: #fff;
      border-radius: 8px 8px 8px 8px;
      padding: 4px;
      width: 100%;
      font-weight: 400;
      font-size: 14px;
      color: #B2B0AF;

      .search-button {
        flex-basis: 60px;
        flex-shrink: 0;
        background: #FFFFFF;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #EBE9E7;
        font-weight: 400;
        font-size: 14px;
        color: #2E2C2B;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 4px;
      }

      .input-wrap {
        background: #fafafa;
        width: 100%;
        padding-left: 28px;

        &::v-deep .uni-easyinput__content-input {
          background: #fafafa;
          border: none;
        }

        &::v-deep .is-input-border {
          background: #fafafa;
          border: none;
        }
      }
    }

    .search-group-type-wrap {
      width: 64px;
      font-weight: 400;
      display: flex;
      align-items: center;
      margin-left: 16px;
      font-size: 12px;
      color: #6F6A67;
    }
  }


  .miss-list {
    border-radius: 8px 8px 8px 8px;
    background: #fff;
    margin-bottom: 12px;

    .miss-list-title {
      display: flex;
      background-color: #fafafa;
      border-radius: 8px 8px 0 0;
      padding: 12px 16px;

      .miss-list-title-item {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 400;
        font-size: 13px;
        color: #B2B0AF;
        line-height: 15px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        white-space: nowrap;
      }

      .miss-list-title-store-name {
        flex: 2;
        justify-content: start;
      }

      .miss-list-title-count {
        display: flex;
        justify-content: flex-end;
      }
    }

    .miss-list-item-wrap {

      //margin:0 12px;
      .miss-list-item {
        padding: 12px 0;
        display: flex;
        font-size: 13px;
        font-weight: 400;
        margin: 0 16px;
        border-bottom: 1px solid #EBEBEB;
        color: #2E2C2B;
        line-height: 15px;

        &:last-child {
          border-bottom: none;
        }

        .miss-list-org-name {
          flex: 2;
          color: #2E2C2B;
          line-height: 15px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          height: fit-content;
        }

        .miss-list-asset-count {
          flex: 1;
          display: flex;
          flex-flow: row-reverse;
          align-items: center;
        }

        .miss-list-org-manager {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

  }
}
</style>
