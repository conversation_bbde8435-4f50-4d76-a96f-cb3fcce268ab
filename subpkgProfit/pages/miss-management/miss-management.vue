<template>
  <view>
    <view class="content">
      <view class="tab-box">
        <view class="tab-item-wrap" :class="{'tab-item-wrap-active': tab === 1}">
          <view class="tab-item" :class="{'tab-item-active': tab === 1}" @click="changeType(1)">失踪待处理</view>
        </view>
        <view class="tab-item-wrap" :class="{'tab-item-wrap-active': tab === 2}">
          <view class="tab-item" :class="{'tab-item-active': tab === 2}" @click="changeType(2)">丢失设备</view>
        </view>
        <view class="tab-item-wrap" :class="{'tab-item-wrap-active': tab === 3}">
          <view class="tab-item" :class="{'tab-item-active': tab === 3}" @click="changeType(3)">赔付与找回</view>
        </view>
      </view>

      <!--     tab1 失踪待处理-->
      <!--      充电宝-->
      <view class="miss-box" v-if="tab === 1">
          <view class="miss-box-item">
            <view class="miss-title">充电宝</view>
            <view class="miss-content">
              <view class="miss-unprocessed">
                <view class="miss-unprocessed-title primary-black">待处理:</view>
                <view class="miss-unprocessed-count">{{ pbSummariesMap.get(STAT_MODE.CURRENT) }}</view>
              </view>
              <view class="miss-sub-count">
                <view class="category-miss working-miss">
                  <view class="category-miss-title-wrap">
                    <view class="category-miss-title">运行失踪</view>
                    <view class="category-miss-intro"
                          @click.stop="showDescModel(0)">
                    </view>
                  </view>
                  <view @click="toMissStat(1)">
                    <view class="category-miss-count-wrap">
                      <view class="category-miss-count">{{ stats.POWER_BANK.count.current.countMissAtStore }}</view>
                      <view class="category-miss-right-icon"></view>
                    </view>
                    <view class="category-miss-today" v-if="false">
                      <view class="category-miss-today-title">今日:</view>
                      <view class="category-miss-today-count">
                        {{ formatNumber(stats.POWER_BANK.count.todayIncrement.countMissAtStore) }}
                      </view>
                    </view>
                  </view>

                </view>
                <view class="category-miss stock-miss">
                  <view class="category-miss-title-wrap">
                    <view class="category-miss-title">库存失踪</view>
                    <view class="category-miss-intro"
                          @click="showDescModel(1)"></view>
                  </view>
                  <view @click.stop="toMissStat(2)">
                    <view class="category-miss-count-wrap">
                      <view class="category-miss-count">
                        {{
                          stats.POWER_BANK.count.current.countMissAtCompanyHub + stats.POWER_BANK.count.current.countMissAtPersonalHub
                        }}
                      </view>
                      <view class="category-miss-right-icon"></view>
                    </view>
                    <view class="category-miss-today" v-if="false">
                      <view class="category-miss-today-title">今日:</view>
                      <view class="category-miss-today-count">
                        {{
                          formatNumber(stats.POWER_BANK.count.todayIncrement.countMissAtCompanyHub + stats.POWER_BANK.count.todayIncrement.countMissAtPersonalHub)
                        }}
                      </view>
                    </view>
                  </view>
                </view>
                <view class="category-miss manual-miss" @click="toMissStat(3)">
                  <view class="category-miss-title-wrap">
                    <view class="category-miss-title">人工失踪</view>
                    <view class="category-miss-intro"
                          @click.stop="showDescModel(2)"></view>
                  </view>
                  <view>
                    <view class="category-miss-count-wrap">
                      <view class="category-miss-count">
                        {{
                          stats.POWER_BANK.count.current.countLostByEndOrder + stats.POWER_BANK.count.current.countLostByRefund
                        }}
                      </view>
                      <view class="category-miss-right-icon"></view>
                    </view>
                    <view class="category-miss-today" v-if="false">
                      <view class="category-miss-today-title">今日:</view>
                      <view class="category-miss-today-count">
                        {{
                          formatNumber(stats.POWER_BANK.count.todayIncrement.countLostByEndOrder + stats.POWER_BANK.count.todayIncrement.countLostByRefund)
                        }}
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>



        <view v-if="false">
          <view class="miss-title" >机柜</view>
          <view class="miss-content">
            <view class="miss-unprocessed">
              <view class="miss-unprocessed-title primary-black">待处理:</view>
              <view class="miss-unprocessed-count">{{ pbSummariesMap.get(STAT_MODE.CURRENT) }}</view>
            </view>
            <view class="miss-sub-count">
              <view class="category-miss working-miss">
                <view class="category-miss-title-wrap">
                  <view class="category-miss-title">运行失踪</view>
                  <view class="category-miss-intro"></view>
                </view>
                <view class="category-miss-count-wrap">
                  <view class="category-miss-count">{{ stats.POWER_BANK.count.current.countMissAtStore }}</view>
                  <view class="category-miss-right-icon" @click="toMissStat(1)"></view>
                </view>
                <view class="category-miss-today" v-if="false">
                  <view class="category-miss-today-title">今日:</view>
                  <view class="category-miss-today-count">
                    {{ formatNumber(stats.POWER_BANK.count.todayIncrement.countMissAtStore) }}
                  </view>
                </view>
              </view>
              <view class="category-miss stock-miss">
                <view class="category-miss-title-wrap">
                  <view class="category-miss-title">库存失踪</view>
                  <view class="category-miss-intro"></view>
                </view>
                <view class="category-miss-count-wrap">
                  <view class="category-miss-count">
                    {{
                      stats.POWER_BANK.count.current.countMissAtCompanyHub + stats.POWER_BANK.count.current.countMissAtPersonalHub
                    }}
                  </view>
                  <view class="category-miss-right-icon" @click="toMissStat(2)"></view>
                </view>
                <view class="category-miss-today" v-if="false">
                  <view class="category-miss-today-title">今日:</view>
                  <view class="category-miss-today-count">
                    {{
                      formatNumber(stats.POWER_BANK.count.todayIncrement.countMissAtCompanyHub + stats.POWER_BANK.count.todayIncrement.countMissAtPersonalHub)
                    }}
                  </view>
                </view>

              </view>
              <view class="category-miss manual-miss">
                <view class="category-miss-title-wrap">
                  <view class="category-miss-title">人工失踪</view>
                  <view class="category-miss-intro"></view>
                </view>
                <view class="category-miss-count-wrap">
                  <view class="category-miss-count">
                    {{
                      stats.POWER_BANK.count.current.countLostByEndOrder + stats.POWER_BANK.count.current.countLostByRefund
                    }}
                  </view>
                  <view class="category-miss-right-icon" @click="toMissStat(3)"></view>
                </view>
                <view class="category-miss-today" v-if="false">
                  <view class="category-miss-today-title">今日:</view>
                  <view class="category-miss-today-count">
                    {{
                      formatNumber(stats.POWER_BANK.count.todayIncrement.countLostByEndOrder + stats.POWER_BANK.count.todayIncrement.countLostByRefund)
                    }}
                  </view>
                </view>

              </view>
            </view>
          </view>
        </view>

      </view>

      <!--      tab2 丢失设备-->
      <view class="lost-box" v-else-if="tab === 2">
        功能开发中...
      </view>

      <!--      tab3 赔付/找回-->
      <view class="hold-accountable" v-else>
        功能开发中...
      </view>
    </view>


    <!--    提示-->
    <view class="tips">
      <uni-popup ref="tipsPopup" :animation="false" :maskClick="false">
        <view class="popup-box">
          <view class="popup-content" v-if="popUpContentOpen[0]">
            <view class="tip-title">运行失踪说明</view>
            <view class="tip-content">
              <view class="">机柜中的充电宝，在无借出及弹宝动作的情况下，下次上报时无故消失</view>
            </view>
            <view class="tip-btn" @click="closePopup">关闭</view>
          </view>
          <view class="popup-content" v-if="popUpContentOpen[1]">
            <view class="tip-title">库存失踪说明</view>
            <view class="tip-content">
              <view class="">库存中的充电宝，超过30天未使用</view>
            </view>
            <view class="tip-btn" @click="closePopup">关闭</view>
          </view>
          <view class="popup-content" v-else-if="popUpContentOpen[2]">
            <view class="tip-title">人工失踪说明</view>
            <view class="tip-content">
              <view class="">租借中/已售出的宝尚未归还</view>
              <view class="">客服结束订单/进行退款</view>
            </view>
            <view class="tip-btn" @click="closePopup">关闭</view>
          </view>
        </view>
      </uni-popup>
    </view>


  </view>


</template>

<script>
import {orgId, isAlly} from "@/utils/role";
import {queryOrgWithAssetStats} from "@/subpkg/api/miss-management";
import {findStockMiss} from "@/subpkgProfit/api/asset-miss";
import {parseGraphQLConnection} from "@/utils/graphql";

const MISS_COUNT_FIELDS = [
  'countLostByEndOrder',
  'countLostByRefund',
  'countMissAtCompanyHub',
  'countMissAtPersonalHub',
  'countMissAtStore'
]

export default {
  data() {
    return {
      tab: 1,
      STAT_MODE: Object.freeze({
        CURRENT: '总计',
        TODAY_INCREMENT: "今日新增",
      }),
      popUpContentOpen: {
        0: false, 1: false, 2: false
      },
      stats: {
        POWER_BANK: {
          count: {
            current: {
              countAll: 0,
              countLost: 0,
              countLostByEndOrder: 0,
              countLostByRefund: 0,
              countMiss: 0,
              countMissAtCompanyHub: 0,
              countMissAtPersonalHub: 0,
              countMissAtRepairHub: 0,
              countMissAtRootHub: 0,
              countMissAtStore: 0,
              countMissAtUser: 0,
              countRented: 0,
              countSold: 0,
              countStock: 0,
              countStockCompany: 0,
              countStockPersonal: 0,
              countStockRepair: 0,
              countStockRoot: 0,
              countSuspendByCompanyReport: 0,
              countWorking: 0
            },
            todayIncrement: {
              countAll: 0,
              countWorking: 0,
              countStockRoot: 0,
              countStockCompany: 0,
              countStockPersonal: 0,
              countStockRepair: 0,
              countRented: 0,
              countSold: 0,
              countMissAtRootHub: 0,
              countMissAtCompanyHub: 0,
              countMissAtStore: 0,
              countMissAtPersonalHub: 0,
              countMissAtUser: 0,
              countMissAtRepairHub: 0,
              countLostByEndOrder: 0,
              countLostByRefund: 0,
              countSuspendByCompanyReport: 0,
              countStock: 0,
              countMiss: 0,
              countLost: 0,
              countEquity: 0,
              countFailure: 0,
              countFatal: 0,
              countInbound: 0,
              utilityRate: 0,
            }
          }
        },
        TERMINAL: {
          count: {}
        }
      },
      pageInfo: {
        findStockMiss: {}
      }
    };
  },
  onShow() {
    this.initMissToHandle()
  },
  watch: {},
  computed: {
    orgId,
    isAlly,
    pbSummariesMap() {
      const {STAT_MODE} = this
      const incrMap = new Map()
      const current = MISS_COUNT_FIELDS.reduce((p, c, i) => p + this.stats.POWER_BANK.count.current[c], 0)
      const todayIncrement = MISS_COUNT_FIELDS.reduce((p, c, i) => p + this.stats.POWER_BANK.count.todayIncrement[c], 0)
      incrMap.set(STAT_MODE.CURRENT, current)
      incrMap.set(STAT_MODE.TODAY_INCREMENT, todayIncrement)
      return incrMap
    }
  },
  methods: {
    async initMissToHandle() {
      const {framework} = await this.getOrgAssetStats()
      this.initCurrentAssetStats(framework)
      // this.initTodayIncrementStats(framework)
    },
    initCurrentAssetStats(f) {
      const {countPowerbank} = f
      this.stats.POWER_BANK.count.current = countPowerbank
    },
    initTodayIncrementStats(f) {
      const {countPowerbank} = f.dateRange.countNewAsset
      this.stats.POWER_BANK.count.todayIncrement = countPowerbank
    },
    async getOrgAssetStats() {
      let scope;
      if (this.isAlly) scope = 'ALLY_SELF';
      else scope = 'AGENT';
      const {data} = await queryOrgWithAssetStats({orgId: this.orgId, scope})
      console.log(data)
      return data.findOrgDetailById
    },

    async initLost() {
      await this.fetchStockMissList()
    },

    async fetchStockMissList() {
      const {data} = await findStockMiss({holderIds: this.orgId})
      console.log(data)

    },
    changeType(tab) {
      this.tab = tab
      switch (tab) {
        case 1: {
          this.initMissToHandle();
          break;
        }
        case 2: {
          this.initLost();
          break;
        }
      }
    },
    formatNumber(n) {
      return n > 0 ? `+${n}` : n < 0 ? `-${n}` : 0
    },

    toMissStat(tab) {
      uni.navigateTo({url: `/subpkgProfit/pages/miss-management/asset-miss-stat/asset-miss-stat?tab=${tab}`})
    },
    showDescModel(num) {
      this.popUpContentOpen[num] = true
      console.log(this.popUpContentOpen)
      this.$refs.tipsPopup.open('center')
    },
    closePopup() {
      for (let key in this.popUpContentOpen) {
        this.popUpContentOpen[key] = false
      }
      console.log(this.popUpContentOpen)
      this.$refs.tipsPopup.close()
    }
  },
}
</script>

<style scoped lang="scss">
* {
  box-sizing: border-box;
}

.content {
  padding: 0 14px;

  .tab-box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    padding: 2px;
    background-color: #EBEBEB;
    box-sizing: border-box;
    border-radius: 6px;
    margin: 12px auto;

    .tab-item-wrap-active {
      background-color: #fff;
      border-radius: 4px;
    }

    .tab-item-wrap {
      flex: 1;
    }

    .tab-item {
      height: 28px;
      color: #979494;
      line-height: 28px;
      font-size: 14px;
      letter-spacing: 1rpx;
      text-align: center;
    }

    .tab-item-active {
      background: linear-gradient(180deg, #9B5EFF, #4615D5);
      background-clip: text;
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.04);
      border-radius: 4px 4px 4px 4px;
      font-weight: bold;
      color: transparent;
    }
  }

  //tab1
  .miss-box {

    .miss-box-item {
      display: flex;
      height: 103.5px;
      .miss-title {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 28px;
        background: linear-gradient(0deg, #9B5EFF 0%, #4615D5 100%);
        writing-mode: vertical-lr;
        font-weight: 500;
        font-size: 12px;
        color: #FFFFFF;
        line-height: 28px;
        border-radius: 8px 0 0 8px;
      }

      .miss-content {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;

        background-color: #fff;
        padding: 0 16px;
        width: 100%;

        .miss-unprocessed {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #EBE9E7;
          padding-bottom: 8px;
          padding-top: 12px;
          height: 20px;

          .miss-unprocessed-title {
            margin-right: 11px;
            display: flex;
            align-items: center;
          }

          .miss-unprocessed-count {
            display: flex;
            align-items: center;
            font-size: 14px;
            background: linear-gradient(180deg, #9B5EFF 0%, #4615D5 100%);
            background-clip: text;
            color: transparent;
            font-weight: 500;
          }
        }
      }

      .miss-sub-count {
        display: flex;
        margin-top: 8px;

        .category-miss {
          flex: 1;

          .category-miss-title-wrap {
            display: flex;

            .category-miss-title {
              font-size: 13px;
              color: #6F6A67;
              font-weight: 400;
            }

            .category-miss-intro {
              width: 16px;
              height: 16px;
              display: flex;
              justify-content: center;
              align-items: center;
              background: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-16-asset-miss%402x.png") no-repeat center;
              background-size: contain;
            }
          }

          .category-miss-count-wrap {
            margin-top: 4px;
            display: flex;
            align-items: center;

            .category-miss-count {
              height: 20px;
              font-weight: 500;
              font-size: 14px;
              color: #2E2C2B;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .category-miss-right-icon {
              width: 16px;
              height: 16px;
              background: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/arrow-16-right%403x-asset-miss.png") no-repeat center;
              background-size: contain;
            }
          }

          .category-miss-today {
            margin-top: 4px;
            display: flex;

            .category-miss-today-title {
              height: 17px;
              font-weight: 400;
              font-size: 12px;
              color: #B2B0AF;
              line-height: 14px;
            }

            .category-miss-today-count {
              width: 20px;
              height: 17px;
              font-weight: 400;
              font-size: 12px;
              color: #FF3B30;
              line-height: 14px;
              text-align: center;
              font-style: normal;
              text-transform: none;
            }
          }


        }


      }
    }


  }

  //tab2
  .pb-lost-box {
    height: 96px;
    background: #fff;
    border-radius: 8px;
  }
}

.primary-black {
  font-size: 14px;
  font-weight: 500;
  color: #2E2C2B;
}

.popup-box {
  width: 638rpx;
  padding-top: 16px;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;

  .tip-title {
    margin-bottom: 16px;
    font-size: 32rpx;
    line-height: 44rpx;
    font-weight: bold;
    color: #2E2C2B;
    letter-spacing: 1rpx;
    text-align: center;
  }

  .tip-content {
    margin-bottom: 16px;
    text-align: center;
    font-size: 14px;
    line-height: 20px;
    color: #2E2C2B;
    letter-spacing: 1rpx;
    padding: 0 12px;
  }

  .shadow-box {
    padding: 8px 0;
    margin: 12px 32rpx 16px;
    background: #FAFAFA;
    border-radius: 4px;
    color: #6F6A67;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 1rpx;
  }

  .tip-btn {
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background: linear-gradient(180deg, #FAFAFA 0%, #FFFFFF 100%);
    color: #6F6A67;
    font-size: 14px;
    letter-spacing: 1px;
  }
}

</style>
