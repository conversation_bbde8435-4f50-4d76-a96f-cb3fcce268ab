<template>
	<view class="profit-analyze-list">
		<mNav title="收益分析" titleColor="#fff" :isShowBack="true" topBarColor="transparent" statusBarColor="transparent" bgImage="https://oss-wukong-web.oss-cn-shenzhen.aliyuncs.com/test/subpkgProfit-profit-analyze-list-bg.png" />
		<view class="search-box" :style="{'--nav-height': -navHeight + 'px', 'top': navHeight + 'px'}">
			<view class="search-box-content">
				<view class="time-tab flex-row">
					<view class="time-tab-item" :class="{'time-tab-item-active': timeType == 1}" @click="changeTimeType(1)">按日</view>
					<view class="time-tab-item" :class="{'time-tab-item-active': timeType == 2}" @click="changeTimeType(2)">按月</view>
				</view>
				<view class="select-box flex-row" v-if="isManager">
					<view class="select-type-box">
						<uni-data-select v-model="type" :localdata="typeList" @change="changeType" @showSelectChange="showSelectChange" :clear="false" placeholder="请选择"></uni-data-select>
					</view>
					<view class="select-name-box">
						<luyj-select-lay :value="filterId" name="storekeeper" placeholder="请选择" :options="filterList" slabel="text" @selectitem="changeFilter" @showSelectChange="showSelectChange"></luyj-select-lay>
					</view>
				</view>
			</view>
		</view>
		<view class="table-box">
			<view class="table-tr" :class="{'tr-none': isShowSelectList}">
				<view class="table-td table-td-2">我方分润</view>
				<view class="table-td table-td-3" v-if="hasProfitOperationPermi">营利</view>
				<view class="table-td table-td-4">订单数</view>
				<view class="table-td table-td-1">客单价</view>
			</view>
			<view class="table-content" v-for="(item,key) in list" :key="key">
				<view class="table-time flex-row-between"  @click="changeDate">
					<text>{{ item.time }}</text>
					<text class="table-order" v-if="hasProfitOperationPermi">￥{{ item.netRevenueManager / 100 | formatMoney }}</text>
					<text class="table-order" v-else>￥{{ item.netRevenueEmp / 100 | formatMoney }}</text>
					<text class="table-time-arrow" v-if="timeType == 1 && false"></text>
				</view>
				<view class="table-th" @click="toDetail(item)">
					<view class="table-td table-td-2">￥{{ getProfit(item) | formatMoney }}</view>
					<view class="table-td table-td-3" v-if="hasProfitOperationPermi">￥{{ getOperateProfit(item) | formatMoney }}</view>
					<view class="table-td table-td-4" v-if="hasProfitOperationPermi">{{ item.countOrder }}</view>
					<view class="table-td table-td-4" v-else>{{ item.countOrderEmp }}</view>
					<view class="table-td table-td-1" v-if="hasProfitOperationPermi">￥{{ item.countOrder ? (item.netRevenueManager / item.countOrder / 100).toFixed(2) : 0 }}</view>
					<view class="table-td table-td-1" v-else>￥{{ item.countOrderEmp ? (item.netRevenueEmp / item.countOrderEmp / 100).toFixed(2) : 0 }}</view>
				</view>
			</view>
		</view>
		<lingfeng-timepicker ref="dayPop" :startTime="time" type="date" @change="changeTime"></lingfeng-timepicker>
		<lingfeng-timepicker ref="monthPop" type="year-month" :defaultData="defaultData" @change="changeTime"></lingfeng-timepicker>
	</view>
</template>

<script src="./profit-analyze-list.js"></script>

<style>
page {
	background-color: #fff;
}
</style>

<style lang="scss" scoped>
@import './profit-analyze-list.scss'
</style>
