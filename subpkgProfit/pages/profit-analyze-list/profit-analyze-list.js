import { navHeight, orgId, isAgentType, isAlly, isGoldenAlly, isManager } from '@/utils/role.js';
import mNav from '@/components/m-nav/m-nav.vue';
import { getDayProfitList, findOrgFilterList, getMonthProfitList, getEmpDayProfitList, getEmpMonthProfitList } from '@/subpkgProfit/api/profitAnalyzeList.js';
import { formatMoney, getToday, getMonth } from '@/utils/function.js';
import { hasProfitOperationPermi, } from "@/utils/permissionList.js";

let today = getToday();
export default {
  data() {
    return {
      filterId: '',
      timeType: 1,  // 1日 2月
      type: 1,  // 1商户2BD3代理商
      storeKeeperList: [],
      staffList: [],
      agentList: [],
      list: [],
      defaultData: {
        month: today.slice(0,7)
      },
      isShowSelectList: false,
    };
  },
  components: {
    mNav,
  },
  computed: {
    navHeight,
    typeList() {
      let arr = [{
        text: '商户',
        value: 1,
      }, {
        text: 'BD',
        value: 2,
      },];

      if (this.isAlly) {
        arr.push({
          text: '代理商',
          value: 3,
        });
      }

      // if (this.isGoldenAlly) {
      //   arr.push({
      //     text: '联盟商',
      //     value: 4,
      //   });
      // }
      return arr;
    },
    filterList() {
      let { agentList, staffList, storeKeeperList, type } = this;

      if (type == 1) return storeKeeperList;
      if (type == 2) return staffList;
      if (type == 3) return agentList;
    },
    orgId,
    isAgentType,
    isAlly,
    isGoldenAlly,
    isManager,
    hasProfitOperationPermi
  },
  filters: {
    formatMoney
  },
  async onLoad(option) {
    let storeKeeperId = parseInt(option.storeKeeperId);
    this.filterId = storeKeeperId || this.orgId
    this.list = await this.fetchData({ time: getToday() });

    let res = await this.fetchFilterList();
    this.storeKeeperList = res.listStoreKeeper;
    this.agentList = res.listAgent;
    this.staffList = res.listEmployee;
  },
  async onReachBottom() {
    if (this.timeType == 2) return;
    let { orgId } = this;

    let listTimeArry = Object.values(this.list);
    
    let time = this.getPreviousDay(listTimeArry[listTimeArry.length - 1].time);

    this.$modal.loading('加载中');
    let list = await this.fetchData({ id: orgId, time });
    this.list = [...this.list, ...list];
    this.$modal.closeLoading();
  },
  methods: {
    async fetchData(data) {
      let { timeType } = this;
      let params = {
        id: this.filterId || this.orgId,
        time: data.time,
      }
      let res;

      let dayQuery;
      let monthQuery;
      let queryString;
      if ([1, 3, 4].indexOf(this.type) > -1) {
        dayQuery = getDayProfitList;
        monthQuery = getMonthProfitList;
        queryString = 'findCurrentOrganization';
      } else if ([2].indexOf(this.type) > -1) {
        dayQuery = getEmpDayProfitList;
        monthQuery = getEmpMonthProfitList;
        queryString = 'findCurrentEmployee';
      }

      this.$modal.loading('加载中');
      if (timeType == 1) res = await dayQuery(params);
      else if (timeType == 2) res = await monthQuery({ id: this.filterId, time: getToday(), })
      let list = res.data[queryString];
      let array = [];
      this.$modal.closeLoading();

      for(let key in list) {
        list[key].time = timeType == 1 ? key.slice(5).replace(/zz/g,"-") : key.slice(5).replace(/zz/g,"-").slice(0, 7);
        array.push(list[key]);
      }
      return array;
    },
    async fetchFilterList() {
      let res = await findOrgFilterList(this.orgId);

      return res.data.findCurrentOrganization;
    },
    async changeTimeType(type) {
      this.filterId = this.filterId ? this.filterId : this.orgId;
      this.timeType = type;
      this.list = await this.fetchData({ time: getToday(), });
    },
    showSelectChange(val) {
      this.isShowSelectList = val;
    },
    changeType() {
      this.filterId = '';
    },
    async changeFilter(index, filter) {
      this.filterId = filter.value;
      uni.pageScrollTo({
				scrollTop: 0,
				duration: 0,
			})

      this.list = await this.fetchData({ time: getToday(), });
    },
    async changeDate() {
      let { timeType } = this;

      if (timeType == 1) {
        this.$refs.dayPop.show();
      } else {
        // this.$refs.monthPop.show();
      }
    },
    async changeTime(val){
      if (val.indexOf('undefined') > -1) {
        val = getMonth();
      }
      if (!this.timeType && !val)	return;
      this.time = this.timeType == 1 ? val : val + '-01';
      this.defaultData.month = val;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      });
      this.list = await this.fetchData({ time: this.time, });
    },
    toDetail(item) {
      uni.navigateTo({
        url: `/subpkgProfit/pages/profit-analyze-detail/profit-analyze-detail?time=${item.time}&type=${this.type}&id=${this.filterId}&timeType=${this.timeType}`,
      });
    },
    getProfit(data) {
      if (this.isAlly) return data.allyEmpProfit / 100;
      if (this.isAgentType) return data.agentEmpProfit / 100;
    },
    getOperateProfit(data) {
      if (this.isAlly) return data.operationProfit / 100;
      if (this.isAgentType) return data.expenseChannelIntangible / 100;
    },
    getPreviousDay(dateString) {
      var currentDate = new Date(dateString);  // 将日期字符串转换为日期对象
    
      currentDate.setDate(currentDate.getDate() - 1);  // 设置日期为前一天
    
      // 格式化日期为 "YYYY-MM-DD" 格式
      var formattedDate = currentDate.toISOString().split('T')[0];
    
      return formattedDate;
    },
  },
}