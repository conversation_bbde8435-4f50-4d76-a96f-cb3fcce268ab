<template>
	<view class="activity-contain">
		<view class="activity-content">
			<view class="title">百日冲刺活动</view>
			<view class="title-tips">活动周期：2025年3月20日至2025年6月30日</view>
			<p class="sub-title">联盟商在活动期内新增设备，根据机型与数量，对联盟商和其推荐人按下表累计积分。</p>
			<uni-table border stripe>
				<uni-tr>
					<uni-th align="center" width="182rpx">设备</uni-th>
					<uni-th align="center" >联盟商积分/台</uni-th>
					<uni-th align="center" width="252rpx">推荐人奖励积分/台</uni-th>
				</uni-tr>
				<uni-tr v-for="(item, key) in list" :key="key">
					<uni-td align="center">{{ item.device }}</uni-td>
					<uni-td align="center">{{ item.personal }}</uni-td>
					<uni-td align="center">{{ item.referral }}</uni-td>
				</uni-tr>
			</uni-table>
			<view class="bonus-tips">至活动期结束时，根据最终积分排名情况进行返现</view>
			<uni-table border stripe>
				<uni-tr>
					<uni-th align="center">排名</uni-th>
					<uni-th align="center" width="288rpx">返现金额(单位：元)</uni-th>
				</uni-tr>
				<uni-tr v-for="(item, key) in bonusList" :key="key">
					<uni-td align="center">{{ item.rank }}</uni-td>
					<uni-td align="center">{{ item.bonus }}</uni-td>
				</uni-tr>
			</uni-table>
			<view class="note">
				<view class="note-title">注意事项：</view>
				<view class="note-content">
					<view class="note-item">排名在活动截止时间计算，以实收货款为准。</view>
					<view class="note-item">设备转让需报备客户经理，同步做积分转移，否则视为不参与活动。</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [
					{
						device: '12口',
						personal: 80,
						referral: 20,
					},
					{
						device: '8口',
						personal: 60,
						referral: 15,
					},
					{
						device: '6口',
						personal: 10,
						referral: 2,
					},
					{
						device: '额外购宝',
						personal: '无',
						referral: '无',
					},
				],
				bonusList: [
					{
						rank: '前3名',
						bonus: '积分*150%',
					},
					{
						rank: '4-10名',
						bonus: '积分*120%',
					},
					{
						rank: '11-20名',
						bonus: '积分*110%',
					},
					{
						rank: '活动期间增购设备总量超过100台者',
						bonus: '积分*100%',
					},
				]
			};
		}
	}
</script>

<style lang="scss" scoped>
::v-deep .uni-table {
	min-width: 0 !important;
}
::v-deep .uni-table-th {
	padding: 16rpx 20rpx !important;
	font-size: 24rpx !important;
	background-color: #FAFAFA;
}
::v-deep .uni-table-td {
	padding: 24rpx 20rpx !important;
	color: #2E2C2B !important;
	line-height: 40rpx !important;
	font-size: 28rpx !important;
}

.activity-contain {
	padding: 24rpx 24rpx 60px;
}
.activity-content {
	background-color: #fff;
	padding: 32rpx;
	border-radius: 8px 8px 8px 8px;
}
.title {
	font-size: 44rpx;
	line-height: 62rpx;
	font-weight: bold;
	margin-bottom: 32rpx;
	color: #2E2C2B;
}
.title-tips {
	padding-bottom: 32rpx;
	margin-bottom: 32rpx;
	color: #6F6A67;
	font-size: 24rpx;
	border-bottom: 1px solid #EBE9E7;
}
.sub-title {
	margin-bottom: 32rpx;
	font-size: 28rpx;
	line-height: 40rpx;
	color: #2E2C2B;
}
.bonus-tips {
	margin: 32rpx 0;
	font-size: 28rpx;
	line-height: 40rpx;
}
.note {
	margin: 32rpx 0 0 0;
	padding-top: 32rpx;
	border-top: 1px solid #EBE9E7;
	font-size: 24rpx;
	color: #6F6A67;
	.note-title {
		margin-bottom: 16rpx;
		letter-spacing: 1rpx;
	}
	.note-item {
		margin-bottom: 16rpx;
		letter-spacing: 1rpx;
	}
}
</style>
