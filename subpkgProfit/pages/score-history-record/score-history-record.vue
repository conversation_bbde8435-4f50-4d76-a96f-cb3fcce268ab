<template>
	<view class="score-history-record">
		<view class="record-content">
			<view class="record-header flex-row-between">
				<view class="record-header-title">进货机型/数量</view>
				<view class="">积分</view>
			</view>
			<view class="record-list" v-if="list.length">
				<view class="record-item" v-for="(item, key) in list" :key="key">
					<view class="record-item-content flex-row-between">
						<view class="flex-row">
							<view class="device-content flex-row">
								<view class="device-item flex-row">
									<view class="device-item-title device-item-title-6"></view>
									<view class="device-item-value">{{ item.countDeviceS6 ? item.countDeviceS6 : 0 }}</view>
								</view>
								<view class="device-item flex-row">
									<view class="device-item-title device-item-title-8"></view>
									<view class="device-item-value">{{ item.countDeviceS8 ? item.countDeviceS8 : 0 }}</view>
								</view>
								<view class="device-item flex-row">
									<view class="device-item-title device-item-title-12"></view>
									<view class="device-item-value">{{ item.countDeviceS12 ? item.countDeviceS12 : 0 }}</view>
								</view>
							</view>
							<view class="referral-icon" v-if="item.allyId != orgId">
								<view>推</view>
								<view>荐</view>
							</view>
							<view class="installment-icon" v-if="item.allyId == orgId && item.credit">
								<view>分</view>
								<view>期</view>
							</view>
						</view>
						<view class="score">+{{ item.allyId == orgId ? item.personalScore : item.referralScore }}</view>
					</view>
					<view class="record-item-footer flex-row">
						<view class="record-date">{{ item.date }}</view>
						<view class="record-company" v-if="item.ally && item.allyId != orgId">{{ item.ally.companyFullName }}</view>
					</view>
				</view>
			</view>
			<view class="record-empty" v-else>
				<view class="">暂无积分记录哦~</view>
			</view>
		</view>
	</view>
</template>

<script src="./score-history-record.js">
</script>

<style lang="scss" scoped>
@import './score-history-record.scss'
</style>
