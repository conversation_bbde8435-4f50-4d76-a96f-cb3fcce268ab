
.score-history-record {
  padding: 12px 0 30px;
}

.record-content {
  margin: 0 24rpx;
  border-radius: 8px;
  overflow: hidden;
  .record-header {
    padding: 16rpx 32rpx;
    background: linear-gradient( 180deg, #FFFFFF 0%, #FAFAFA 100%);
    color: #6F6A67;
    font-size: 28rpx;
    line-height: 40rpx;
  }
  .record-list {
    background-color: #fff;
    .record-item {
      padding: 24rpx 32rpx;
      border-bottom: 1px solid #FAFAFA;
      &:nth-last-child(1) {
        border-bottom: none;
      }
      .record-item-content {
        margin-bottom: 24rpx;
      }
      .device-content {
        height: 72rpx;
        padding: 0 16rpx;
        background-color: #FAFAFA;
        border-radius: 4px;
        .device-item {
          width: 128rpx;
          .device-item-title {
            width: 32rpx;
            height: 32rpx;
            margin-right: 16rpx;
            background-size: 32rpx 32rpx;
          }
          .device-item-title-6 {
            background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/score-history-record-device-6.png');
          }
          .device-item-title-8 {
            background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/score-history-record-device-8.png');
          }
          .device-item-title-12 {
            background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/score-history-record-device-12.png');
          }
          .device-item-value {
            font-size: 28rpx;
            line-height: 40rpx;
            color: #2E2C2B;
            line-height: 40rpx;
          }
        }
      }
      .referral-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 72rpx;
        padding: 8rpx;
        margin: 0 8rpx;
        background: rgba(25,128,250,0.06);
        border-radius: 4px 4px 4px 4px;
        color: #1980FA;
        font-size: 20rpx;
        box-sizing: border-box;
      }
      .installment-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 72rpx;
        padding: 8rpx;
        margin-left: 8rpx;
        background: rgba(244,173,67,0.1);
        border-radius: 4px 4px 4px 4px;
        color: #F4AD43;
        font-size: 20rpx;
        box-sizing: border-box;
      }
      .score {
        color: #1980FA;
        font-size: 28rpx;
        line-height: 40rpx;
      }
      .record-item-footer {
        font-size: 24rpx;
        line-height: 34rpx;
        color: #B2B0AF;
        .record-date {
          margin-right: 32rpx;
        }
      }
    }
  }
  .record-empty {
    font-size: 26rpx;
    color: #999;
    letter-spacing: 1rpx;
    line-height: 160rpx;
    text-align: center;
    background-color: #fafafa;
  }
}