import { findScoreRecord } from '@/subpkgProfit/api/scoreHistoryRecord.js';
import { orgId } from '@/utils/role.js';
import { flatNodeList } from '@/utils/function.js';

export default {
  data() {
    return {
      list: [],
      pageInfo: {},
    };
  },
  computed: {
    orgId,
  },
  async onLoad() {
    this.list = await this.fetchList();
  },
  async onReachBottom() {
    if (!this.pageInfo.hasNextPage || this.isLoading) return;

    let list = await this.fetchList();
    this.list = [...this.list, ...list];
  },
  methods: {
    async fetchList() {
      this.loading = true;
      let res = await findScoreRecord({orgId: this.orgId, endCursor: this.pageInfo.endCursor});
      this.loading = false;
      this.pageInfo = res.data.findScoreRecord.pageInfo;

      return flatNodeList(res.data.findScoreRecord.edges);
    },
  },
}