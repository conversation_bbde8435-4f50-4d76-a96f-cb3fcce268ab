.set-permission-contain {
  padding: 0 24rpx 110px;
}

.permission-first {
  padding-top: 28rpx;
  padding-bottom: 8rpx;
  border-bottom: 1px solid rgba(45,45,45,0.1);
  &:nth-last-child(1) {
    border-bottom: none;
  }
  .first-title {
    position: relative;
    padding-left: 12rpx;
    margin-bottom: 26rpx;
    color: #1EAB67;
    font-size: 32rpx;
    line-height: 44rpx;
    &::after {
      position: absolute;
      left: 0;
      top: 5px;
      display: block;
      content: "";
      width: 2px;
      height: 24rpx;
      background-color: #1EAB67;
    }
  }
  .permission-second {
    padding-left: 12rpx;
    .second-title {
      margin-bottom: 28rpx;
      color: #2D2D2D;
      font-size: 28rpx;
      line-height: 40rpx;
    }
  }
  .permission-third {
    padding-left: 20rpx;
    .permission-item {
      float: left;
      padding: 8rpx 36rpx;
      margin-right: 36rpx;
      margin-bottom: 28rpx;
      border: 1px solid rgba(45,45,45,0.1);
      border-radius: 4px;
      font-size: 28rpx;
      color: #2D2D2D;
    }
    .permission-item-inactive-disabled {
      color: rgba(45,45,45,0.5);
    }
    .permission-item-active {
      position: relative;
      border: 1px solid #1EAB67;
      background-color: #F6FFF8;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/select-icon.png');
      background-size: 13px 13px;
      background-repeat: no-repeat;
      background-position: right -1px bottom -1px;
    }
    .permission-item-active-disabled {
      background-color: #fbfffc;
      color: #8ad5af;
      border: 1px solid #8ad5af;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/set-permission-right-bottom-inactive-icon.png');
    }
  }
}

.data-info-icon {
  display: block;
  width: 12px;
  height: 12px;
  margin-left: 10rpx;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-icon-black.png');
  background-size: 12px 12px;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 46rpx;
  padding-top: 38rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 19px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 19px);
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.25);
  border-radius: 24rpx 24rpx 0 0;
  box-sizing: border-box;
  .fixed-btn {
    width: 288rpx;
    height: 36px;
    text-align: center;
    line-height: 36px;
    font-size: 28rpx;
    font-weight: bold;
    border-radius: 18px;
    letter-spacing: 1px;
  }
  .reset-btn {
    box-shadow: 0px 0px 4px 0px rgba(16,28,18,0.14);
    color: #2D2D2D;
  }
  .confirm-btn {
    background: linear-gradient(115deg, #76DB91 0%, #1EAB67 100%);
    box-shadow: 0px 2px 4px 0px rgba(16,28,18,0.14);
    color: #fff;
  }
}