import {
	findPermissionList,
	setPermission,
	findStaffById,
} from "@/subpkgProfit/api/setPermission.js";
import { orgType } from "@/utils/role";
import { toast } from "@/utils/common";

export default {
	data() {
		return {
			id: "", // 需要设置的人员id
			list: [],
			defaultPermissions: [],
			permissions: [],
			clonePermissions: [],
		};
	},
	computed: {
		orgType,
	},
	async onLoad(option) {
		let { orgType } = this;

		this.id = option.id;
		this.list = await this.fetchData();
	},
	methods: {
		async fetchData() {
			let { orgType } = this;

			let staff = await findStaffById(this.id);
			let res = await findPermissionList({ userId: this.id });
			let permissionList = JSON.parse(res.data.findPermission);

			let permissions = staff.data.findEmployeeById.roleInfo.permissions;
			this.defaultPermissions =
				staff.data.findEmployeeById.defaultInitPermission;
			this.permissions = permissions;
			this.clonePermissions = JSON.parse(JSON.stringify(permissions));

			permissionList.forEach((first) => {
				first.children &&
					first.children.forEach((second) => {
						second.children &&
							second.children.forEach((third) => {
								if (permissions.indexOf(third.code) > -1) {
									third.hasPermission = true;
								}
							});
					});
			});
			return permissionList;
		},
		async fetchPermission() {
			let { id } = this;
		},
		togglePermission(firstKey, secondKey, thirdKey, third) {
			let hasPermission = third.hasPermission;
			let permission = third.code;

			if (!third.edit) return;
			if (!hasPermission) {
				this.permissions.push(permission);
			} else {
				let permissionKey = this.permissions.indexOf(permission);
				this.permissions.splice(permissionKey, 1);
			}

			this.$set(
				this.list[firstKey].children[secondKey].children[thirdKey],
				"hasPermission",
				!hasPermission
			);
		},
		toExplain(item) {
			if (item.code != "eq") return;
			uni.navigateTo({
				url: `/subpkg/pages/explain-page/explain-page?type=permissionData`,
			});
		},
		reset() {
			let { defaultPermissions, list } = this;

			list.forEach((first, firstKey) => {
				first.children &&
					first.children.forEach((second, secondKey) => {
						second.children &&
							second.children.forEach((third, thirdKey) => {
								if (defaultPermissions.indexOf(third.code) > -1) {
									this.$set(
										this.list[firstKey].children[secondKey].children[thirdKey],
										"hasPermission",
										true
									);
									if (this.permissions.indexOf(third.code) < 0) {
										this.permissions.push(third.code);
									}
								} else {
									this.$set(
										this.list[firstKey].children[secondKey].children[thirdKey],
										"hasPermission",
										false
									);
									if (this.permissions.indexOf(third.code) > -1) {
										let permissionKey = this.permissions.indexOf(third.code);
										this.permissions.splice(permissionKey, 1);
									}
								}
							});
					});
			});
		},
		async save() {
			let { id, permissions } = this;

			let data = {
				userId: id,
				permissions,
			};
			let res = await setPermission(data);
			toast("设置成功");
			uni.navigateBack();
		},
	},
};
