<template>
	<view class="set-permission-contain">
		<view class="permission-root">
			<view class="permission-first" v-for="(item, firstKey) in list" :key="item.code">
				<view class="first-title flex-row" @click="toExplain(item)">
					{{ item.name }}
					<text class="data-info-icon" v-if="item.code=='eq'"></text>
				</view>
				<view class="permission-second" v-for="(second, secondKey) in item.children" :key="second.code">
					<view class="second-title">{{ second.name }}</view>
					<view class="permission-third clearfix">
						<view class="permission-item" :class="{'permission-item-active': third.hasPermission, 'permission-item-active-disabled': third.hasPermission && !third.edit, 'permission-item-inactive-disabled': !third.hasPermission && !third.edit}" @click="togglePermission(firstKey, secondKey, thirdKey, third)" v-for="(third, thirdKey) in second.children" :key="third.code">
							{{ third.name }}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="fixed-bottom safe-area-padding">
			<view class="reset-btn fixed-btn" @click="reset">恢复默认</view>
			<view class="confirm-btn fixed-btn" @click="save">保存修改</view>
		</view>
	</view>
</template>

<script src="./set-permission.js"></script>

<style>
</style>

<style lang="scss" scoped>
@import './set-permission.scss'
</style>
