::v-deep .wu-calendar__content {
  background-color: transparent !important;
}
::v-deep .wu-calendar__header {
  justify-content: space-between !important;
  padding: 0 32rpx !important;
  background-color: #fff !important;
  border-bottom: none !important;
}
::v-deep .wu-calendar__header-btn-box.horizontal {
  width: 88rpx !important;
  height: 88rpx !important;
}
::v-deep .wu-calendar__header-text {
  color: #6F6A67 !important;
  font-size: 28rpx !important;
  font-weight: bold !important;
  line-height: 40rpx !important;
  letter-spacing: 1rpx !important;
}

::v-deep .wu-calendar__header-btn {
  width: 32rpx !important;
  height: 32rpx !important;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-right-icon.png') !important;
  background-size: 32rpx 32rpx !important;
  background-repeat: no-repeat !important;
  border: none !important;
  transform: none !important;
}
::v-deep .wu-calendar__header-btn-box:nth-of-type(1) .wu-calendar__header-btn {
  transform: rotate(180deg) !important;
}
::v-deep .wu-calendar__backtoday {
  display: none !important;
}

// 星期几
::v-deep .wu-calendar__weeks {
  padding: 0 32rpx !important;
}
::v-deep .wu-calendar__weeks-day {
  color: #B2B0AF !important;
  font-size: 24rpx !important;
  border-bottom: none !important;
  
}

::v-deep swiper .wu-calendar-block {
  padding: 16rpx 0 !important;
  margin: 0 16rpx !important;
  background-color: #fff !important;
  border-radius: 8px 8px 8px 8px !important;
}
::v-deep swiper .wu-calendar__weeks {
  padding: 0 16rpx !important;
}
::v-deep swiper .wu-calendar__weeks:nth-of-type(1) .wu-calendar__weeks-item:nth-of-type(1) {
  border-radius: 6px 0 0 0;
}
::v-deep swiper .wu-calendar__weeks:nth-last-of-type(1) .wu-calendar__weeks-item:nth-last-of-type(1) {
  border-radius: 0 0 6px 0;
}

::v-deep .wu-calendar-item__weeks-box {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  background-color: #fff !important;
}
::v-deep .wu-calendar__weeks-item {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  background-color: #F5F5F5 !important;
}

::v-deep .wu-calendar-item__disabled-item {
  background-color: #F5F5F5 !important;
  
}
::v-deep .wu-calendar-item__disabled-item .wu-calendar-item__weeks-box-text {
  color: #B2B0AF !important;
}


::v-deep .wu-calendar-item__weeks-box-item {
  justify-content: flex-start !important;
  padding-top: 8px !important;
  background-color: transparent !important;
  box-sizing: border-box !important;
}

::v-deep .wu-calendar-item__weeks-box-text {
  font-size: 32rpx !important;
  line-height: 22px !important;
  color: #2E2C2B !important;
  background-color: transparent !important;
}
::v-deep .wu-calendar-item__weeks-lunar-text {
  display: none !important;
}

// 今天
::v-deep .wu-calendar-item__weeks-box .wu-calendar-item__weeks-box-text {
  display: block;
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx !important;
  text-align: center;
  box-sizing: border-box;
}
::v-deep .wu-calendar-item__today-item .wu-calendar-item__weeks-box-text {
  border: 1px solid #EBE9E7;
  background-color: #FAFAFA !important;
  line-height: 76rpx !important;
  border-radius: 8rpx;
}
::v-deep .wu-calendar-item__today-item .wu-calendar-item__weeks-box-text .wu-calendar-item__weeks-box-text-sub {
  background: linear-gradient(4.2625615022220847e-7deg, #3935FD 0%, #3EAAF9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
::v-deep .wu-calendar-item__weeks-lunar-color-block {
  margin: 0 0 8px 0 !important;
}
::v-deep .wu-calendar-item__disabled-item .wu-calendar-item__weeks-box-text-sub {
  opacity: 0 !important;
}

.activate-history-contain {
  padding-bottom: 50px;
}

.grid-box {
  display: flex;
  justify-content: center;
  .grid-item {
    display: flex;
    align-items: center;
    margin-right: 64rpx;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #2E2C2B;
    &:nth-last-child(1) {
      margin-right: 0;
    }

    .grid-item-icon {
      width: 16rpx;
      height: 16rpx;
      margin-right: 8rpx;
      background-color: #43B730;
      border-radius: 1px;
    }
    .grid-item-icon-red {
      background-color: #FF3B30;
    }
  }
}