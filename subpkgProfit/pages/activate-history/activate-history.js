import { findTrendList } from '@/subpkgProfit/api/activateHistory.js';
import { orgId } from '@/utils/role';
import { getMonth, getToday } from '@/utils/function.js';

export default {
	data() {
		return {
      allyId: null,
      month: getToday(),
			selected: []
		}
	},
  computed: {
    orgId,
  },
  async onLoad(options) {
    if (options.allyId) {
      this.allyId = parseInt(options.allyId);
    } else {
      this.allyId = this.orgId;
    }
    this.fetchList();
  },
	methods: {
    async fetchList() {
      const { allyId, month } = this;
      if (!allyId) return;
      this.$modal.loading('加载中');
      let res = await findTrendList({ orgId: allyId, month });
      let list = [];

      res.data.layTerminalTrend.forEach(item => {
        let obj = {
          date: item.date.split('T')[0],
          colorBlock: item.totalCount > 0 ? "linear-gradient( 270deg, #9DEA3B 0%, #2AA269 100%)" : item.totalCount == 0 ? "transparent" : "linear-gradient( 270deg, #F9733E 0%, #FF3B30 100%)",
          increaseNum: item.activeTerminalCount,
          decreaseNum: item.inactiveTerminalCount,
        }
        list.push(obj)
      })
      this.selected = list;
      this.$modal.closeLoading();
    },
    async monthSwith(e) {
      console.log(e)
      let { year, month, } = e;

      month = this.fillZero(month);
      this.month = year + '-' + month + '-01';

      this.fetchList();
    },
    fillZero(number, int = 2) {
      let str = number + "";
      while (str.length < int) {
        str = "0" + str;
      }
      return str;
    }
	}
}