<template>
	<view class="ally-banner ally-banner-direct" :style="{'--nav-height': -navBarHeight + 'px', '--nav-height-number': navBarHeight + 146 + 'px',}">
    <view class="ally-level-bg"></view>
    <view class="ally-level" :class="[`ally-level-${level}`]" @click="toAllyDetail">
    </view>
    <view class="level-fixed" @click="toAllyDetail">{{details.fixed ? '固定' : '当前'}}等级</view>
		<view class="ally-banner-content">
      <view class="role-box">
        <uni-data-select
          v-model="roleId"
          :localdata="userList"
          @change="changeRole"
          :clear="false"
          placeholder="请选择"
        ></uni-data-select>
      </view>
      <view class="a-b-c-content">
        <view class="ally-data">
          <view class="ally-data-item flex-column">
            <view class="ally-data-item-title flex-row" @click="showTips">
              总收益
              <text class="info-icon"></text>
            </view>
            <view class="ally-data-item-value">
              <text class="ally-data-item-icon">￥</text>
              <text class="ally-data-item-money">{{ all.dateRange ? formatIncomeAmount(all.dateRange.dataset.assetAccountDeposit).money : 0 }}</text>
              <text class="ally-data-item-unit">{{ all.dateRange ? formatIncomeAmount(all.dateRange.dataset.assetAccountDeposit).unit : '' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
	</view>
</template>

<script>
import { changeRole, } from "@/api/login.js";
import { userList, loginRelaunchPage, manageId } from "@/utils/role.js";
import { formatIncomeAmount } from '@/subpkgProfit/utils/money.js';
import { setToken } from "@/utils/auth";

export default {
  props: {
    all: {
      type: Object,
      default: {},
    },
    details: {
      type: Object,
      default: {},
    },
    level: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      roleId: "", // 身份id
    };
  },
  computed: {
    manageId,
    userList,
    navBarHeight() {
			return (
				getApp().globalData.navBarHeight + getApp().globalData.statusBarHeight
			);
		},
  },
  created() {
    this.roleId = this.manageId;
  },
  methods: {
    async changeRole() {
			let res = await changeRole(this.roleId);
			setToken(res.data);
			this.$store.commit("SET_TOKEN", res.data);
			this.$modal.loading("加载中");
			this.$store.dispatch("GetInfo").then((res) => {
				this.$modal.closeLoading();
				this.initLogin();
			});
		},
    async initLogin() {
			this.$modal.closeLoading();
			
      loginRelaunchPage();
		},
    toAllyDetail() {
      if (this.level == 'KA') return;
      uni.navigateTo({
        url: "/subpkgProfit/pages/index-ally-detail/index-ally-detail",
      })
    },
    showTips() {
      this.$emit('showTips', "allProfit");
    },
    formatIncomeAmount,
  }
};
</script>

<style lang="scss" scoped>
.ally-banner {
  position: relative;
  padding: 32px 38rpx 0 0; 
  height: 146px;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-banner-bg-1.png');
  background-size: 100% var(--nav-height-number);
  background-position: 0 var(--nav-height);
  background-repeat: no-repeat;
  box-sizing: border-box;
}
.flex-column {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.ally-banner-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  .role-box {
    width: 360rpx;
    height: 17px;
    margin-bottom: 12px;
  }
  .a-b-c-content {
    .ally-data {
      display: flex;
      height: 76px;
      .ally-data-item {
        flex: 1;
        color: #fff;
        .info-icon {
          width: 32rpx;
          height: 32rpx;
          margin-left: 8rpx;
          background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-banner-info-icon.png");
          background-size: 32rpx 32rpx;
        }
        .ally-data-item-title {
          margin-bottom: 4px;
          font-size: 13px;
          line-height: 18px;
          color: rgba(255,255,255,0.5);
        }
        .ally-data-item-value {
          .ally-data-item-icon {
            font-size: 16px;
            line-height: 21px;
            font-weight: bold;
          }
          .ally-data-item-money {
            margin: 0 8rpx;
            font-size: 32px;
            line-height: 42px;
            font-weight: bold;
          }
          .ally-data-item-unit {
            font-size: 16px;
            line-height: 22px;
          }
        }
      }
      .ally-level-content {
        display: flex;
        justify-content: center;
        padding-top: 16px;
      }
    }
  }
}

.ally-level-bg {
  position: absolute;
  left: 14rpx;
  top: 60px;
  background-size: 390rpx 40px;
  width: 390rpx;
  height: 40px;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-level-bg.png');
}
.ally-level {
  position: absolute;
  top: 31px;
  left: 102rpx;
  width: 100px;
  height: 56px;
  background-size: 100px 56px;
  background-repeat: no-repeat;
  z-index: 3;
}
.ally-level-V0 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V0.png');
}
.ally-level-V1 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V1.png');
}
.ally-level-V2 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V2.png');
}
.ally-level-V3 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V3.png');
}
.ally-level-V4 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V4.png');
}
.ally-level-V5 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V5.png');
}
.ally-level-V6 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V6.png');
}
.ally-level-V7 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V7.png');
}
.ally-level-V8 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V8.png');
}.ally-level-V9 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V9.png');
}

.ally-level-V10 {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-V10.png');
}
.ally-level-KA {
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-KA.png');
}

.level-fixed {
  position: absolute;
  left: 46rpx;
  bottom: 0px;
  width: 322rpx;
  height: 42px;
  color: rgba(255,255,255,0.5);
  font-size: 24rpx;
  line-height: 42px;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-level-bottom-bg.png');
  background-size: 322rpx 42px;
  text-align: center;
  z-index: 3;
}
</style>
