<template>
	<view class="function-block">
		<view class="function-row">
      <view class="function-title">组织</view>
      <view class="function-content">
        <view class="function-item" @click="toPage(item)" v-for="(item, key) in orgFunctionList" :key="item.id" v-if="item.show">
          <view class="function-icon">
            <img :src="item.icon" class="function-img" />
          </view>
          <view class="function-name">{{ item.name }}</view>
        </view>
      </view>
    </view>
    <view class="function-row">
      <view class="function-title">工具</view>
      <view class="function-content">
        <block v-for="(item, key) in toolList" :key="item.id">
          <view class="function-item" @click="toPage(item)" v-if="item.show">
            <view class="function-icon">
              <img :src="item.icon" class="function-img" />
            </view>
            <view class="function-name">{{ item.name }}</view>
          </view>
        </block>
      </view>
    </view>
    <view class="function-row">
      <view class="function-title">其他</view>
      <view class="function-content">
        <view class="function-item" v-for="(item, key) in deviceList" :key="item.id" @click="toPage(item)" v-if="item.show">
          <view class="function-icon">
            <img :src="item.icon" class="function-img" />
          </view>
          <view class="function-name">{{ item.name }}</view>
        </view>
      </view>
    </view>
    <view class="function-row" v-if="supportList && supportList.length">
      <view class="function-title">支持</view>
      <view class="function-content">
        <view class="function-item" v-for="(item, key) in supportList" :key="item.url" @click="contactSupport(item)">
          <view class="function-icon">
            <img :src="item.icon" class="function-img" />
          </view>
          <view class="function-name">{{ item.name }}</view>
        </view>
      </view>
    </view>
	</view>
</template>

<script>
import { hasFindOrderPermi, hasInvestmentMenuPermi, hasProfitOperationPermi, } from '@/utils/permissionList.js'
import { includeWhitePhone, isAgentType, manageId, orgId, getRealName, orgName } from '@/utils/role'
import { isManager } from '@/utils/role.js'
import { findSupportList } from '@/subpkgProfit/api/indexAlly.js';
import { toast } from "@/utils/common";

export default {
  data() {
    return {
      orgFunctionList: [
        {
					id: "staff",
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon1.png",
					name: "BD",
					url: "/subpkg/pages/staff/staff",
					show: 1,
				},
				{
					id: "agent",
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon2.png",
					name: "代理",
					url: "/subpkg/pages/agent/agent",
					show: 1,
				},
        {
          id: "storeKeeper",
          icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop.png",
          name: "商户",
          url: "/subpkg/pages/storekeeper-list/storekeeper-list",
          show: 1,
        },
				{
          id: 3,
          icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/area-task-icon.png",
          url: "/subpkgProfit/pages/ally-area-task/ally-area-task",
          name: "独家区域",
          show: 1,
        },
      ],
      toolList: [
				{
					id: 1,
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon5.png",
					url: "/subpkg/pages/service-shop/service-shop",
					name: "周边点位",
					show: 1,
				},
				{
          id: 2,
          icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-icon-maintenance.png",
          url: "/subpkgDevice/pages/maintenance-service/maintenance-service",
          name: "智能运维",
          show: 0,
        },
				{
					id: 3,
					url: "/subpkgProfit/pages/order-manage/order-manage",
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon6.png",
					name: "订单查询",
					show: 0,
				},
				{
					id: 4,
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon7.png",
					name: "工单",
					url: "/subpkg/pages/prepay-list/prepay-list",
					show: 1,
				},
			],
      deviceList: [
        {
          id: "newStore",
          icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-new-shop.png",
          url: "/subpkgShop/new-store-list/new-store-list",
          name: "门店分析",
          show: 0,
        },
        {
					id: 1,
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon8.png",
					url: "/subpkg/pages/activate-storekeeper/activate-storekeeper",
					name: "激活上线",
					show: 1,
				},
        {
					id: 2,
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon9.png",
					url: "/subpkg/pages/stock-transfer/stock-transfer",
					name: "设备转移",
					show: 1,
				},
        {
          id: "vip",
					icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-icon4.png",
					name: "会员",
					url: "/subpkg/pages/vip/vip",
					show: 1,
				},
        {
          id: 4,
          icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/investment-icon.png",
          url: "/subpkgInvest/pages/allyHomePage/index",
          name: "门店投资",
          show: 0,
        },
        {
          id: 5,
          icon: "https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/home_sanfangxitong.png",
          url: "/subpkgExternal/pages/index",
          name: "三方系统",
          show: 1,
        },

      ],
      supportList: [],
    };
  },
  computed: {
    hasFindOrderPermi,
    orgId,
    manageId,
    isManager,
    includeWhitePhone,
    isAgentType,
    getRealName,
    orgName,
  },
  created() {
    if (this.hasFindOrderPermi) {
			this.toolList[2].show = 1;
		}
    this.fetchSupportList();
    this.checkShowMaintenance()
    this.checkShowInvestment()
    this.checkShowNewStore()
  },
  methods: {
    toPage(item) {
      console.log(item, 'item.url')
      if (item.url) {
        uni.navigateTo({
					url: item.url,
				});
      }
    },
    async checkShowMaintenance() {
      await this.$store.dispatch('maintenance/GetSetting', this.orgId)
      const setting = this.$store.state.maintenance.setting

      if (!(setting && setting.ruleSettings)) return

      // 没有设置分数的员工不展示智能运维
      const userList = setting.userSettings.filter(item => item.userId == this.manageId);

      if (!this.isManager && !userList.length) return

      this.$store.dispatch('maintenance/GetStaffList', this.orgId)
      const index = this.toolList.findIndex(item => item.name === "智能运维")
      if (index > -1) {
        this.toolList[index].show = true
      }
    },
    async fetchSupportList() {
      try {
        let res = await findSupportList();

        this.supportList = res.data.findFirstOpenSettings.settingBody.list;
      } catch(e) {
        console.log(e);
      }
    },
    checkShowInvestment(){
      if(!this.isAgentType){
        const index = this.deviceList.findIndex(item => item.name === '门店投资')
        if(index > -1){
          this.deviceList[index].show = 1
        }
      }
    },
    checkShowNewStore(){
      if(hasProfitOperationPermi()){
        const index = this.deviceList.findIndex(item => item.name === '门店分析')
        this.deviceList[index].show = 1
      }
    },
    contactSupport(item){
      wx.openCustomerServiceChat({
          showMessageCard: true,
          sendMessageTitle: this.manageId + this.getRealName + '-' + this.orgName,
          sendMessagePath:`pages/login/login`,
          sendMessageImg: 'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/contact-logo.png',
          extInfo: { url: item.url },
          corpId: "ww3c8c7a923c73539f",
          success(res) {
          },
          fail(e) {
            console.log(e, "e11");
            toast("调用失败");
          },
      });
    },
  },
}
</script>

<style lang="scss" scoped>
.function-block {
  margin: 24rpx 24rpx 0;
  border-radius: 16rpx;
  background-color: #fff;
  overflow: hidden;
  .function-row {
    display: flex;
    border-bottom: 1px solid #EBEBEB;
    &:nth-last-child(1) {
      border-bottom: none;
    }
    .function-title {
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 56rpx;
      padding-top: 30rpx;
      background: linear-gradient( 270deg, #F5F5F5 0%, #FFFFFF 100%);
      font-size: 24rpx;
      color: #B2B0AF;
      writing-mode: vertical-lr;
      text-align: center;
      letter-spacing: 30rpx;
    }
    .function-content {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      .function-item {
        position: relative;
        width: 160rpx;
        height: 160rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .function-img {
          width: 48rpx;
          height: 48rpx;
          margin-bottom: 8rpx;
        }
        .function-name {
          color: #2E2C2B;
          font-size: 26rpx;
          font-weight: bold;
          line-height: 36rpx;
          letter-spacing: 1rpx;
        }
      }
      .function-item::after {
        display: block;
        content: '';
        width: 2rpx;
        height: 32rpx;
        background-color: #F5F5F5;
        position: absolute;
        right: 0;
        top: 64rpx;
      }
      .function-item:nth-last-child(1)::after {
        display: none;
      }
    }
  }
}
</style>
