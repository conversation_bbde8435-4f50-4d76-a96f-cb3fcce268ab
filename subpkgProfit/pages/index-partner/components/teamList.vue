<template>
	<view class="partner-list-contain" v-if="allyList.length">
		<view class="filter-box flex-row-between">
			<view class="search-box">
				<view class="search-content flex-row">
					<view class="search-icon"></view>
					<input v-model="searchText" @blur="searchAlly" class="search-input" placeholder="请输入搜索内容" />
				</view>
			</view>
			<view class="sort-item">按流水</view>
		</view>
		<view class="partner-list-box">
			<view class="partner-list-title">
				<text class="title">{{ tab == 1 ? '团队' : '直营' }}列表</text>
				<text class="title-sub">({{ tab == 1 ? '团队' : '直营' }}数: {{ tab == 1 ? (details.all ? details.all.countAlly : 0) : (details.direct ? details.direct.countAlly : 0) }})</text>
			</view>
			<view class="partner-list" v-if="list.length">
        <view class="self-list" v-if="selfList.length">
          <view class="self-box" @click="toSelfDetail" v-if="self.allyId == orgId">
            <view class="self-name flex-row-between">
              <view>
                {{ self.allyName }}
              </view>
              <view style="color: #6F6A67;font-weight: 400;">
                {{self.allyId == orgId ? '(我)' : ''}}
              </view>
            </view>
            <view class="shadow-box">
              <view class="self-title">上月收益</view>
              <view class="flex-row-between">
                <view class="self-income">
                  <text class="income-icon">￥</text>
                  <text class="income-money">{{ formatIncomeAmount(self.framework.lastMonthData.dataset.assetAccountDeposit).money }}</text>
                  <text class="income-unit">{{ formatIncomeAmount(self.framework.lastMonthData.dataset.assetAccountDeposit).unit }}</text>
                </view>
                <view class="self-order">
                  <text>订单数：</text>
                  <text>{{ self.framework.lastMonthData.dataset.countOrder }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="direct-list" v-if="directList.length">
          <view v-for="(item, key) in directList" :key="key">
            <Team :partner="item" />
          </view>
        </view>
        <view class="team-list">
          <view v-for="(item, key) in teamList" :key="key">
            <Team :partner="item" />
          </view>
        </view>
			</view>
      <view class="partner-list-empty" v-else>
        暂无{{ tab == 1 ? '团队' : '直营' }}数据
      </view>
		</view>
	</view>
</template>

<script>
import { formatIncomeAmount } from '@/subpkgProfit/utils/money.js';
import { sortByNestedProperty } from '@/utils/tool.js';
import Team from './team.vue';
import { orgId } from '@/utils/role.js';

	export default {
    props: {
      allyId: {
        type: Number || String,
        default: null
      },
      allyList: {
        type: Array,
        default: [],
      },
      tab: {
        type: Number,
        default: 1,
      },
      details: {
        type: Object,
        default: {}
      },
    },
    data() {
      return {
        pageScrollTop: 0,
        searchText: '',
        self: {},
        list: [],
        selfList: [],
        directList: [],
        teamList: [],
      }
    },
    components: {
      Team,
    },
    watch: {
      allyList: {
        deep: true,
        handler(newVal) {
          this.setSelfData();
          let list = this.sortList(newVal);
          list.unshift(this.self);
          this.list = this.initChart(list);
          this.selfList = this.list.filter(partner => partner.allyId == this.orgId);
          this.directList = this.list.filter(partner => partner.direct);
          this.teamList = this.list.filter(partner => !partner.direct && partner.allyId != this.orgId);
        }
      },
    },
    computed: {
      orgId
    },
    onPageScroll(e) {
      this.pageScrollTop = e.scrollTop
    },
    methods: {
      initChart(list) {
        // 防止死循环；
        const allyList = JSON.parse(JSON.stringify(list));
        allyList.forEach(item => {
          const data = item.framework.monthChart.map(chart => chart.dataset.assetAccountDeposit / 100);
          const categories = item.framework.monthChart.map(chart => chart.date.slice(5, 7));
          let profitRes = {
            categories,
            series: [
              {
                name: "收益",
                textColor: "#ffcd9e",
                data: data,
                legendShape: "circle",
                color: "#3EAAF9",
                pointShape: "none",
              },
            ],
          };
          item.framework.chartData = JSON.parse(JSON.stringify(profitRes));
        })
        return allyList;
      },
      searchAlly() {
        const { searchText } = this;

        if (searchText) {
          let list = this.sortList(this.allyList);
          list.unshift(this.self);
          list = this.initChart(list);
          this.list = list.filter(item => {
            return item.allyName.indexOf(searchText) > -1
          });
          this.selfList = this.list.filter(partner => partner.allyId == this.orgId);
          this.directList = this.list.filter(partner => partner.direct);
          this.teamList = this.list.filter(partner => !partner.direct  && partner.allyId != this.orgId);
        } else {
          let list = this.sortList(this.allyList);
          list.unshift(this.self);
          this.list = this.initChart(list);
          this.selfList = this.list.filter(partner => partner.allyId == this.orgId);
          this.directList = this.list.filter(partner => partner.direct);
          this.teamList = this.list.filter(partner => !partner.direct && partner.allyId != this.orgId);
        }
      },
      setSelfData() {
        this.self = {
          allyName: this.details.allyName,
          allyId: this.details.allyId,
          level: this.details.level,
          framework: {
            countAlly: 1,
            lastMonthData: this.details.self.lastMonthData,
            monthChart: this.details.self.monthChart
          }
        }
      },
      sortList(allyList) {
        let list = JSON.parse(JSON.stringify(allyList));
        if (!list || !list.length) return [];

        return sortByNestedProperty(list, obj => obj.framework.lastMonthData.dataset.assetAccountDeposit, 'desc');
      },
      formatIncomeAmount,
      nearestHundredDivisibleByThree(num) {
        const nextHundred = Math.ceil(num / 100) * 100;
        return nextHundred + (nextHundred % 3 === 0 ? 0 : 300 - (nextHundred % 300));
      },
      toSelfDetail() {
        const { self, orgId } = this;

        if (self.allyId == orgId) {
          if (this.details.level == 'KA') return;
          uni.navigateTo({
            url: `/subpkgProfit/pages/index-ally-detail/index-ally-detail`
          });
          return;
        }
        uni.navigateTo({
          url: `/subpkgProfit/pages/index-ally-detail/index-ally-detail?allyId=${self.allyId}`
        });
      },
    }
  }
</script>

<style lang="scss" scoped>
.partner-list-contain {
  padding: 20rpx 0 40rpx;
}
.filter-box {
  margin: 0 24rpx;
  .search-box {
    width: 566rpx;
    height: 88rpx;
    padding: 8rpx;
    box-sizing: border-box;
    border-radius: 16rpx;
    background-color: #fff;
  }
  .search-content {
    height: 72rpx;
    background-color: #FAFAFA;
    border-radius: 8rpx;
    .search-icon {
      width: 72rpx;
      height: 32rpx;
      background-size: 32rpx 32rpx;
      background-position: 16rpx center;
      background-repeat: no-repeat;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-partner-search-icon.png');
    }
    .search-input {
      flex: 1;
      height: 72rpx;
      background-color: #FAFAFA;
      font-size: 28rpx;
    }
  }
  .sort-item {
    padding-right: 32rpx;
    font-size: 24rpx;
    line-height: 34rpx;
    color: #6F6A67;
    background-size: 32rpx 32rpx;
    background-repeat: no-repeat;
    background-position: right center;
  }
}

.partner-list-box {
  margin: 24rpx;
  padding: 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  .partner-list-title {
    margin-bottom: 24rpx;
    font-size: 28rpx;
    line-height: 40rpx;
    letter-spacing: 1rpx;
    .title {
      color: #2E2C2B;
      font-weight: bold;
    }
    .title-sub {
      margin-left: 8rpx;
      color: #6F6A67;
    }
  }
}

.self-list {
  margin-bottom: 24rpx;
  border-bottom: 1px solid #EBE9E7;
}
.self-box {
  padding: 24rpx;
  margin-bottom: 24rpx;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #EBE9E7;
  .self-name {
    font-size: 28rpx;
    line-height: 40rpx;
    font-weight: bold;
    letter-spacing: 1rpx;
    color: #2E2C2B;
  }
  .shadow-box {
    margin-top: 16rpx;
    padding: 16rpx;
    background: #FAFAFA;
    border-radius: 4px;
    .self-title {
      margin-bottom: 16rpx;
      font-size: 24rpx;
      line-height: 34rpx;
      color: #6F6A67;
    }
    .self-income {
      color: #2E2C2B;
      .income-icon {
        font-size: 28rpx;
        line-height: 40rpx;
        font-weight: bold;
      }
      .income-money {
        margin: 0 8rpx;
        font-weight: bold;
        font-size: 36rpx;
        line-height: 32rpx;
      }
      .income-unit {
        font-size: 28rpx;
      }
    }
    .self-order {
      font-size: 24rpx;
      line-height: 34rpx;
      color: #979494;
    }
  }
}

.direct-list {
  margin-bottom: 24rpx;
  border-bottom: 1px solid #EBE9E7;
}

.partner-list-empty {
  font-size: 26rpx;
  letter-spacing: 1rpx;
  color: #999;
  line-height: 120px;
  text-align: center;
}
</style>
