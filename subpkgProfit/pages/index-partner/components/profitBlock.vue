<template>
	<view class="profit-block">
    <view class="profit-block-bg" v-if="false"></view>
		<view class="tab-box" v-if="all.countAlly && all.countAlly > 1">
      <view class="tab-item" :class="{'tab-item-active': tab === 1}" @click="changeTab(1)">
        <view class="tab-item-text">团队收益</view>
      </view>
      <view class="tab-item" :class="{'tab-item-active': tab === 2}" @click="changeTab(2)">
        <view class="tab-item-text">直营收益</view>
      </view>
      <view class="tab-item-bg" :class="{'tab-item-bg-2': tab === 2}"></view>
    </view>
    <view class="tab-content" v-if="all.countAlly && all.countAlly > 1">
      <swiper :current="tab - 1" @change="onSwiperChange">
        <swiper-item>
          <view class="tab-block-item">
            <view class="flex-row-between">
              <view class="profit-left">
                <view class="profit-left-banner">
                  <view class="profit-left-title">本月收益</view>
                  <view class="profit-left-value">
                    <view class="profit-icon">￥</view>
                    <view class="profit-money">{{ all.thisMonthData ? formatIncomeAmount(all.thisMonthData.dataset.assetAccountDeposit).money : 0 }}</view>
                    <view class="profit-unit">{{ all.thisMonthData ? formatIncomeAmount(all.thisMonthData.dataset.assetAccountDeposit).unit : '' }}</view>
                  </view>
                  <view class="profit-order">
                    <text>订单：</text>
                    <text>{{ all.thisMonthData ? all.thisMonthData.dataset.countOrder : 0 }}</text>
                  </view>
                  <view class="profit-left-chart">
                    <qiun-data-charts canvas2d type="area" :opts="opts" :chartData="dayChartData"  :inScrollView="true" :pageScrollTop="pageScrollTop" tooltipFormat="partnerDayToolTips" :reshow="tab == 1" />
                  </view>
                </view>
                
              </view>
              <view class="profit-right">
                <view class="profit-right-banner">
                  <view class="profit-right-title">上月收益</view>
                  <view class="profit-right-value">
                    <view class="profit-icon">￥</view>
                    <view class="profit-money">{{ all.LastMonthData ? formatIncomeAmount(all.LastMonthData.dataset.assetAccountDeposit).money : 0 }}</view>
                    <view class="profit-unit">{{ all.LastMonthData ? formatIncomeAmount(all.LastMonthData.dataset.assetAccountDeposit).unit : '' }}</view>
                  </view>
                  <view class="profit-order">
                    <text>订单：</text>
                    <text>{{ all.LastMonthData ? all.LastMonthData.dataset.countOrder : 0 }}</text>
                  </view>
                  <view class="profit-right-chart">
                    <qiun-data-charts canvas2d type="area" :opts="opts2" :chartData="monthChartData"  :inScrollView="true" :pageScrollTop="pageScrollTop" tooltipFormat="partnerMonthToolTips" :reshow="tab == 1"/>
                  </view>
                </view>
              </view>
            </view>
            <view class="profit-left-data flex-row">
              <view class="profit-left-data-item flex-column">
                <view class="profit-data-value">{{ all.countStore ? all.countStore : 0 }}</view>
                <view class="profit-data-title">门店数</view>
              </view>
              <view class="profit-left-data-item flex-column">
                <view class="profit-data-value">{{ all.countTerminal ? all.countTerminal.countWorking : 0 }}</view>
                <view class="profit-data-title">门店机柜</view>
              </view>
              <view class="profit-left-data-item flex-column">
                <view class="profit-data-value">{{ all.countTerminal ? all.countTerminal.countStock : 0 }}</view>
                <view class="profit-data-title">库存机柜</view>
              </view>
            </view>
          </view>
        </swiper-item>
        <swiper-item>
          <view class="tab-block-item">
            <view class="flex-row-between">
              <view class="profit-left">
                <view class="profit-left-banner">
                  <view class="profit-left-title">今日收益</view>
                  <view class="profit-left-value">
                    <view class="profit-icon">￥</view>
                    <view class="profit-money">{{ direct.todayData ? formatIncomeAmount(direct.todayData.dataset.assetAccountDeposit).money : 0 }}</view>
                    <view class="profit-unit">{{ direct.todayData ? formatIncomeAmount(direct.todayData.dataset.assetAccountDeposit).unit : '' }}</view>
                  </view>
                  <view class="profit-order">
                    <text>订单：</text>
                    <text>{{ direct.todayData ? direct.todayData.dataset.countOrder : 0 }}</text>
                  </view>
                  <view class="profit-left-chart">
                    <qiun-data-charts canvas2d type="area" :opts="opts" :chartData="directDayChartData" :inScrollView="true" :pageScrollTop="pageScrollTop" tooltipFormat="partnerDayToolTips" :reshow="tab == 2"/>
                  </view>
                </view>
              </view>
              <view class="profit-right">
                <view class="profit-right-banner">
                  <view class="profit-right-title">本月收益</view>
                  <view class="profit-right-value">
                    <view class="profit-icon">￥</view>
                    <view class="profit-money">{{ direct.thisMonthData ? formatIncomeAmount(direct.thisMonthData.dataset.assetAccountDeposit).money : 0 }}</view>
                    <view class="profit-unit">{{ direct.thisMonthData ? formatIncomeAmount(direct.thisMonthData.dataset.assetAccountDeposit).unit : '' }}</view>
                  </view>
                  <view class="profit-order">
                    <text>订单：</text>
                    <text>{{ direct.thisMonthData ? direct.thisMonthData.dataset.countOrder : 0 }}</text>
                  </view>
                  <view class="profit-right-chart">
                    <qiun-data-charts canvas2d type="area" :opts="opts2" :chartData="directMonthChartData" :inScrollView="true" :pageScrollTop="pageScrollTop" tooltipFormat="partnerMonthToolTips" :reshow="tab == 2"/>
                  </view>
                </view>
              </view>
            </view>
            <view class="profit-left-data flex-row">
              <view class="profit-left-data-item flex-column">
                <view class="profit-data-value">{{ direct.countStore ? direct.countStore : 0 }}</view>
                <view class="profit-data-title">门店数</view>
              </view>
              <view class="profit-left-data-item flex-column">
                <view class="profit-data-value">{{ direct.countTerminal ? direct.countTerminal.countWorking : 0 }}</view>
                <view class="profit-data-title">门店机柜</view>
              </view>
              <view class="profit-left-data-item flex-column">
                <view class="profit-data-value">{{ direct.countTerminal ? direct.countTerminal.countStock : 0 }}</view>
                <view class="profit-data-title">库存机柜</view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="tab-content" v-else>
      <view class="tab-block-item">
        <view class="flex-row-between">
          <view class="profit-left">
            <view class="profit-left-banner">
              <view class="profit-left-title">本月收益</view>
              <view class="profit-left-value">
                <view class="profit-icon">￥</view>
                <view class="profit-money">{{ all.thisMonthData ? formatIncomeAmount(all.thisMonthData.dataset.assetAccountDeposit).money : 0 }}</view>
                <view class="profit-unit">{{ all.thisMonthData ? formatIncomeAmount(all.thisMonthData.dataset.assetAccountDeposit).unit : '' }}</view>
              </view>
              <view class="profit-order">
                <text>订单：</text>
                <text>{{ all.thisMonthData ? all.thisMonthData.dataset.countOrder : 0 }}</text>
              </view>
              <view class="profit-left-chart">
                <qiun-data-charts canvas2d type="area" :opts="opts" :chartData="dayChartData" :inScrollView="true" :pageScrollTop="pageScrollTop" tooltipFormat="partnerDayToolTips" />
              </view>
            </view>
            
          </view>
          <view class="profit-right">
            <view class="profit-right-banner">
              <view class="profit-right-title">上月收益</view>
              <view class="profit-right-value">
                <view class="profit-icon">￥</view>
                <view class="profit-money">{{ all.LastMonthData ? formatIncomeAmount(all.LastMonthData.dataset.assetAccountDeposit).money : 0 }}</view>
                <view class="profit-unit">{{ all.LastMonthData ? formatIncomeAmount(all.LastMonthData.dataset.assetAccountDeposit).unit : '' }}</view>
              </view>
              <view class="profit-order">
                <text>订单：</text>
                <text>{{ all.LastMonthData ? all.LastMonthData.dataset.countOrder : 0 }}</text>
              </view>
              <view class="profit-right-chart">
                <qiun-data-charts canvas2d type="area" :opts="opts2" :chartData="monthChartData" :inScrollView="true" :pageScrollTop="pageScrollTop" tooltipFormat="partnerMonthToolTips" />
              </view>
            </view>
          </view>
        </view>
        <view class="profit-left-data flex-row">
          <view class="profit-left-data-item flex-column">
            <view class="profit-data-value">{{ all.countStore ? all.countStore : 0 }}</view>
            <view class="profit-data-title">门店数</view>
          </view>
          <view class="profit-left-data-item flex-column">
            <view class="profit-data-value">{{ all.countTerminal ? all.countTerminal.countWorking : 0 }}</view>
            <view class="profit-data-title">门店机柜</view>
          </view>
          <view class="profit-left-data-item flex-column">
            <view class="profit-data-value">{{ all.countTerminal ? all.countTerminal.countStock : 0 }}</view>
            <view class="profit-data-title">库存机柜</view>
          </view>
        </view>
      </view>
    </view>
	</view>
</template>

<script>
import { formatIncomeAmount } from '@/subpkgProfit/utils/money.js';

export default {
  props: {
    direct: {
      type: Object,
      default: {},
    },
    all: {
      type: Object,
      default: {},
    },
    orgId: {
      type: Number,
      default: null,
    }
  },
  data() {
    return {
      pageScrollTop: 0, 
      tab: 1,
      dayChartData: {},
      monthChartData: {},
      directDayChartData: {},
      directMonthChartData: {},
      chartOpts: {
        padding: [0, 0, 0, 1],
        dataLabel: false,
        xAxis: {
          disabled: true,
          disableGrid: true,
          axisLine: false,
          boundaryGap: "justify",
        },
        yAxis: {
          disabled: true,
          disableGrid: true,
          data: [
            {
              min: 0,
            }
          ]
        },
        legend: {
          show: false,
        },
				extra: {
          area: {
            type: "curve",
            opacity: 0.6,
            addLine: true,
            width: 1,
            gradient: true,
          },
          tooltip: {
            showBox: true,
            legendShow: false,
            fontSize: 12,
            showArrow: false,
            bgColor: '#fff',
            bgOpacity: 1,
            borderWidth: 1,
            borderColor: '#EBE9E7', 
            borderRadius: 8,
            lineHeight: 18,
            gridColor: '#DDDDDD',
            fontColor: '#2E2C2B'
          }
				},
      },
    };
  },
  computed: {
    opts() {
      const { chartOpts } = this;

      return { color: ["#fff","#fff",], ...chartOpts,  }
    },
    opts2 () {
      const { chartOpts } = this;

      return { color: ["#3EAAF9","#3EAAF9",], ...chartOpts }
    }
  },
  onPageScroll(e) {
    this.pageScrollTop = e.scrollTop
  },
  watch: {
    direct() {
      this.initChart();
    },
    all() {
      this.initChart();
    }
  },
  methods: {
    changeTab(tab) {
      this.tab = tab;

      this.$emit('changeTab', this.tab)
    },
    onSwiperChange(e) {
      this.tab = e.detail.current + 1;

      this.$emit('changeTab', this.tab)
    },
    toPartnerList() {
      const { tab, orgId } = this;

      uni.navigateTo({
        url: `/subpkgProfit/pages/partner-list/partner-list?tab=${tab}&orgId=${orgId}`
      });
    },
    // 公司营收 绘制
    initChart() {
      const { monthChart, dayChart } = this.all;
      const directMonthChart = this.direct.monthChart;
      const directDayChart = this.direct.dayChart;

      const allDayChartData = dayChart.map(item => item.dataset.assetAccountDeposit / 100);
      const allDayChartCategories = dayChart.map(item => item.date.slice(-2));
      const allMonthChartData = monthChart.map(item => item.dataset.assetAccountDeposit / 100);
      const allMonthChartCategories = monthChart.map(item => item.date.slice(-5, -3));
      const directDayChartData = directDayChart.map(item => item.dataset.assetAccountDeposit / 100);
      const directDayChartCategories = directDayChart.map(item => item.date.slice(-2));
      const directMonthChartData = directMonthChart.map(item => item.dataset.assetAccountDeposit / 100);
      const directMonthChartCategories = directMonthChart.map(item => item.date.slice(-5, -3));

      let res = {
        categories: allDayChartCategories,
        series: [{
          name: "收益",
          legendShape: "circle",
          color: "#3EAAF9",
          pointShape: "none",
          data: allDayChartData,
        }],
      };
      let res2 = {
        categories: allMonthChartCategories,
        series: [{
          name: "收益",
          legendShape: "circle",
          color: "#3EAAF9",
          pointShape: "none",
          data: allMonthChartData,
        }],
      };
      let res3 = {
        categories: directDayChartCategories,
        series: [{
          name: "收益",
          legendShape: "circle",
          color: "#3EAAF9",
          pointShape: "none",
          data: directDayChartData,
        }],
      };
      let res4 = {
        categories: directMonthChartCategories,
        series: [{
          name: "收益",
          legendShape: "circle",
          color: "#3EAAF9",
          pointShape: "none",
          data: directMonthChartData,
        }],
      };
      this.dayChartData = JSON.parse(JSON.stringify(res));
      this.monthChartData = JSON.parse(JSON.stringify(res2));
      this.directDayChartData = JSON.parse(JSON.stringify(res3));
      this.directMonthChartData = JSON.parse(JSON.stringify(res4));
    },
    formatIncomeAmount,
  }
};
</script>

<style lang="scss" scoped>
::v-deep .tab-content swiper {
  height: 232px !important;
}
.profit-block {
  position: relative;
  padding: 16px 32rpx;
  margin: 14px 24rpx 0;
  background-color: #FFFFFF;
  border-radius: 8px;
  .profit-block-bg {
    position: absolute;
    top: 4rpx;
    left: 50%;
    transform: translateX(-50%);
    background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-ally-profit-banner-bg.png');
    width: 694rpx;
    height: 160rpx;
    background-repeat: no-repeat;
    background-size: 694rpx 160rpx;
  }
}

.tab-box {
  position: relative;
  width: 654rpx;
  height: 32px;
  padding: 0 4rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  background: rgba(0,0,0,0.04);
  border-radius: 12rpx;
  box-sizing: border-box;
  .tab-item {
    position: relative;
    z-index: 2;
  }
  .tab-item-text {
    width: 315rpx;
    height: 28px;
    text-align: center;
    line-height: 28px;
    font-size: 14px;
    font-weight: bold;
    color: #979494;
    letter-spacing: 1rpx;
  }
  .tab-item-active {
    .tab-item-text {
      background: linear-gradient(180.00000042688666deg, #2E2C2B 0%, #2E2C2B 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .tab-item-bg {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 315rpx;
    height: 28px;
    box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.04);
    border-radius: 8rpx;
    background-color: #fff;
    transition: .3s;
  }
  .tab-item-bg-2 {
    left: auto;
    right: 2px;
  }
}

.tab-content {
  .tab-block-item {
    height: 234px;
  }
}

.flex-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.profit-left {
  width: 302rpx;
  .profit-left-banner {
    height: 158px;
    background: #fff;
    border-radius: 4px 4px 4px 4px;
    box-sizing: border-box;
    .profit-left-title {
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 20px;
      color: #6F6A67;
      font-weight: bold;
      letter-spacing: 1rpx;
    }
    .profit-left-value {
      display: flex;
      align-items: flex-end;
      height: 32px;
      margin-bottom: 4px;
      color: #2E2C2B;
      .profit-icon {
        font-size: 14px;
        line-height: 14px;
        vertical-align: bottom;
        font-weight: bold;
      }
      .profit-money {
        margin: 0 8rpx;
        font-size: 24px;
        line-height: 24px;
        font-weight: bold;
      }
      .profit-unit {
        font-size: 14px;
        line-height: 14px;
      }
    }
    .profit-order {
      color: #979494;
      font-size: 12px;
      line-height: 17px;
    }
    .profit-left-chart {
      width: 100%;
      height: 64px;
      margin-top: 8px;
    }
  }
}
.profit-right {
  width: 302rpx;
  .profit-right-banner {
    width: 100%;
    height: 158px;
    border-radius: 4px 4px 4px 4px;
    box-sizing: border-box;
    overflow: hidden;
    .profit-right-title {
      margin-bottom: 8px;
      color: #6F6A67;
      font-size: 14px;
      line-height: 20px;
      font-weight: bold;
    }
    .profit-right-value {
      display: flex;
      align-items: flex-end;
      height: 32px;
      margin-bottom: 4px;
      color: #2E2C2B;
      .profit-icon {
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
      }
      .profit-money {
        margin: 0 4px;
        font-size: 24px;
        line-height: 1;
        font-weight: bold;
      }
      .profit-unit {
        font-size: 14px;
        line-height: 14px;
      }
    }
    .profit-order {
      color: #6F6A67;
      font-size: 12px;
      line-height: 17px;
    }
    .profit-right-chart {
      width: 302rpx;
      height: 64px;
      margin-top: 8px;
    }
  }
}
.profit-left-data {
  width: 100%;
  padding: 8px 0 8px;
  margin-top: 16px;
  border: 1px solid #EBE9E7;
  border-radius: 4px 4px 4px 4px;
  box-sizing: border-box;
  .profit-left-data-item {
    flex: 1;
  }
  .profit-team-item {
    position: relative;
  }
  .profit-data-value {
    margin-bottom: 2px;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
    color: #2E2C2B;
  }
  .profit-data-title {
    font-size: 12px;
    line-height: 17px;
    color: #6F6A67;
  }
}
</style>
