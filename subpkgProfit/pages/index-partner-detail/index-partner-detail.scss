.index-partner-detail {
  padding: 24rpx
}

.title {
  width: 100%;
  padding: 16rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  color: #2E2C2B;
  font-size: 28rpx;
  line-height: 40rpx;
  font-weight: bold;
  letter-spacing: 1rpx;
  box-sizing: border-box;
}

.info-box {
  padding: 32rpx 32rpx 24rpx;
  background-color: #fff;
  border-radius: 8px;
  .info-row {
    margin-bottom: 32rpx;
    &:nth-last-child(1) {
      padding-bottom: 32rpx;
      border-bottom: 1px solid #EBE9E7;
    }
    .info-row-title {
      flex-shrink: 0;
      margin-right: 40rpx;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #6F6A67;
      letter-spacing: 1rpx;
    }
    .info-row-value {
      font-size: 28rpx;
      line-height: 40rpx;
      color: #2E2C2B;
      letter-spacing: 1rpx;
    }
  }
  .info-sub-box {
    .sub-title {
      margin-bottom: 24rpx;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #6F6A67;
      letter-spacing: 1rpx;
    }
    .info-sub-row {
      display: flex;
      justify-content: space-between;
      padding: 16rpx;
      margin-bottom: 16rpx;
      background-color: #FAFAFA;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #2E2C2B;
      letter-spacing: 1rpx;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .info-sub-row-title {
        color: #6F6A67;
      }
      .info-sub-row-right {
        text-align: right;
      }
      .info-sub-row-company {
        margin-top: 8rpx;
      }
    }
  }
}

.gmv-chart-box {
  margin-top: 24rpx;
  padding: 32rpx 32rpx 24rpx;
  background-color: #fff;
  border-radius: 8px;
  .chart-data {
    display: flex;
    .chart-data-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .chart-data-item-value {
        margin-bottom: 8rpx;
        font-size: 32rpx;
        line-height: 44rpx;
        font-weight: bold;
      }
      .chart-data-item-title {
        font-size: 24rpx;
        color: #979494;
        line-height: 34rpx;
        letter-spacing: 1rpx;
      }
    }
  }
  .chart-box {
    height: 122px;
  }
}

.device-chart-box {
  margin-top: 24rpx;
  padding: 32rpx 32rpx 24rpx;
  background-color: #fff;
  border-radius: 8px;
  .chart-data {
    display: flex;
    .chart-data-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .chart-data-item-value {
        margin-bottom: 8rpx;
        font-size: 32rpx;
        line-height: 44rpx;
        font-weight: bold;
      }
      .chart-data-item-title {
        font-size: 24rpx;
        color: #979494;
        line-height: 34rpx;
        letter-spacing: 1rpx;
      }
    }
  }
}