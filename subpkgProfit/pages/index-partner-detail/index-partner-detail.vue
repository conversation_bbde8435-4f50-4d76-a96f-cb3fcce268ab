<template>
	<view class="index-partner-detail">
		<view class="title">{{ detail.allyName }}</view>
		<view class="info-box">
			<view class="info-row flex-row-between">
				<view class="info-row-title">联盟商：</view>
				<view class="info-row-value">{{ detail.allyName }}</view>
			</view>
			<view class="info-row flex-row-between" v-if="detail.allyDetail">
				<view class="info-row-title">负责人：</view>
				<view class="info-row-value" v-if="detail.allyDetail">{{ detail.allyDetail.responsiblePerson.name }}</view>
			</view>
			<view class="info-row flex-row-between" v-if="detail.allyDetail">
				<view class="info-row-title">加盟日：</view>
				<view class="info-row-value" v-if="detail.allyDetail">{{ detail.allyDetail.createdDate.slice(0, 10) }}</view>
			</view>
			<view class="info-sub-box">
				<view class="sub-title">商户号</view>
				<view class="info-sub-row">
					<view class="info-sub-row-title">微信：</view>
					<view class="info-sub-row-right">
						<view>{{ detail.accountWechatMchId }}</view>
						<view class="info-sub-row-company">{{ detail.accountWechatMchName }}</view>
					</view>
				</view>
				<view class="info-sub-row">
					<view class="info-sub-row-title">支付宝：</view>
					<view class="info-sub-row-right">
						<view>{{ detail.accountAlipayMchId }}</view>
						<view class="info-sub-row-company">{{ detail.accountAlipayMchName }}</view>
					</view>
				</view>
			</view>
		</view>
		<view class="gmv-chart-box">
			<view class="chart-data">
				<view class="chart-data-item">
					<view class="chart-data-item-value">{{ detail.all ? detail.all.countStore : 0 }}</view>
					<view class="chart-data-item-title">门店数</view>
				</view>
				<view class="chart-data-item">
					<view class="chart-data-item-value">{{ detail.all ? detail.all.countTerminal.countWorking : 0 }}</view>
					<view class="chart-data-item-title">门店机柜</view>
				</view>
				<view class="chart-data-item">
					<view class="chart-data-item-value">{{ detail.all ? detail.all.dateRange.dataset.countOrder : 0 }}</view>
					<view class="chart-data-item-title">订单总数</view>
				</view>
				<view class="chart-data-item">
					<view class="chart-data-item-value">{{ detail.all ? formatIncomeAmount(detail.all.dateRange.dataset.assetAccountDeposit).money + formatIncomeAmount(detail.all.dateRange.dataset.assetAccountDeposit).unit : 0 }}</view>
					<view class="chart-data-item-title">总收益</view>
				</view>
			</view>
			<view class="chart-box">
				<qiun-data-charts 
					type="mix"
					:opts="opts"
					:chartData="chartData"
				/>
			</view>
		</view>
		<view class="device-chart-box" v-if="false">
			<view>
				<view class="chart-data">
					<view class="chart-data-item">
						<view class="chart-data-item-value">321</view>
						<view class="chart-data-item-title">总机柜数</view>
					</view>
					<view class="chart-data-item">
						<view class="chart-data-item-value">300</view>
						<view class="chart-data-item-title">点位数</view>
					</view>
					<view class="chart-data-item">
						<view class="chart-data-item-value">83%</view>
						<view class="chart-data-item-title">在线率</view>
					</view>
				</view>
				<view class="chart-box"></view>
			</view>
			<view>
				<view class="chart-data">
					<view class="chart-data-item">
						<view class="chart-data-item-value">321</view>
						<view class="chart-data-item-title">宝权益</view>
					</view>
					<view class="chart-data-item">
						<view class="chart-data-item-value">92%</view>
						<view class="chart-data-item-title">持宝率</view>
					</view>
					<view class="chart-data-item">
						<view class="chart-data-item-value">32</view>
						<view class="chart-data-item-title">库存数</view>
					</view>
				</view>
				<view class="chart-box"></view>
			</view>
		</view>
	</view>
</template>

<script src="./index-partner-detail.js">
	
</script>

<style lang="scss" scoped>
@import './index-partner-detail.scss'
</style>
