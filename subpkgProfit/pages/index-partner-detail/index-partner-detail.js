import { findPartnerDetail } from '@/subpkgProfit/api/indexPartnerDetail.js';
import { formatIncomeAmount } from '@/subpkgProfit/utils/money.js';

export default {
  data() {
    return {
      allyId: null,
      detail: {},
      chartData: {},
      opts: {
        color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
        padding: [15,15,0,15],
        enableScroll: false,
        dataLabel: false,
        legend: {},
        xAxis: {
          disabled: false,
          disableGrid: true,
        },
        yAxis: {
          disabled: true,
          disableGrid: true,
          data: [
            {
              position: "left",
            },
            {
              position: "right",
              min: 0,
              textAlign: "left"
            },
          ]
        },
        extra: {
        }
      }
    };
  },
  onLoad(options) {
    this.allyId = parseInt(options.id, 10);
    this.fetchData();
  },
  methods: {
    async fetchData() {
      const { allyId } = this;
      const res = await findPartnerDetail({ allyId });
      this.initChart(res.data.findPartnerById);
      this.detail = res.data.findPartnerById;
    },
    initChart(detail) {
      const { all } = detail;
      const { monthChart } = all;

      const categories = monthChart.map(item => item.date.slice(5, 7));
      const profitData = monthChart.map(item => item.dataset.assetAccountDeposit / 100);
      const orderData = monthChart.map(item => item.dataset.countOrder);

      let res = {
          categories,
          series: [
            {
              name: "月收益",
              type: "line",
              style: "curve",
              color: "#0D7BFC",
              data: profitData,
            },
            {
              name: "订单",
              type: "column",
              color: "#0D7BFC",
              data: orderData
            },
          ]
        };
      this.chartData = JSON.parse(JSON.stringify(res));
    },
    formatIncomeAmount,
  },
}