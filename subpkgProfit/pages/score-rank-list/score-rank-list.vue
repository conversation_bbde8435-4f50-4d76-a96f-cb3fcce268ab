<template>
	<view class="score-rank-list">
		<mNav
			title="联盟商排行榜"
			titleColor="#fff"
			topBarColor="transparent"
			statusBarColor="transparent"
			:isShowBack="true"
			:bgImage="'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/score-rank-list-bg.png'"
		/>
		<view class="bg" :style="{'--nav-height': -navBarHeight + 'px', '--nav-height-number': navBarHeight + 236 + 'px',}"></view>
		<view class="score-rank-content">
			<view class="activity-info flex-row-between" @click="toActivityInfo">
				<view class="title">百日冲刺活动</view>
				<view class="flex-row">
					<view class="info-title">活动说明</view>
					<view class="info-icon"></view>
				</view>
			</view>
			<view class="banner-title">
				<view class="banner-title-img"></view>
				<view class="banner-title-sub-img"></view>
			</view>
			<view class="rank-list">
				<view class="rank-item-box" :class="{'rank-item-active': rank.allyId == orgId }" v-for="(rank, key) in list" :key="key" @click="clickRow(rank)">
					<view class="rank-item flex-row">
						<view class="rank-icon" :class="[`rank-icon-${key + 1}`]">{{ key > 2 ? key + 1 : '' }}</view>
						<view class="rank-content">
							<view class="rank-company">{{ rank.ally && rank.ally.companyFullName ? rank.ally.companyFullName : '未知' }}</view>
							<view class="rank-type flex-row">
								<view class="score-item flex-row">
									<view>积分：</view>
									<view>{{ rank.totalScore }}</view>
								</view>
								<view class="bonus-item flex-row">
									<view>预期奖金：</view>
									<view>￥{{ rank.estimatedBonus }}</view>
								</view>
							</view>
						</view>
					</view>
					<view class="rank-item-more" v-if="rank.allyId == orgId"></view>
				</view>
			</view>
			<view class="rank-tips" v-if="list.length">榜单仅显示排名位于前20名的联盟商</view>
		</view>
		<view class="self-box">
			<view class="self-content flex-row">
				<view class="self-rank">
					<block v-if="!selfDetail.rank || selfDetail.rank > 3">
						<view class="self-rank-title">我的排名</view>
						<view class="self-rank-number" v-if="selfDetail.rank">{{ selfDetail.rank }}</view>
						<view class="self-rank-empty" v-else>未上榜</view>
					</block>
					<block v-else>
						<view class="self-rank-medal" :class="[`self-rank-medal-${selfDetail.rank}`]"></view>
					</block>
				</view>
				<view class="self-info flex-row-between" v-if="selfDetail.ally" @click="toHistory">
					<view class="">
						<view class="self-company">{{ selfDetail.ally.companyFullName }}</view>
						<view class="self-info-footer flex-row">
							<view class="self-score">积分：{{ selfDetail.totalScore }}</view>
							<view class="self-bonus">预期奖金：￥{{ selfDetail.estimatedBonus }}</view>
						</view>
					</view>
					<view class="self-more"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script src="./score-rank-list.js">
	
</script>

<style lang="scss" scoped>
@import "./score-rank-list.scss";
</style>
