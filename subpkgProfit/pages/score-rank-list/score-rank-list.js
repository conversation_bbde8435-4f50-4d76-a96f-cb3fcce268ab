import { findRankingTable, findRankResult } from '@/subpkgProfit/api/scoreRankList.js';
import { orgId } from '@/utils/role.js';
import mNav from "@/components/m-nav/m-nav.vue";

export default {
  data() {
    return {
      list: [],
      selfDetail: {},
    };
  },
  computed: {
    orgId,
    navBarHeight() {
			return (
				getApp().globalData.navBarHeight + getApp().globalData.statusBarHeight
			);
		},
  },
  components: {
    mNav,
  },
  async onLoad() {
    this.findSelfResult();
    this.fetchList();
  },
  async onPullDownRefresh() {
    this.findSelfResult();
		this.fetchList().then(() => {
      uni.stopPullDownRefresh();
    })
	},
  methods: {
    async fetchList() {
      let res = await findRankingTable();

      this.list = res.data.findRankingTable;
    },
    async findSelfResult() {
      let res = await findRankResult(this.orgId);

      this.selfDetail = res.data.findRankResult;
    },
    clickRow(item) {
      if (item && item.allyId !== this.orgId) return;

      this.toHistory();
    },
    toHistory() {
      uni.navigateTo({
        url: '/subpkgProfit/pages/score-history-record/score-history-record',
      });
    },
    toActivityInfo() {
      uni.navigateTo({
        url: '/subpkgProfit/pages/score-activity-info/score-activity-info',
      });
    },
  }
}