
.aftersale-banner {
  position: relative;
  margin: 0 24rpx 24rpx;
  padding: 32rpx;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;
}

.aftersale-rule {
  position: absolute;
  right: 32rpx;
  top: 32rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  line-height: 34rpx;
  color: #B2B0AF;
  .rule-icon::after {
    content: "";
    display: block;
    font-family: "Wukong Font", serif;
    content: "\e904";
    font-size: 32rpx;
  }
}

.aftersale-sum {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #EBE9E7;
  .aftersale-sum-title {
    margin-bottom: 8rpx;
    font-size: 32rpx;
  }
  .aftersale-sum-num {
    font-size: 64rpx;
    line-height: 90rpx;
    font-weight: bold;
  }
}

.aftersale-detail {
  .aftersale-sender,
  .aftersale-receiver {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: #2E2C2B;
    .aftersale-title {
      flex-shrink: 0;
      width: 104rpx;
      color: #6F6A67;
    }
  }
  .aftersale-sender {
    margin-bottom: 16rpx;
  }
}

.table-box {
  padding: 32rpx;
  margin: 0 24rpx;
  background: #FFFFFF;
  border-radius: 8px 8px 8px 8px;

  .table-content {
    font-size: 26rpx;
    .table-tr-1 {
      flex-shrink: 0;
      width: 320rpx;
    }
    .table-tr-2 {
      flex-shrink: 0;
      width: 60rpx;
    }
    .table-tr-3 {
    }
    .table-header {
      color: #6F6A67;
    }
    .table-row {
      margin-top: 32rpx;
      color: #2E2C2B;
    }
  }
}