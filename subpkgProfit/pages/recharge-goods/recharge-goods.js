import { getRechargeGoods, createOrder, getCountRechargeUsage } from '@/subpkgProfit/api/recharge-goods';
import { flatNodeList } from '@/utils/function';
import { orgId, orgName } from '@/utils/role';
import { appId } from '@/utils/constant';

export default {
  data() {
    return {
      catalog: 'MOBILE',
      tabList: [{
        text: '手机号验证',
        value: 'MOBILE'
      }, {
        text: '一般广告',
        value: 'AD_NORMAL'
      }, {
        text: '视频广告',
        value: 'AD_VIDEO'
      }],
      usageDetail: {},
      list: [],
      product: {},
      productId: '',
    };
  },
  computed: {
    orgId,
    orgName
  },
  async onLoad() {
    this.fetchGoodsAndUsage();
  },
  async onPullDownRefresh() {
		await this.fetchGoodsAndUsage();
		uni.stopPullDownRefresh();
	},
  methods: {
    changeCatalog(item) {
      this.product = {};
      this.productId = null;
      this.catalog = item.value;
      this.fetchGoodsAndUsage();
    },
    async fetchGoodsAndUsage() {
      let { catalog, orgId } = this;
      getCountRechargeUsage({ catalog, orgId }).then(res => {
        this.usageDetail = res.data.countRechargeUsage;
      })

      let res = await getRechargeGoods({ catalog });

      this.list = flatNodeList(res.data.findRechargeGoods.edges);
    },
    changeProduct(item) {
      this.product = item;
      this.productId = item.id;
    },
    async toPay() {
      if (!this.productId) {
        uni.showToast({
          icon: "error",
					title: "请选择产品",
					duration: 1000,
        })
        return;
      }

      let res = await createOrder({ orgId: this.orgId, appId: appId, rechargeGoodsId: this.productId });

      uni.requestPayment({
        orderInfo: this.product,
        signType: res.data.signType,
        timeStamp: res.data.timestamp,
        paySign: res.data.paySign,
        nonceStr: res.data.nonceStr,
        package: res.data.packageVal,
        success: function() {
          console.log('调用成功')
          uni.showToast({
            icon: "success",
            title: "支付成功",
            duration: 1000,
          })
        },
        fail: err => {
          console.log(err, 'err')
          uni.showToast({
            icon: "error",
            title: "取消支付",
            duration: 1000,
          })
        },
      })
    },
    toHistory() {
      uni.navigateTo({
				url: '/subpkgProfit/pages/recharge-history/recharge-history'
			})
    }
  },
}