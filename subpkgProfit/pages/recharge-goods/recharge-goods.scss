.recharge-goods-contain {
  padding-top: 20px;
  padding-bottom: 180px;
}

.tab-box {
  display: flex;
  margin: 0 24rpx 20px;
  .tab-item {
    margin-right: 30rpx;
    font-size: 24rpx;
    &:nth-last-child(1) {
      margin-right: 0;
    }
  }
  .tab-item-active {
    position: relative;
    font-weight: bold;
    color: $global-color-primary;
    &::after {
      position: absolute;
      left: 50%;
      bottom: -10rpx;
      content: "";
      transform: translateX(-50%);
      display: block;
      width: 100rpx;
      height: 4rpx;
      background-color: $global-color-primary;
    }
  }
}

.info-box {
  padding: 40rpx 24px;
  margin: 0 24rpx 20px;
  background-color: #fff;
  border-radius: 20rpx;
  .info-title {
    position: relative;
    margin-bottom: 36rpx;
    padding-left: 20rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: $global-color-primary;
    line-height: 30rpx;
  }
  .info-title::after {
    position: absolute;
    left: 0;
    top: 3rpx;
    display: block;
    content: "";
    width: 6rpx;
    height: 24rpx;
    background-color: $global-color-primary;
  }
  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding-bottom: 20rpx;
    font-size: 26rpx;
    letter-spacing: 2rpx;
    border-bottom: 1px solid #eee;
    &:nth-last-child(1) {
      margin-bottom: 0;
      border-bottom: none;
      padding-bottom: 0;
    }
    .info-row-left {
      flex-shrink: 0;
      width: 200rpx;
      font-weight: bold;
    }
    .info-row-right {
      flex: 1;
      text-align: right;
      font-weight: bold;
    }
  }
}

.ability-box {
  .info-row {
    margin-bottom: 10rpx;
    padding-bottom: 0rpx;
    border-bottom: none;
    line-height: 36rpx;
  }
}

.product-box {
  padding: 40rpx 24px 8rpx;
  margin: 0 24rpx;
  background-color: #fff;
  border-radius: 20rpx;
  .product-box-title {
    position: relative;
    margin-bottom: 36rpx;
    padding-left: 20rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: $global-color-primary;
    line-height: 30rpx;
  }
  .product-box-title::after {
    position: absolute;
    left: 0;
    top: 3rpx;
    display: block;
    content: "";
    width: 6rpx;
    height: 24rpx;
    background-color: $global-color-primary;
  }
}

.list {
  display: flex;
  flex-wrap: wrap;
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16rpx 20px;
    margin-bottom: 20px;
    margin-right: 26rpx;
    width: 282rpx;
    border: 1px solid #dde;
    margin-right: 20px;
    border-radius: 8rpx;
    font-size: 26rpx;
    letter-spacing: 2rpx;
    font-weight: bold;
    box-sizing: border-box;
    &:nth-child(2n) {
      margin-right: 0;
    }
    .money {
      margin: 8rpx 0 8rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: $global-color-primary;
    }
    .tips {
      font-size: 24rpx;
      color: #999;
      font-weight: normal;
      text-decoration: line-through;
    }
  }
  .item-active {
    color: #fff;
    background-color: $global-color-primary;
    border-color: $global-color-primary;
    .money {
      color: #fff;
    }
    .tips {
      color: #fff;
    }
  }
}


.pay-history {
  margin-top: 10px;
  text-align: center;
  font-size: 26rpx;
  line-height: 26rpx;
  color: $global-color-primary;
  text-decoration: underline;
}
.pay-btn {
  width: 220rpx;
  height: 74rpx;
  margin: 0 auto;
  line-height: 74rpx;
  font-size: 26rpx;
  letter-spacing: 2rpx;
  background-color: $global-color-primary;
  border-radius: 8rpx;
  text-align: center;
  color: #fff;
}
.pay-btn-disabled {
  background-color: #999;
}

.pay-box {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding-top: 20px;
  padding-left: 60rpx;
  padding-right: 60rpx;
  padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
	padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
  background-color: #fff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
  box-sizing: border-box;
  .org-name {
    letter-spacing: 2rpx;
    margin-bottom: 8rpx;
    color: $global-color-primary;
  }
  .pay-box-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 30rpx;
  }
  .pay-product-tips {
    margin-bottom: 20rpx;
    font-size: 26rpx;
    letter-spacing: 2rpx;
  }
  .pay-product-empty-tips {
    color: #999;
  }
  .special-text {
    color: $global-color-primary;
  }
  .pay-money {
    font-size: 32rpx;
    font-weight: bold;
    letter-spacing: 2rpx;
  }
}