<template>
	<view class="recharge-goods-contain">
		<view class="tab-box">
			<view class="tab-item" :class="{'tab-item-active': item.value == catalog}" v-for="(item, key) in tabList" :key="item.value" @click="changeCatalog(item)">{{ item.text }}</view>
		</view>
		<view class="info-box ability-box">
			<view class="info-title">组件能力</view>
			<view class="info-row" v-if="catalog == 'MOBILE'">
				<view>充值微信小程序获取手机号能力，即可在用户租借时获取手机号，用于客服沟通和营销活动。
					只对用户授权下获取成功的手机号收费，同一用户仅需获取一次。</view>
			</view>
			<view class="info-row" v-if="catalog == 'MOBILE'">
					<view>用户手机号会显示在订单详情中，可在后台导出明细。</view>
			</view>
			<view class="info-row" v-if="catalog == 'AD_NORMAL' || catalog == 'AD_VIDEO'">
				<view>充值云服务商CDN公网流量，用于用户获取广告内容。</view>
			</view>
			<view class="info-row" v-if="catalog == 'AD_NORMAL'">
				<view>普通广告内容限制为不超过5M的图片。</view>
			</view>
			<view class="info-row" v-if="catalog == 'AD_VIDEO'">
				<view>视频广告内容限制为不超过25M的短视频。</view>
			</view>
			<view class="info-row" v-if="catalog == 'AD_NORMAL' || catalog == 'AD_VIDEO'">
				<view>如联盟商已有云服务公网流量，可联系客户经理，广告免收费用。</view>
			</view>
		</view>
		<view class="info-box">
			<view class="info-title">能力概况</view>
			<view class="info-row">
				<view class="info-row-left">剩余可用次数:</view>
				<view class="info-row-right">{{ usageDetail.balance }}次</view>
			</view>
			<view class="info-row">
				<view class="info-row-left">充值总数:</view>
				<view class="info-row-right">{{ usageDetail.totalRecharge }}次</view>
			</view>
			<view class="info-row">
				<view class="info-row-left">未出账次数:</view>
				<view class="info-row-right">{{ usageDetail.outstandingUsage }}次</view>
			</view>
			<view class="info-row">
				<view class="info-row-left">已出账次数:</view>
				<view class="info-row-right">{{ usageDetail.totalUsage }}次</view>
			</view>
		</view>
		<view class="product-box">
			<view class="product-box-title">充值套餐</view>
			<view class="list">
				<view class="item" :class="{'item-active': productId === item.id}" v-for="(item, key) in list" :key="item.id" @click="changeProduct(item)">
					<view>{{ item.goodsName }}</view>
					<view class="money">￥{{ item.price / 100 }}</view>
					<view class="tips">原价：{{ item.oldPrice / 100 }}元</view>
				</view>
			</view>
		</view>
		
		<view class="pay-box">
			<view class="org-name">{{ orgName }}</view>
			<view class="pay-box-content">
				<view>
					<view class="pay-product-tips pay-product-empty-tips" v-if="!productId">未选中套餐</view>
					<view class="pay-product-tips" v-else>已选择<text class="special-text">{{ product.goodsName }}</text>套餐</view>
					<view class="pay-money">￥{{ productId ? product.price / 100 : 0 }}</view>
				</view>
				<view>
					<view class="pay-btn" :class="{'pay-btn-disabled': !productId}" @click="toPay">充值</view>
					<view class="pay-history" @click="toHistory">充值（消耗）记录</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script src="./recharge-goods.js">
	
</script>

<style lang="scss" scoped>
@import './recharge-goods.scss'
</style>
