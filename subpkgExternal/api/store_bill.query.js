export const findStoreListQuery = `
    #graphql
    query findOrgDetailPaging(
        $first:Int
        $sort:[String]
        $filter: OrgFilter
        $after: String
        $dir: String
        $routeTypes: [RouteType]!
    ){
        result :findOrgDetailPagingV2(
            first: $first
            sort: $sort
            filter: $filter
            after: $after
            dir: $dir
            routeTypes: $routeTypes
        ){
            edges {
                node {
                    ... on OrgStoreDetailDTO {
                        id
                        externalStoreId
                        name
                        createdDate
                        platformType {
                            name
                            description
                        }
                        orgConfig{
                            contract {
                                rentShare
                            }
                            operatingGoal:setting(key: "OperatingSettingDto") {
                                object
                            }
                            billConfigPowerbank: setting(key: "BillingConfigPowerbankDto"){
                                object
                            }

                        }
                        storeKeeperDetail {
                            id
                            responsiblePerson{
                                phone
                                name
                            }
                        }
                        framework(scope: STORE){
                            dateRangeAll: dateRange(range:ALL){
                                dataset{
                                    assetAccountDeposit
                                    storeKeeperProfit

                                }

                            }
                            dateRange(range:TODAY){
                                dataset{
                                    assetAccountDeposit
                                    storeKeeperProfit

                                }

                            }
                        }
                    }
                }
            }
            pageInfo {
                hasNextPage
                endCursor
            }
        }
    }
`;

export const findExchangeImportQuery = `
    #graphql
    query findExchangeImportPageable($page: Int! $size:Int! $orgId:Int $filter: ExchangeImportFilterDto $sort: [String] $dir: String){
        result: findExchangeImportPageable(sort: $sort dir:$dir page:$page size: $size orgId: $orgId filter: $filter){
            content {
                id {
                    store {
                        id
                        externalStoreId
                        name
                    }
                    date
                }
                amountVisibleStorekeeperFee
            }
            totalPages
        }
    }
`

/**
 * {
 * "query": "query findOrgDetailPageable(
 *   $page:Int!
 *   $size: Int!
 *   $sort: [String]
 *   $dir:String
 *   $fuzzySearch:String
 *   $ids: [Int]
 *   $routeTypes: [RouteType]!
 *   $externalStoreId: String
 *   $platformType:StorePlatformType
 * ) {
 *   result: findOrgDetailPageable(
 *     page: $page
 *     size: $size
 *     sort:$sort
 *     dir:$dir
 *     routeTypes:$routeTypes
 *     filter: {
 *       ids: $ids
 *    fuzzySearch: $fuzzySearch
 *       suspend: false
 *       orgType: STORE
 *       tagIds:[14]
 *       externalStoreId: $externalStoreId
 *       platformType: $platformType
 *     }
 *   ) {
 *     content{
 *         ... on OrgStoreDetailDTO {
 *           id
 *           name
 *           createdDate
 *           externalStoreId
 *           platformType{
 *             name
 *             description
 *           }
 *             orgConfig{
 *                 contract {
 *                         rentShare
 *                     }
 *                     operatingGoal:setting(key: \"OperatingSettingDto\") {
 *                         object
 *                     }
 *             }
 *           storeKeeperDetail {
 *             id
 *             responsiblePerson{
 *               phone
 *               name
 *             }
 *           }
 *           framework(scope: STORE){
 *                        dateRangeAll: dateRange(range:ALL){
 *                             dataset{
 *                                 assetAccountDeposit
 *                                 storeKeeperProfit
 *
 *                             }
 *
 *                         }
 *              dateRange(range:TODAY){
 *                             dataset{
 *                                 assetAccountDeposit
 *                                 storeKeeperProfit
 *
 *                             }
 *
 *                         }
 *                     }
 *         }
 *       }
 * totalPages
 *     number
 *     size
 * totalElements
 *   }
 * }",
 * "variables":{
 *   "page":   {{pagination1.current - 1}},
 *   "size": {{pagination1.pageSize}},
 *   "sort": ["id"],
 *   "dir": "DESC",
 *   "routeTypes": [
 *       "PARENT",
 *       "AGENT_CHANNEL_ALLY",
 *       "AGENT_PROVIDER_ALLY",
 *       "AGENT_INVESTOR_ALLY",
 *       "AGENT_PARTNER_ALLY"
 *     ],
 *     ids:  {{ params.storeId ? [params.storeId] : null }},
 *      externalStoreId: {{ params.externalStoreId ? params.externalStoreId : null }},
 * platformType:{{ params.platformType ? params.platformType : null }},
 * fuzzySearch:{{ params.storeName ? params.storeName : null }}
 *
 * }
 * }
 */
