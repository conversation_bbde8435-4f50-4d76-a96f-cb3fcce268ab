import { getRequest, graphqlRequest, postRequest } from '@/utils/requestUtil'
import upload from '@/utils/upload'
import { findImportTasksQuery } from '@/subpkgExternal/api/import.query'
import dayjs from '@/uni_modules/uv-ui-tools/libs/util/dayjs'

export const importFileToOssApi = file => postRequest('/import/oss', file)

export const importStoreOrderApi = (files, platform) => postRequest(`/import/v2/${platform}`, files)

export const importBillApi = data => postRequest('/import/async', data, true)
export const importStoreTemplateApi = data => postRequest('/import/async/template/store', data, true)
export const importOrderTemplateApi = data => postRequest('/import/async/template/order', data, true)

// 数据类型:[{name:string,description:string}]
export const getPlatformListApi = () => getRequest(`/import/platformType`)

export const simpleImportStoreApi = (filePath, platform) => {
  console.log(filePath, platform)
  return upload({
    url: `/import/store/${platform}`,
    filePath
  })
}

export const simpleImportOrderApi = (filePath, platform) => {
  return upload({
    url: `/import/bill/${platform}`,
    filePath
  })
}

export const findImportRecordApi = variables => graphqlRequest(findImportTasksQuery, variables)

// 按时间降序
export const importRecordMapper = list => list.map(item => ({
  ...item,
  statusStyle: statusStyle(item.taskStatus),
  createdTime: dayjs(item.createdTime).format('YYYY-MM-DD HH:mm:ss')
})).sort((a, b) => dayjs(b.createdTime).unix() - dayjs(a.createdTime).unix())

const statusStyle = (status) => {
  const map = {
    QUEUED: { text: '排队中', color: '#8D42FF' },
    PROCESSING: { text: '导入中', color: '#22AAE4' },
    COMPLETED: { text: '导入成功', color: '#43B731' },
    FAILED: { text: '导入失败', color: '#FF3B30' }
  }
  return map[status]
}
