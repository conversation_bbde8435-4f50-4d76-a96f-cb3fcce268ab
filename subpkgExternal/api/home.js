import { graphqlRequest } from '@/utils/requestUtil'
import { findExchangeImportQuery, findStoreListQuery } from '@/subpkgExternal/api/home.query'
import dayjs from '@/uni_modules/uv-ui-tools/libs/util/dayjs'

export const findStoreListReq = (variables) => graphqlRequest(findStoreListQuery, variables)

export const findExchangeImportReq = (variables) => graphqlRequest(findExchangeImportQuery, variables)

export const exchangeImportMapper = list => list.map(item => ({
  storeId: item.id.store.id,
  externalStoreId: item.id.store.externalStoreId,
  storeName: item.id.store.name,
  date: dayjs(item.id.date).format('YYYY/MM/DD'),
  storeKeeperFee: centToYuan(item.amountVisibleStorekeeperFee)
}))

export const storeMapper = (list) => list.map(item => ({
  storeId: item.id,
  externalStoreId: item.externalStoreId,
  platform: item.platformType?.description,
  storeName: item.name,
  phone: item.storeKeeperDetail.responsiblePerson.phone,
  responsiblePersonName: item.storeKeeperDetail.responsiblePerson.name,
  rentShare: item.orgConfig?.contract?.rentShare,
  operateGoal: item.orgConfig?.operatingGoal?.object?.goal || 0,
  unitPrice: centToYuan(item.orgConfig?.billConfigPowerbank?.object.unitPrice),
  createdDate: item.createdDate,

  allyTotalProfit: centToYuan(item.framework?.dateRangeAll?.dataset?.assetAccountDeposit),
  allyTodayProfit: centToYuan(item.framework?.dateRange?.dataset?.assetAccountDeposit),
  storeTotalProfit: centToYuan(item.framework?.dateRangeAll?.dataset?.storeKeeperProfit),
  storeTodayProfit: centToYuan(item.framework?.dateRange?.dataset?.storeKeeperProfit),
}))

const centToYuan = (cent) => cent ? (cent / 100).toFixed(2) : '-'
