<template>
  <view class="container">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">账单导入</text>
      <text class="page-subtitle">选择导入方式并上传文件</text>
    </view>

    <!-- 导入方式切换 -->
    <view class="import-tabs">
      <view
          class="tab-item"
          :class="{ 'tab-item--active': importMode === 'platform' }"
          @click="switchImportMode('platform')"
      >
        <view class="tab-icon">🏪</view>
        <view class="tab-content">
          <text class="tab-title">战报导入</text>
          <text class="tab-desc">导入第三方平台战报</text>
        </view>
      </view>
      <view
          class="tab-item"
          :class="{ 'tab-item--active': importMode === 'template' }"
          @click="switchImportMode('template')"
      >
        <view class="tab-icon">📋</view>
        <view class="tab-content">
          <text class="tab-title">模板文件导入</text>
          <text class="tab-desc">导入平台标准模板文件</text>
        </view>
      </view>
    </view>

    <!-- 平台导入模式 -->
    <template v-if="importMode === 'platform'">
      <!-- 平台选择区域 -->
      <uni-card class="platform-card" title="选择平台" :is-shadow="false" :border="true">
        <view class="platform-grid">
          <view
              v-for="platform in platformList"
              :key="platform.name"
              class="platform-item"
              :class="{ 'platform-item--active': selectedPlatform === platform.name }"
              @click="selectPlatform(platform.name)"
          >
            <view class="platform-content">
              <view class="platform-desc">{{ platform.description || platform.name }}</view>
              <view class="platform-check" v-if="selectedPlatform === platform.name">
                <text class="check-icon">✓</text>
              </view>
            </view>
          </view>
        </view>
      </uni-card>

      <!-- 文件上传区域 -->
      <uni-card class="upload-card" title="文件上传" :is-shadow="false" :border="true">
        <!-- 战报文件 -->
        <view class="file-item" @click="checkFiles(0)">
          <view class="file-info">
            <text class="file-label">请上传战报文件</text>
            <text class="file-status" v-if="!filesPath[0]">仅支持 .xlsx/.csv 文件</text>
            <text class="file-status success" v-else>✅ 已上传</text>
          </view>
          <view class="file-action">
            <text class="clear-btn" v-if="filesPath[0]" @click.stop="clearFile(0)">重传</text>
            <text class="iconfont icon-right" v-else></text>
          </view>
        </view>

        <template v-if="storeConfigFileUrl">
          <view class="config-notice" @click="downloadConfig">
            <view class="notice-text">⚠️ 检测到门店信息缺失，补充文件后重新上传</view>
            <view
                style="width: 150rpx;text-align: center;font-size: 24rpx;color: #007aff;padding: 8rpx 16rpx;border: 1px solid #007aff;border-radius: 8rpx;background-color: transparent;">
              打开补充文件
            </view>
          </view>
          <!-- 配置文件 -->
          <view class="file-item" @click="selectConfigFile">
            <view class="file-info">
              <text class="file-label">请上传补充文件</text>
              <text class="file-status" v-if="filesPath.length < 2">仅支持 .xlsx/.csv 文件</text>
              <text class="file-status success" v-else>✅ 已上传</text>
            </view>
            <view class="file-action">
              <text class="clear-btn" v-if="filesPath.length > 1" @click.stop="clearFile(1)">重传</text>
              <text class="iconfont icon-right" v-else></text>
            </view>
          </view>
        </template>
      </uni-card>

      <!-- 操作按钮 -->
      <view class="action-section">
        <view
            class="import-btn"
            :class="{ 'import-btn--disabled': !canImport }"
            @click="handleImport"
        >
          <text class="btn-text">开始导入</text>
        </view>
      </view>
    </template>

    <!-- 模板导入模式 -->
    <template v-if="importMode === 'template'">
      <!-- 平台选择区域（模板导入） -->
      <uni-card class="platform-card" title="选择平台" :is-shadow="false" :border="true">
        <view class="platform-grid">
          <view
              v-for="platform in platformList"
              :key="platform.name"
              class="platform-item"
              :class="{ 'platform-item--active': selectedTemplatePlatform === platform.name }"
              @click="selectTemplatePlatform(platform.name)"
          >
            <view class="platform-content">
              <view class="platform-desc">{{ platform.description || platform.name }}</view>
              <view class="platform-check" v-if="selectedTemplatePlatform === platform.name">
                <text class="check-icon">✓</text>
              </view>
            </view>
          </view>
        </view>
      </uni-card>
      <!-- 模板类型选择 -->
      <uni-card class="template-card" :is-shadow="false" :border="true">
        <view class="template-grid">
          <view
              class="template-item"
              @click="uploadTemplateFile('store')"
          >
            <view>
              <view class="flex-row">
                <view class="template-content">
                  <text class="template-title">请上传门店文件</text>
                  <text class="file-status">仅支持 .xlsx/.csv文件</text>
                </view>
              </view>
            </view>
            <view class="file-action">

              <text class="iconfont icon-right"></text>
            </view>
          </view>
          <view class="" @click.stop="downloadTemplate('store')">
            <text style="color: blue">下载模板</text>
          </view>
        </view>
      </uni-card>

      <uni-card class="template-card" :is-shadow="false" :border="true">
        <view class="template-grid">
          <view
              class="template-item"
              @click="uploadTemplateFile('exchange')"
          >
            <view>
              <view class="flex-row">
                <view class="template-content">
                  <text class="template-title">请上传账单文件</text>
                  <text class="file-status">仅支持 .xlsx/.csv文件</text>
                </view>
              </view>
            </view>

            <view class="file-action">
              <text class="iconfont icon-right"></text>
            </view>
          </view>
          <view class="" @click.stop="downloadTemplate('exchange')">
            <text style="color: blue">下载模板</text>
          </view>
        </view>
      </uni-card>
    </template>

    <!-- 错误信息弹窗 -->
    <uni-popup ref="errorsPopup">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">导入错误信息</text>
          <view class="popup-close" @click="closeErrorsPopup">
            <text class="close-text">关闭</text>
          </view>
        </view>

        <view class="errors-content">
          <view class="errors-summary">
            <view class="summary-icon">⚠️</view>
            <view class="summary-info">
              <text class="summary-title">发现 {{ importErrors.length }} 个信息待完善</text>
              <text class="summary-desc">1.请补充以下问题后重新导入</text>
              <text class="summary-desc" v-if="importMode === 'platform'">2.请确认导入的文件平台是{{ selectedPlatformInfo.description }}</text>
            </view>
          </view>

          <view class="errors-list">
            <view
                v-for="(error, index) in importErrors"
                :key="index"
                class="error-item"
            >
              <view class="error-row">
                <text class="row-label">第{{ error.rowNum }}行</text>
              </view>
              <view class="error-message">
                <text class="message-text">{{ error.errorMsg }}</text>
              </view>
            </view>
          </view>

          <view class="errors-actions">
            <view class="action-btn primary" @click="closeErrorsPopup">
              <text class="btn-text">我知道了</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 成功结果弹窗 -->
    <uni-popup ref="successPopup" border-radius="20rpx 20rpx 0 0">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">导入成功</text>
          <view class="popup-close" @click="closeSuccessPopup">
            <text class="close-text">关闭</text>
          </view>
        </view>

        <view class="success-content">
          <view class="success-summary">
            <view class="summary-icon">🎉</view>
            <view class="summary-info">
              <text class="summary-title">恭喜！导入完成</text>
              <text class="summary-desc">数据已成功导入到系统中</text>
            </view>
          </view>

          <view class="success-stats">
            <view class="stat-item">
              <view class="stat-number">{{ (importResult && importResult.successStoreCount) || 0 }}</view>
              <view class="stat-label">成功导入门店</view>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <view class="stat-number">{{ (importResult && importResult.successExchangeCount) || 0 }}</view>
              <view class="stat-label">成功导入账单</view>
            </view>
          </view>

          <view class="success-actions">
            <view class="action-btn primary" @click="closeSuccessPopup">
              <text class="btn-text">完成</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import {
  getPlatformListApi,
  importStoreOrderApi,
  simpleImportStoreApi,
  simpleImportOrderApi
} from '@/subpkgExternal/api/import'
import upload from '@/utils/upload'
import { toast } from '@/utils/common'

export default {
  data () {
    return {
      // 导入模式：'platform' | 'template'
      importMode: 'platform',

      // 平台导入相关
      filesPath: [],
      storeConfigFileUrl: '',
      selectedPlatform: '', // 默认选择的平台
      importErrors: [], // 导入错误信息
      importResult: null, // 导入成功结果

      // 模板导入相关
      selectedTemplate: '', // 选择的模板类型：'store' | 'exchange'
      selectedTemplatePlatform: '', // 模板导入选择的平台
      templateFilePath: '', // 模板文件路径
      templateDownloadUrls: {
        store: 'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/thirdPartySystem/store_template.xlsx',
        exchange: 'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/thirdPartySystem/bill_template.xlsx'
      }
    }
  },
  computed: {
    platformList () {
      if (this.importMode === 'platform') {
        return [{ name: 'SD', description: '搜电' }, { name: 'GS', description: '怪兽' }]
      }
      return [
        { name: 'SD', description: '搜电' },
        { name: 'GS', description: '怪兽' },
        { name: 'JD', description: '街电' },
        { name: 'MT', description: '美团' },
        { name: 'LD', description: '来电' }]
    },
    // 是否可以导入（平台模式）
    canImport () {
      if (this.importMode !== 'platform') return false
      const hasReportFile = this.filesPath.length > 0
      const hasConfigFile = !this.storeConfigFileUrl || this.filesPath.length === 2
      const hasPlatform = !!this.selectedPlatform
      return hasReportFile && hasConfigFile && hasPlatform
    },

    // 是否可以导入（模板模式）
    canTemplateImport () {
      if (this.importMode !== 'template') return false
      return !!this.selectedTemplate && !!this.selectedTemplatePlatform && !!this.templateFilePath
    },

    selectedPlatformInfo () {
      return this.platformList.find(p => p.name === this.selectedPlatform) || {}
    },
  },
  methods: {
    async uploadToOss (tempPath) {
      console.log(tempPath)
      const result = await upload({
        url: '/import/oss',
        filePath: tempPath
      })
      return result.data
    },
    // 切换导入模式
    switchImportMode (mode) {
      this.importMode = mode
      // 切换模式时清空相关数据
      if (mode === 'platform') {
        this.selectedTemplate = ''
        this.selectedTemplatePlatform = ''
        this.templateFilePath = ''
        this.selectPlatform(this.platformList[0].name)
      } else {
        this.selectedPlatform = ''
        this.filesPath = []
        this.storeConfigFileUrl = ''
      }
    },

    // 选择模板类型
    selectTemplate (templateType) {
      this.selectedTemplate = templateType
    },

    // 选择模板平台
    selectTemplatePlatform (platform) {
      this.selectedTemplatePlatform = platform
    },

    // 下载模板
    downloadTemplate (platform) {
      const url = this.templateDownloadUrls[platform]
      uni.downloadFile({
        url,
        success: res => {
          uni.openDocument({
            showMenu: true,
            filePath: res.tempFilePath,
          })
        }
      })
    },

    // 上传模板文件
    uploadTemplateFile (templateType) {
      if(!this.selectedTemplatePlatform) {
        return toast('请选择平台')
      }
      uni.chooseMessageFile({
        count: 1,
        type: 'file',
        extension: ['xlsx'],
        success: res => {
          console.log('选择模板文件结果:', res)
          if (res.tempFiles && res.tempFiles.length > 0) {
            const tempFile = res.tempFiles[0]
            this.templateFilePath = tempFile.path
            this.selectTemplate(templateType)
            this.handleTemplateImport()
          }
        },
        fail: (error) => {
          console.error('选择模板文件失败:', error)
        }
      })
    },
    // 清除模板文件
    clearTemplateFile () {
      this.templateFilePath = ''
    },
    // 模板导入处理
    async handleTemplateImport () {
      if (!this.canTemplateImport) {
        this.templateFilePath = ''
        toast('请先选择平台')
        return
      }

      try {
        this.$modal.loading('导入中...')
        let result
        if (this.selectedTemplate === 'store') {
          // 调用门店导入接口
          result = await simpleImportStoreApi(this.templateFilePath, this.selectedTemplatePlatform)
        } else if (this.selectedTemplate === 'exchange') {
          // 调用账单导入接口
          result = await simpleImportOrderApi(this.templateFilePath, this.selectedTemplatePlatform)
        }

        uni.hideLoading()

        if (result.data.errors && result.data.errors.length > 0) {
          this.importErrors = result.data.errors
          this.showErrorsPopup()
        } else {
          // 导入成功，显示成功结果
          this.importResult = {
            successStoreCount: result.data.successStoreCount || 0,
            successExchangeCount: result.data.successExchangeCount || 0
          }
          this.showSuccessPopup()
        }


        // 清空状态
        this.selectedTemplate = ''
        this.selectedTemplatePlatform = ''
        this.templateFilePath = ''

      } catch (error) {
        uni.hideLoading()
        console.error('模板导入失败:', error)
        toast('导入失败，请重试')
      }
    },

    // 选择平台
    selectPlatform (platformName) {
      this.selectedPlatform = platformName
      this.clearFile(0)
      this.clearFile(1)
    },

    // 下载配置模板
    downloadConfig () {
      if (!this.storeConfigFileUrl) {
        return toast('配置文件链接不存在')
      }

      uni.downloadFile({
        url: this.storeConfigFileUrl,
        success: res => {
          uni.openDocument({
            showMenu: true,
            filePath: res.tempFilePath,
          })
        }
      })
    },

    // 选择配置文件
    selectConfigFile () {
      this.checkFiles(1)
    },

    // 显示错误信息弹窗
    showErrorsPopup () {
      this.$refs.errorsPopup.open()
    },

    // 关闭错误信息弹窗
    closeErrorsPopup () {
      this.$refs.errorsPopup.close()
    },

    // 显示成功结果弹窗
    showSuccessPopup () {
      this.$refs.successPopup.open()
    },

    // 关闭成功结果弹窗
    closeSuccessPopup () {
      this.$refs.successPopup.close()
    },

    // 处理导入按钮点击
    handleImport () {
      if (!this.canImport) {
        uni.showToast({
          title: '请完善必要信息',
          icon: 'none'
        })
        return
      }
      this.uploadFile()
    },

    // 上传文件
    async uploadFile () {
      if (!this.canImport) {
        return toast('请完善必要信息')
      }

      uni.showLoading({
        title: '导入中...'
      })

      const result = await importStoreOrderApi(this.filesPath, this.selectedPlatform)
      uni.hideLoading()

      // 情况1: 传了两个文件 - 只看errors，不校验configFileUrl
      if (this.filesPath.length === 2) {
        if (result.data.errors && result.data.errors.length > 0) {
          this.importErrors = result.data.errors
          this.showErrorsPopup()
        } else {
          // 导入成功，显示成功结果
          this.importResult = {
            successStoreCount: result.data.successStoreCount || 0,
            successExchangeCount: result.data.successExchangeCount || 0
          }
          this.showSuccessPopup()
          // 清空文件列表
          this.filesPath = []
          this.storeConfigFileUrl = ''
          this.importErrors = []
        }
        return
      }

      // 情况2: 传了一个文件
      if (this.filesPath.length === 1) {
        // 情况2.1: 有configFileUrl - 提示上传模板
        if (result.data.configFileUrl) {
          this.storeConfigFileUrl = result.data.configFileUrl
          toast('需要配置文件')
        }
        // 情况2.2: 没有configFileUrl - 看errors
        else {
          if (result.data.errors && result.data.errors.length > 0) {
            this.importErrors = result.data.errors
            this.showErrorsPopup()
          } else {
            // 导入成功，显示成功结果
            this.importResult = {
              successStoreCount: result.data.successStoreCount || 0,
              successExchangeCount: result.data.successExchangeCount || 0
            }
            this.showSuccessPopup()
            // 清空文件列表
            this.filesPath = []
            this.storeConfigFileUrl = ''
            this.importErrors = []
          }
        }
      }
      uni.hideLoading()

    },

    // 选择文件
    checkFiles (index = 0) {
      uni.chooseMessageFile({
        count: 1,
        type: 'file',
        extension: ['xlsx', 'csv'],
        success: async res => {
          console.log('选择文件结果:', res)
          if (res.tempFiles && res.tempFiles.length > 0) {
            const tempFile = res.tempFiles[0]
            const tempPath = tempFile.path

            // 显示上传进度
            uni.showLoading({
              title: '上传文件中...'
            })

            try {
              // 上传到OSS获取真实链接
              const ossUrl = await this.uploadToOss(tempPath)

              console.log('文件链接:', ossUrl)

              // 将链接存入filesPath
              this.setFileUrl(index, ossUrl)

              uni.hideLoading()
              uni.showToast({
                title: '文件上传成功',
                icon: 'none',
                duration: 1500
              })

            } catch (error) {
              uni.hideLoading()
              console.error('文件上传失败:', error)
            }
          }
        },
        fail: (error) => {
          console.error('选择文件失败:', error)
        }
      })
    },

    // 设置文件URL
    setFileUrl (index, url) {
      if (index === 0) {
        // 选择战报文件，替换第一个位置
        this.filesPath.splice(0, 1, url)
        // 如果重新选择战报文件，清空配置文件URL
        this.storeConfigFileUrl = ''
      } else if (index === 1) {
        // 选择配置文件，添加到第二个位置
        if (this.filesPath.length === 1) {
          this.filesPath.push(url)
        } else {
          this.filesPath.splice(1, 1, url)
        }
      }
    },

    // 清除文件重新上传
    clearFile (index) {
      this.filesPath.splice(index, 1)
      // 如果清除的是战报文件，清空配置文件URL
      if (index === 0) {
        this.storeConfigFileUrl = ''
      }

    }
  },
  onLoad () {
    this.selectPlatform(this.platformList[0].name)
  }
}
</script>

<style lang="scss" sccoped>
.container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 20rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 32rpx;
  padding: 40rpx 0 20rpx 0;

  .page-title {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 16rpx;
  }

  .page-subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

// 导入方式切换
.import-tabs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
  padding: 0 8rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background-color: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
  }

  &--active {
    border-color: #007aff;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);

    .tab-title {
      color: #007aff;
      font-weight: 600;
    }

    .tab-icon {
      transform: scale(1.1);
    }
  }
}

.tab-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  transition: transform 0.3s ease;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.tab-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
}


// 平台选择卡片
.platform-card {
  margin-bottom: 32rpx;
}

.platform-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 8rpx 0;
}

.platform-item {
  flex: 1;
  min-width: 0;
  padding: 20rpx 16rpx;
  background-color: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;

  &:active {
    transform: scale(0.98);
  }

  &--active {
    border-color: #007aff;
    background-color: #f0f8ff;

    .platform-desc {
      color: #007aff;
      font-weight: 600;
    }
  }
}

.platform-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.platform-desc {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  transition: all 0.3s ease;
  text-align: center;
  word-break: break-all;
}

.platform-check {
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007aff;
  border-radius: 50%;

  .check-icon {
    font-size: 14rpx;
    color: #fff;
    font-weight: bold;
  }
}

// 模板选择样式
.template-card, .download-card, .template-upload-card {
  margin-bottom: 32rpx;
}

.template-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 8rpx 0;
}

.template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
  }

  &--active {
    border-color: #007aff;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);

    .template-title {
      color: #007aff;
      font-weight: 600;
    }
  }
}

.template-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.template-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.template-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
}

.template-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.template-check {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007aff;
  border-radius: 50%;

  .check-icon {
    font-size: 16rpx;
    color: #fff;
    font-weight: bold;
  }
}

// 下载区域样式
.download-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    background-color: #f8f9fa;
  }
}

.download-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.download-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #007aff;
}

.download-content {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.download-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.download-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.download-action {
  margin-left: 16rpx;
}

.download-btn {
  font-size: 24rpx;
  color: #007aff;
  padding: 8rpx 16rpx;
  border: 1px solid #007aff;
  border-radius: 8rpx;
  background-color: transparent;
}

// 上传卡片
.upload-card {
  margin-bottom: 32rpx;
}

// 文件项样式
.file-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    transform: scale(0.98);
    background-color: #f8f9fa;
  }
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.file-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.file-status {
  font-size: 24rpx;
  color: #999;

  &.success {
    color: #52c41a;
  }
}

.file-action {
  display: flex;
  align-items: center;

  .clear-btn {
    font-size: 24rpx;
    color: #007aff;
    padding: 8rpx 16rpx;
    border: 1px solid #007aff;
    border-radius: 8rpx;
    background-color: transparent;
  }

  .icon-right {
    font-size: 28rpx;
    color: #999;
  }
}

.upload-section {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.upload-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.upload-step {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .step-number {
    width: 48rpx;
    height: 48rpx;
    background-color: #007aff;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 600;
  }

  .step-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.upload-status {
  .status-success {
    font-size: 24rpx;
    color: #52c41a;
    background-color: #f6ffed;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
  }
}

.upload-area {
  border: 2rpx dashed #d9d9d9;
  border-radius: 16rpx;
  padding: 40rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;

  .upload-icon {
    width: 80rpx;
    height: 80rpx;
    background-color: #e6f7ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .upload-icon-text {
      font-size: 40rpx;
    }
  }

  .upload-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .upload-hint {
    font-size: 24rpx;
    color: #999;
  }
}

.upload-success {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #f6ffed;
  border: 2rpx solid #b7eb8f;
  border-radius: 16rpx;

  .success-icon {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .success-icon-text {
      font-size: 32rpx;
    }
  }

  .success-text {
    flex: 1;
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #52c41a;
    font-weight: 500;
  }

  .clear-btn {
    padding: 12rpx 20rpx;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 8rpx;

    .clear-text {
      font-size: 24rpx;
      color: #666;
    }
  }
}


// 配置模板卡片
.config-card {
  margin-bottom: 32rpx;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.config-notice {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 12rpx;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  margin-bottom: 16rpx;

  &:active {
    transform: scale(0.97);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .notice-icon {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .notice-icon-text {
      font-size: 28rpx;
    }
  }

  .notice-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .notice-text {
    position: relative;
    flex: 1;
    font-size: 24rpx;
    color: #d46b08;
    line-height: 1.5;

  }

  .notice-text_download {
    color: #080808;
    font-size: 24rpx;
  }

}

.config-download {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background-color: #f8f9fa;
  }
}

.download-content {
  display: flex;
  gap: 16rpx;
  flex: 1;

  .download-icon {
    width: 56rpx;
    height: 56rpx;
    background-color: #e6f7ff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .download-icon-text {
      font-size: 28rpx;
    }
  }

  .download-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .download-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .download-url {
      font-size: 24rpx;
      color: #999;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background-color: #007aff;
  border-radius: 12rpx;

  .copy-icon-text {
    font-size: 24rpx;
  }

  .copy-text {
    font-size: 24rpx;
    color: #fff;
    font-weight: 500;
  }
}

// 操作按钮区域
.action-section {
  margin-top: 48rpx;
  padding: 0 32rpx 48rpx;

  // 添加一些视觉分隔
  &::before {
    content: '';
    display: block;
    width: 100rpx;
    height: 4rpx;
    background: linear-gradient(90deg, #007aff, #0056cc);
    border-radius: 2rpx;
    margin: 0 auto 32rpx;
  }
}

.import-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 122, 255, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;

  // 添加光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:active {
    transform: scale(0.96);
    box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);

    &::before {
      left: 100%;
    }
  }

  &--disabled {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    cursor: not-allowed;

    &::before {
      display: none;
    }

    &:active {
      transform: none;
    }

    .btn-text {
      color: #bbb;
    }
  }

  .btn-text {
    font-size: 34rpx;
    color: #fff;
    font-weight: 600;
    letter-spacing: 2rpx;
    text-shadow: 0 1px 2rpx rgba(0, 0, 0, 0.1);
  }
}

// 通用样式
.flex-row {
  display: flex;
  align-items: center;
}

.flex-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 弹窗样式
.popup-content {
  background-color: #fff;
  max-height: 80vh;
  overflow: hidden;
  border-radius: 20rpx;
}


.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.popup-close {
  .close-text {
    font-size: 28rpx;
    color: #666;
  }
}


// 配置操作弹窗
.config-actions {
  padding: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 16rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background-color: #f0f0f0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e6f7ff;
  border-radius: 12rpx;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.action-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.action-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.action-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

// 错误信息弹窗样式
.errors-content {
  padding: 32rpx;
  max-height: 70vh;
  overflow: hidden;
}

.errors-summary {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 12rpx;
  margin-bottom: 32rpx;
}

.summary-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}

.summary-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.summary-title {
  font-size: 28rpx;
  color: #d46b08;
  font-weight: 600;
}

.summary-desc {
  font-size: 24rpx;
  color: #d46b08;
  line-height: 1.4;
}

.errors-list {
  max-height: 40vh;
  overflow-y: auto;
  margin-bottom: 32rpx;
}

.error-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.error-row {
  min-width: 120rpx;
  margin-right: 16rpx;
}

.row-label {
  font-size: 24rpx;
  color: #cf1322;
  font-weight: 600;
  background-color: #fff1f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border: 1px solid #ffccc7;
}

.error-message {
  flex: 1;
}

.message-text {
  font-size: 26rpx;
  color: #a8071a;
  line-height: 1.5;
}

.errors-actions {
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;

  &.primary {
    background-color: #007aff;

    .btn-text {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  &:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

// 成功结果弹窗样式
.success-content {
  padding: 32rpx;
}

.success-summary {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.summary-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.summary-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.summary-title {
  font-size: 32rpx;
  color: #52c41a;
  font-weight: 600;
}

.summary-desc {
  font-size: 26rpx;
  color: #389e0d;
  line-height: 1.4;
}

.success-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 40rpx 32rpx;
  background-color: #fafafa;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.stat-divider {
  width: 2rpx;
  height: 80rpx;
  background-color: #e8e8e8;
}

.success-actions {
  display: flex;
  justify-content: center;
  padding-top: 16rpx;
}

// 图标字体样式
.iconfont {
  font-family: 'iconfont';
}
</style>
