<script>
export default {
  name: 'ImportRecord',
  computed:{
    importTaskRecord(){
      return this.$store.state.external.importTaskRecord
    }
  },

}
</script>

<template>
  <view class="import-task-record">
    <view class="title">导入记录</view>

    <view class="record-item" v-for="item in importTaskRecord" :key="item.taskId">
      <view class="flex-row pb-16" style="justify-content: space-between">
        <view class="time">{{ item.createdTime }}</view>
        <view class="status" :style="{color: item.statusStyle.color,borderColor: item.statusStyle.color}">
          {{ item.statusStyle.text }}
        </view>
      </view>
      <view class="pb-16 desc" v-if="item.taskStatus === 'QUEUE'">当前导入人数较多，还需等待 {{item.queueNum}} 个任务</view>
      <view class="pb-16 desc" v-if="item.taskStatus === 'PROCESSING'">正在导入中，请稍候...</view>
      <view class="pb-16 desc" v-if="item.taskStatus === 'COMPLETED'">新增门店数：{{item.successStoreCount}} 个；导入账单数：{{item.successExchangeCount}} 条</view>
      <view class="pb-16 desc desc_failed" v-if="item.taskStatus === 'FAILED'">失败原因：系统异常</view>
      <view class="file-info">
        <view class="iconfont-record" style="margin-right: 8rpx"></view>
        <view style="max-width: 538rpx;" class="text-ellipsis">{{ item.fileName }}</view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.import-task-record {
  padding: 32rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .pb-16{
    margin-bottom: 16rpx;
  }

  .title {
    font-weight: bold;
    font-size: 28rpx;
    color: #2E2C2B;
    margin-bottom: 8rpx;
  }

  .record-item {
    padding: 16rpx 0;
    border-bottom: 1rpx solid #F0F1F4;
    .time {
      font-weight: 300;
      font-size: 24rpx;
      color: #2E2C2B;
    }

    .status {
      padding: 4rpx 12rpx;
      font-weight: 300;
      font-size: 24rpx;
      border: 1px solid;
      border-radius: 4rpx;
    }

    .desc {
      font-weight: 300;
      font-size: 24rpx;
      color: #2E2C2B;
      &_failed {
        color: #FF3B30;
      }
    }

    .file-info {
      font-weight: 300;
      font-size: 24rpx;
      color: #2E2C2B;
      display: flex;
    }
  }
}
</style>
