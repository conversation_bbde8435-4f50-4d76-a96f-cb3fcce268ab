<script>
import UploadComponent from '@/subpkgExternal/pages/v2/components/UploadComponent.vue'
import { importBillApi } from '@/subpkgExternal/api/import'
import { needFillStoreConfigColumns } from '@/subpkgExternal/pages/v2/components/needFillStoreConfigTableConfig'
import ErrorPopup from '@/subpkgExternal/pages/v2/components/ErrorPopup.vue'

export default {
  name: 'BillImport',
  components: { UploadComponent, ErrorPopup },
  data () {
    return {
      dataUrl: '',
      fileName: '',
      configUrl: null,
      downloadConfigUrl: null,
      errorStatus: null,
      fillTableData: [],
      clickRowInfo: {
        data: null,
        index: -1
      },
      tableKey: 1,
      loading: false,
    }
  },
  computed: {
    needFillStoreConfigColumns () {
      return needFillStoreConfigColumns
    },
    submitValidation () {
      if (this.dataUrl && this.errorStatus) return false
      if (this.errorStatus === 'NEED_UPLOAD_STORE_CONFIG' && !this.configUrl) return false
      if (['NEED_FILL_STORE_CONFIG', 'STORE_CONFIG_VALIDATION_ERROR'].includes(this.errorStatus) && !this.fillTableValidation) return false
      return !['VALIDATION_ERROR', 'READ_FILE_ERROR'.includes(this.errorStatus)]
    },
    fillTableValidation () {
      return this.fillTableData.every(item => Object.keys(item).every(key => item[key]))
    }
  },
  methods: {
    rowClick (data, index) {
      this.clickRowInfo = { data: JSON.parse(JSON.stringify(data)), index }
      this.$refs.editStorePopup.open()
      console.log(this.clickRowInfo)
    },
    editConfirm () {
      this.fillTableData[this.clickRowInfo.index] = JSON.parse(JSON.stringify(this.clickRowInfo.data))
      console.log(this.fillTableData)
      this.tableKey = Date.now()
      this.editClose()
    },
    editClose () {
      this.$refs.editStorePopup.close()
    },
    uploadBillSuccess (fileInfo) {
      console.log(fileInfo)
      this.dataUrl = fileInfo.url
      this.fileName = fileInfo.fileName
      this.errorStatus = null
      this.configUrl = null
      this.fillTableData = []
      this.submitHandler()
    },
    uploadConfigSuccess (fileInfo) {
      this.configUrl = fileInfo.url
      this.submitHandler()
    },
    async submitHandler () {
      if (this.loading) return
      try {
        this.loading = true
        await importBillApi({
          dataUrl: this.dataUrl,
          configUrl: this.configUrl,
          storeConfigTemplates: this.fillTableData,
          fileName: this.fileName
        })
        this.$refs.uploadBillRef?.clearFile()
        this.$refs.uploadConfigRef?.clearFile()
        this.dataUrl = null
        this.configUrl = null
        this.fillTableData = []
        this.errorStatus = null
        this.loading = false
        this.$store.dispatch('external/GetRecord')
        this.$modal.msg('操作成功')

      } catch (e) {
        this.loading = false
        this.$modal.msg(e.msg)
        e.data && this.errorHandle(e.msg, e.data)
      }
    },
    errorHandle (msg, error) {
      console.log('errorHandle', error)

      const status = error.status
      this.errorStatus = status
      const errorHandlerMap = {
        'NEED_UPLOAD_STORE_CONFIG': this.needUploadStoreConfigHandle,
        'NEED_FILL_STORE_CONFIG': this.needFillStoreConfigHandle,
        'STORE_CONFIG_VALIDATION_ERROR': this.storeConfigValidationErrorHandle,
        'VALIDATION_ERROR': this.validationErrorHandler,
        'READ_FILE_ERROR': this.readFileError
      }
      errorHandlerMap[status] && errorHandlerMap[status](msg, error)
    },
    needUploadStoreConfigHandle (msg, error) {
      this.downloadConfigUrl = error.configFileUrl
    },
    needFillStoreConfigHandle (msg, error) {
      const fields = ['externalStoreId', 'storeName', 'phone', 'responsiblePerson', 'share', 'operatingGoal']
      this.fillTableData = error.storeConfigTemplates.map((item, index) => {
        fields.forEach(field => {
          item['origin_' + field] = item[field] || ''
        })
        item['rowNumber'] = index + 1
        return item
      })
      console.log(this.fillTableData)
    },
    storeConfigValidationErrorHandle (msg, error) {
      this.$refs.errorPopupRef.open(error.errors)
      this.needFillStoreConfigHandle(error)
    },
    validationErrorHandler (msg, error) {
      this.$refs.errorPopupRef.open(error.errors)
    },
    readFileError () {

    },
    downloadConfig (url) {
      uni.downloadFile({
        url,
        success: res => {
          uni.openDocument({
            showMenu: true,
            filePath: res.tempFilePath,
          })
        }
      })
    }
  }
}
</script>

<template>
  <view class="bill-import">
    <view class="bill-import-container">
      <UploadComponent title="上传战报文件" @success="uploadBillSuccess" :style="{height: errorStatus?'auto':'100%'}"
                       ref="uploadBillRef"/>
      <template v-if="['NEED_FILL_STORE_CONFIG','STORE_CONFIG_VALIDATION_ERROR'].includes(errorStatus)">
        <view class="error_NEED_FILL_STORE_CONFIG">
          <view class="error-info">
            <view class="iconfont-alert"></view>
            <view class="error-text"> 检测到战报中新增{{ fillTableData.length }}家门店，请补充以下新增门店信息</view>
          </view>
          <view class="fill-table">
            <zb-table
                style="flex: 1; overflow: scroll;"
                :key="tableKey"
                ref="zbTable"
                :data="fillTableData"
                :columns="needFillStoreConfigColumns"
                show-header
                @rowClick="rowClick"
            ></zb-table>
          </view>
          <view class="submit-btn" :class="{'submit-btn_disable':submitValidation}" @click="submitHandler">上传</view>
        </view>
      </template>

      <template
          v-if="['NEED_UPLOAD_STORE_CONFIG'].includes(errorStatus) || ('VALIDATION_ERROR' === errorStatus && configUrl)">
        <view class="error_NEED_UPLOAD_STORE_CONFIG">
          <view class="error-info">
            <view class="iconfont-alert"></view>
            <view class="error-text"> 检测到战报中缺失分成、运营目标等信息，请完善补充文件后上传</view>
          </view>
          <view class="download_config_container">
            <view class="flex-row" style="margin-bottom: 32rpx" @click="downloadConfig(downloadConfigUrl)">
              <image style="width: 32rpx;height: 32rpx"
                     src="https://resource.wukongcd.com/prod/mp/external_store_download_config.png"/>
              <view class="download_text">下载门店补充文件</view>
            </view>
            <UploadComponent title="上传补充文件" @success="uploadConfigSuccess" style="margin-top: 32rpx"
                             ref="uploadConfigRef"/>
          </view>
        </view>
      </template>
    </view>

    <uni-popup ref="editStorePopup" type="center" :animation="false" :mask-click="false">
      <view class="edit-popup-box">
        <view class="edit-title">门店信息补充</view>
        <view class="store-info">
          <view class="store-name">{{ clickRowInfo.data.storeName }}</view>
          <view class="external-id">ID：{{ clickRowInfo.data.externalStoreId }}</view>
        </view>
        <view class="form-edit">
          <view class="form-item">
            <view class="label">手机号</view>
            <view>
              <input class="input" placeholder="请输入" v-model="clickRowInfo.data.phone"
                     placeholder-style="font-size: 28rpx;color: #B4B8C0;"/>
            </view>

          </view>
          <view class="form-item">
            <view class="label">姓名</view>
            <view>
              <input class="input" placeholder="请输入" v-model="clickRowInfo.data.responsiblePerson"
                     placeholder-style="font-size: 28rpx;color: #B4B8C0;"/>
            </view>
          </view>
          <view class="form-item">
            <view class="label">分成A</view>
            <view style="position: relative">
              <input class="input" placeholder="请输入" v-model="clickRowInfo.data.share"
                     placeholder-style="font-size: 28rpx;color: #B4B8C0;"/>
              <view
                  style="position: absolute;right: 32rpx;top: 50%;transform: translateY(-50%);font-size: 28rpx;color: #B4B8C0;">
                %
              </view>
            </view>
          </view>
          <view class="form-item">
            <view class="label">分成B</view>
            <view style="position: relative">
              <input class="input" placeholder="请输入" v-model="clickRowInfo.data.operatingGoal"
                     placeholder-style="font-size: 28rpx;color: #B4B8C0;"/>
              <view
                  style="position: absolute;right: 32rpx;top: 50%;transform: translateY(-50%);font-size: 28rpx;color: #B4B8C0;">
                %
              </view>
            </view>
          </view>
        </view>
        <!-- 按钮区域 -->
        <view class="btn-box">
          <view class="cancel-btn" @click="editClose">取消</view>
          <view class="save-btn" @click="editConfirm">确认</view>
        </view>
      </view>
    </uni-popup>

    <ErrorPopup ref="errorPopupRef"/>
  </view>
</template>

<style scoped lang="scss">
.bill-import {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .bill-import-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .error-info {
      padding: 16rpx 24rpx;
      background: #FDEFBD;
      border-radius: 16rpx;
      display: flex;
      font-weight: 400;
      font-size: 24rpx;
      color: #A37F00;

      .iconfont-alert {
        color: #A37F00;
      }

      .error-text {
        width: 542rpx;
        margin-left: 16rpx;
      }
    }

    .error_NEED_FILL_STORE_CONFIG {
      flex: 1;
      margin-top: 24rpx;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .fill-table {
        margin: 16rpx 0;
        max-height: 500rpx;
        display: flex;
        flex-direction: column;
        overflow: scroll;
      }

      .submit-btn {
        width: 638rpx;
        height: 88rpx;
        text-align: center;
        line-height: 88rpx;
        background: #3564FD;
        box-shadow: 0rpx 1px 4rpx 0rpx rgba(0, 0, 0, 0.05);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        border: 1px solid #EBE9E7;
        color: #FFFFFF;

        &_disable {
          background: #E2E2E2;
        }
      }
    }

    .error_NEED_UPLOAD_STORE_CONFIG {
      margin-top: 16rpx;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      border: 1px solid #F5F5F5;
      padding: 24rpx;

      .download_config_container {
        margin-top: 24rpx;

        .download_text {
          margin-left: 4rpx;
          font-weight: 400;
          font-size: 28rpx;
          color: #3564FD;
        }
      }
    }
  }

  .edit-popup-box {
    width: calc(564rpx - 80rpx);
    background: #FFFFFF;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 32rpx 40rpx;

    .edit-title {
      font-weight: 500;
      font-size: 32rpx;
      color: #2E2C2B;
      text-align: center;
    }

    .store-info {
      margin-top: 40rpx;
      padding: 16rpx 24rpx;
      background: #F0F1F4;
      border-radius: 16rpx 16rpx 16rpx 16rpx;

      .store-name {
        font-weight: 400;
        font-size: 28rpx;
        color: #2E2C2B;
      }

      .external-id {
        margin-top: 4rpx;
        font-weight: 300;
        font-size: 20rpx;
        color: #979494;
      }
    }

    .form-edit {
      .form-item {
        margin-top: 32rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .label {
          font-weight: 400;
          font-size: 28rpx;
          color: #7F8998;
        }

        .input {
          font-weight: 400;
          font-size: 28rpx;
          width: calc(376rpx - 96rpx);
          padding: 0 64rpx 0 32rpx;
          height: 80rpx;
          background: #FFFFFF;
          border-radius: 12rpx 12rpx 12rpx 12rpx;
          border: 1px solid #DCDCDC;
        }
      }
    }

    .btn-box {
      margin-top: 40rpx;
      display: flex;
      justify-content: space-between;

      .cancel-btn {
        width: 226rpx;
        height: 88rpx;
        line-height: 88rpx;
        text-align: center;
        font-weight: 400;
        font-size: 34rpx;
        color: #2E2C2B;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        border: 1px solid #C8C8C8;
      }

      .save-btn {
        width: 226rpx;
        height: 88rpx;
        line-height: 88rpx;
        text-align: center;
        font-weight: 400;
        font-size: 34rpx;
        color: #FFFFFF;
        background: #3564FD;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
      }
    }

  }
}
</style>
