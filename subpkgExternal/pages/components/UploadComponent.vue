<script>
import upload from '@/utils/upload'

export default {
  name: 'Upload',
  props: {
    title: String
  },
  data () {
    return {
      fileName: '',
      url: '',
    }
  },
  methods: {
    clearFile(){
      this.fileName = ''
      this.url = ''
    },
    async uploadToOss (tempPath) {
      const result = await upload({
        url: `/import/oss`,
        filePath: tempPath
      })
      return result.data
    },
    chooseFile () {
      uni.chooseMessageFile({
        count: 1,
        type: 'file',
        extension: ['xlsx', 'csv'],
        success: async res => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            const tempFile = res.tempFiles[0]
            const fileName = tempFile.name
            const tempPath = tempFile.path
            console.log(tempPath)
            // 显示上传进度
            this.$modal.loading('上传文件中...')

            try {
              // 上传到OSS获取真实链接
              const url = await this.uploadToOss(tempPath)
              // const url = 'https://oss-export-data.oss-cn-shenzhen.aliyuncs.com/test/03f2b454-2823-4491-8cec-5dd4fcf87e57-kcDw5Bpc3lO268535f2bef6e673842932f940b765527.xlsx'

              uni.hideLoading()
              this.fileName = fileName
              this.url = url
              this.$modal.showToast('文件上传成功')
              this.$emit('success', { url, fileName })
            } catch (error) {
              uni.hideLoading()
              console.error('文件上传失败:', error)
            }
          }
        },
        fail: (error) => {
          console.error('选择文件失败:', error)
        }
      })
    }
  }
}
</script>
<template>
  <view class="upload_component"
        :style="{width:url?'auto':'100%',height:url?'auto':'100%',minHeight:url?'auto':'400rpx'}">
    <view class="upload_info" v-if="url">
      <view class="flex-row">
        <image class="fileicon"
               src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/external_file_icon.png"/>
        <view class="filename">{{ fileName }}</view>
      </view>
      <view class="reupload" @click="chooseFile">重新上传</view>
    </view>
    <view class="upload_component_container" @click="chooseFile" v-else>
      <image class="image" src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/external_upload.png"/>
      <view class="title">{{ title }}</view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.upload_component {
  .upload_info {
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 1px solid #F5F5F5;
    display: flex;
    align-items: center;
    padding: 24rpx;
    justify-content: space-between;

    .fileicon {
      width: 48rpx;
      height: 48rpx;
    }

    .filename {
      margin-left: 24rpx;
      width: 340rpx;
      font-size: 28rpx;
      color: #2E2C2B;
      word-break: break-all;
    }

    .reupload {
      font-weight: 400;
      font-size: 28rpx;
      color: #3564FD;
    }
  }

  .upload_component_container {
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    min-height: 400rpx;
    background: #F0F1F4;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    border: 1px dashed #3564FD;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .image {
      width: 74rpx;
      height: 74rpx;
    }

    .title {
      margin-top: 32rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #3564FD;
    }
  }
}

</style>
