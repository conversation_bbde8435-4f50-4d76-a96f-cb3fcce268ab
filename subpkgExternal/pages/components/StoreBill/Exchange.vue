<script>
import { managerId } from '@/utils/role'
import dayjs from '@/uni_modules/uv-ui-tools/libs/util/dayjs'
import ZbTable from '@/uni_modules/zb-table/components/zb-table/zb-table.vue'
import MScroll from '@/components/m-scroll/m-scroll.vue'
import { columns } from './exchangeProTableConfig'
import { exchangeImportMapper, findExchangeImportReq } from '@/subpkgExternal/api/store_bill'

export default {
  name: 'Exchange',
  components: { MScroll, ZbTable },
  data () {
    return {
      exchangeList: [],
      loading: false,
      columns: columns,
      pageInfo: {
        page: 0,
        size: 20,
        totalPages: 0,
        hasNext: true
      },
      importDate: '',
    }
  },
  computed: {
    managerId,
    companyId(){
      return this.$store.state.user.allyId
    }
  },
  methods: {
    async getDataList (done) {
      if(this.loading){
        return
      }
      if(!done){
        this.pageInfo.hasNext = true
        this.pageInfo.page = 0
        this.exchangeList = []
        this.$modal.loading()
      }

      if (!this.pageInfo.hasNext) {
        return done && done('ok')
      }

      this.loading = true
      const requestParams = {
        page: this.pageInfo.page,
        size: this.pageInfo.size,
        orgId: this.companyId,
        sort: 'importTime',
        dir: 'DESC',
        filter: {
          importDate: this.importDate,
        }
      }

      const result = await findExchangeImportReq(requestParams)
      const responseData = result.data.result
      console.log(exchangeImportMapper(responseData.content))
      this.exchangeList.push(...exchangeImportMapper(responseData.content))

      // 更新分页信息
      this.pageInfo.totalPages = responseData.totalPages
      this.pageInfo.hasNext = this.pageInfo.page < responseData.totalPages

      if (this.pageInfo.hasNext) {
        this.pageInfo.page += 1
        done && done()
      } else {
        done && done('ok')
      }
      this.loading = false
      uni.hideLoading()
    },

    // 日期选择处理
    onDateChange (event) {
      this.importDate = event.detail.value
      this.resetAndSearch()
    },

    // 重置并搜索
    resetAndSearch () {
      this.exchangeList = []
      this.pageInfo.page = 0
      this.pageInfo.hasNext = true
      this.getDataList(null, true)
    }
  },
  created () {
    this.importDate = dayjs().format('YYYY-MM-DD')
    this.getDataList()
  },
}
</script>

<template>
  <view style="flex: 1;overflow:hidden;display: flex;flex-direction: column;">
    <!-- 筛选区域 -->
    <view class="filter-container">
      <!-- 日期选择器 -->
      <view class="filter-item">
        <picker
            mode="date"
            :value="importDate"
            @change="onDateChange"
            class="date-picker"
        >
          <view class="date-display">
            <text class="date-text">{{ importDate || '请选择日期' }}</text>
            <text class="date-icon">📅</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 表格区域 -->
    <view style="flex: 1;overflow:hidden;">
      <MScroll :list="exchangeList" scroll-x first-loading no-refresh :loading="loading">
        <template #default>
          <zb-table
              ref="zbTable"
              :data="exchangeList"
              :columns="columns"
              show-header
              :pullUpLoading="getDataList"
              is-show-load-more
          ></zb-table>
        </template>
      </MScroll>
    </view>
  </view>
</template>

<style scoped lang="scss">
.filter-container {
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  min-width: 140rpx;
}

// 日期选择器样式
.date-picker {
  flex: 1;
}

.date-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:active {
    border-color: #007aff;
    background-color: #fff;
  }
}

.date-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.date-icon {
  font-size: 24rpx;
  opacity: 0.6;
}

</style>
