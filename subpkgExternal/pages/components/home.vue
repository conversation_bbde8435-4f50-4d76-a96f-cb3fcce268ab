<template>
  <view class="external-store-list-page">
    <view class="navbar">
      <view :style="{height: statusHeight + 'px'}" style="width: 100%"></view>
      <view :style="{height: navHeight + 'px'}" class="flex-row" style="width: 100%;">
        <view
            style="z-index: 9;margin-left: 20px;width: 20px;height: 20px;background-size: 100% 100%;background-image: url(https://resource.wukongcd.com/prod/mp/arrow.png)"
            @click="back"/>
        <view class="nav-container">
          <view class="mode" @click="mode= 'STORE'" :class="{'mode_active':mode === 'STORE'}">门店管理</view>
          <view class="mode" @click="mode= 'EXCHANGE'" :class="{'mode_active':mode === 'EXCHANGE'}">账单管理</view>
        </view>
      </view>
    </view>
    <Store ref="storeRef" v-if="mode === 'STORE'" style="flex: 1;display: flex;flex-direction: column;overflow: hidden"/>
    <Exchange ref="exchangeRef" v-if="mode === 'EXCHANGE'" style="flex: 1;display: flex;flex-direction: column;overflow: hidden"/>

    <!-- 悬浮导入按钮 -->
    <view class="floating-import-btn" @click="goToImport">
      <view class="btn-content">
        <text class="btn-icon">📊</text>
        <text class="btn-text">导入账单</text>
      </view>
    </view>
  </view>
</template>

<script>
import ZbTable from '@/uni_modules/zb-table/components/zb-table/zb-table.vue'
import MScroll from '@/components/m-scroll/m-scroll.vue'
import Exchange from '@/subpkgExternal/pages/home/<USER>'
import Store from '@/subpkgExternal/pages/home/<USER>'

export default {
  components: { MScroll, ZbTable,Store,Exchange },
  data () {
    return {
      statusHeight: 0,
      navHeight: 0,
      mode: 'STORE'
    }
  },

  methods: {
    back(){
      this.$tab.navigateBack()
    },

    // 跳转到导入页面
    goToImport() {
      uni.navigateTo({
        url: '/subpkgExternal/pages/import/import'
      })
    }
  },
  onLoad () {
    const globalData = getApp().globalData
    this.statusHeight = globalData.statusBarHeight
    this.navHeight = globalData.navBarHeight
  },
  onShow() {
    this.$refs.storeRef?.getDataList()
    this.$refs.exchangeRef?.getDataList()
  },
}
</script>

<style lang="scss" scoped>
.external-store-list-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .navbar {
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    align-items: center;
    .nav-container{
      width: 100%;
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: center;
      transform: translateX(-20px);
      .mode{
        padding: 8rpx 0;
        color: #6F6A67;
        font-size: 28rpx;
        margin-right: 40rpx;
        &_active{
          font-size: 32rpx!important;
          font-weight: bold;
          color: #222222;
          position: relative;
          &:before{
            content: "";
            display: block;
            position: absolute;
            left: 50%;
            bottom: -6rpx;
            background: #222222;
            width: 30%;
            height: 4rpx;
            transform: translateX(-50%);
            border-radius: 6rpx;
          }
        }
      }
    }
  }
}

// 悬浮导入按钮
.floating-import-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  position: fixed;
  right: 40rpx;
  bottom: 120rpx;
  z-index: 999;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);
  }
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.btn-icon {
  font-size: 32rpx;
  line-height: 1;
}

.btn-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 600;
  letter-spacing: 1rpx;
  line-height: 1;
}
</style>
