<script>
import UploadComponent from '@/subpkgExternal/pages/v2/components/UploadComponent.vue'
import { importOrderTemplateApi, importStoreTemplateApi } from '@/subpkgExternal/api/import'
import ErrorPopup from '@/subpkgExternal/pages/v2/components/ErrorPopup.vue'

export default {
  name: 'TemplateImport',
  components: { UploadComponent,ErrorPopup },
  data () {
    return {
      loading: false
    }
  },
  methods: {
    downloadTemplate (url) {
      uni.downloadFile({
        url,
        success: res => {
          uni.openDocument({
            showMenu: true,
            filePath: res.tempFilePath,
          })
        }
      })
    },
    async uploadStoreSuccess (fileInfo) {
      try {
        if (this.loading) return
        this.loading = true
        await importStoreTemplateApi({
          dataUrl: fileInfo.url,
          fileName: fileInfo.fileName
        })
        this.loading = false
        this.$store.dispatch('external/GetRecord')
        this.$refs.uploadStoreRef.clearFile()
        this.$modal.msg('操作成功')
      } catch (e) {
        this.$modal.msg(e.msg)
        e.data?.errors && this.$refs.errorPopupRef.open(e.data.errors)
        this.loading = false
      }
    },
    async uploadExchangeSuccess (fileInfo) {
      try {
        if (this.loading) return
        this.loading = true
        await importOrderTemplateApi({
          dataUrl: fileInfo.url,
          fileName: fileInfo.fileName
        })
        this.loading = false
        this.$store.dispatch('external/GetRecord')
        this.$refs.uploadExchangeRef.clearFile()
        this.$modal.msg('操作成功')
      } catch (e) {
        this.$modal.msg(e.msg)
        e.data?.errors && this.$refs.errorPopupRef.open(e.data.errors)
        this.loading = false
      }

    }
  }
}
</script>

<template>
  <view class="template-import">
    <view class="store-import">
      <view class="download_config_container">
        <view class="flex-row" style="margin-bottom: 32rpx"
              @click="downloadTemplate('https://oss-export-data.oss-cn-shenzhen.aliyuncs.com/prod/thirdPartySystem/store_template.xlsx?key='+Date.now())">
          <image style="width: 32rpx;height: 32rpx"
                 src="https://resource.wukongcd.com/prod/mp/external_store_download_config.png"/>
          <view class="download_text">下载门店模板</view>
        </view>
        <UploadComponent title="上传门店" @success="uploadStoreSuccess" style="margin-top: 32rpx" ref="uploadStoreRef"/>
      </view>
    </view>
    <view class="order-import">
      <view class="download_config_container">
        <view class="flex-row" style="margin-bottom: 32rpx"
              @click="downloadTemplate('https://oss-export-data.oss-cn-shenzhen.aliyuncs.com/prod/thirdPartySystem/bill_template.xlsx?key='+Date.now())">
          <image style="width: 32rpx;height: 32rpx"
                 src="https://resource.wukongcd.com/prod/mp/external_store_download_config.png"/>
          <view class="download_text">下载账单模板</view>
        </view>
        <UploadComponent title="上传账单" @success="uploadExchangeSuccess" style="margin-top: 32rpx"
                         ref="uploadExchangeRef"/>
      </view>
    </view>

    <ErrorPopup ref="errorPopupRef"/>
  </view>
</template>

<style scoped lang="scss">
.template-import {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .store-import {
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 1px solid #F5F5F5;
    padding: 24rpx;
    margin-bottom: 24rpx;
  }

  .order-import {
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 1px solid #F5F5F5;
    padding: 24rpx;
  }

  .download_config_container {
    .download_text {
      margin-left: 4rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #3564FD;
    }
  }

}
</style>
