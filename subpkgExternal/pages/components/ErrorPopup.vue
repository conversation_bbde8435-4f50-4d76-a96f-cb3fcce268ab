<script>
export default {
  name: 'ErrorPopup',
  data(){
    return {
      errorList:[]
    }
  },
  methods:{
    open(errorList){
      this.errorList = errorList
      this.$refs.editStorePopup.open()
    },
    close(){
      this.$refs.editStorePopup.close()
    }
  }
}
</script>

<template>
  <uni-popup ref="editStorePopup" type="center" :animation="false" :mask-click="false">
    <view class="error-popup">
      <view class="error-popup-title">
       <view class="iconfont-alert"></view>
        <view class="text">存在{{ errorList.length }}个错误信息如下，请编辑后重新上传</view>
      </view>
      <view class="error-table">
        <view class="row">
          <view class="text label">行数</view>
          <view class="text detail">错误信息</view>
        </view>
        <view v-for="(error, index) in errorList" :key="index" class="row">
          <view class="text label">{{ error.rowNum }}</view>
          <view class="text detail">{{ error.errorMsg }}</view>
        </view>
      </view>
      <view class="error-popup-button"  @click="close">确定</view>
    </view>
  </uni-popup>
</template>

<style scoped lang="scss">
.error-popup {
  width: 622rpx;
  padding: 64rpx 0 0 0;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .error-popup-title {
    margin: 0 48rpx;
    width: 478rpx;
    display: flex;

    .iconfont-alert {
      font-size: 32rpx;
      color: #FF3B30;
    }

    .text {
      font-weight: 500;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.9);
      text-align: center;
      font-weight: bold;
    }
  }

  .error-table {
    margin: 16rpx 48rpx 0 48rpx;
    border: 1px solid #C8C8C8;
    max-height: 500rpx;
    overflow-y: scroll;

    .row {
      display: flex;
      border-bottom: 1px solid #C8C8C8;

      &:last-child {
        border-bottom: none;
      }

      .text {
        font-size: 24rpx;
        color: #2E2C2B;
      }

      .label {
        width: calc(140rpx - 16rpx * 2 - 1 * 2px);
        height: 66rpx;
        line-height: 66rpx;
        padding: 0 16rpx;
        border-right: 1px solid #C8C8C8;
      }

      .detail {
        width: calc(387rpx - 16rpx * 2);
        height: 66rpx;
        line-height: 66rpx;
        padding: 0 16rpx;
      }
    }
  }
  .error-popup-button{
    margin-top: 48rpx;
    border-top: 1px solid #E7E7E7;
    width: 100%;
    height: 112rpx;
    line-height: 112rpx;
    font-weight: 600;
    font-size: 32rpx;
    color: #0052D9;
    text-align: center;
  }
}
</style>
