<script>
import Exchange from '@/subpkgExternal/pages/components/Exchange.vue'
import Store from '@/subpkgExternal/pages/components/Store.vue'

export default {
  name: 'ImportData',
  components: { Store, Exchange },
  data () {
    return {
      statusHeight: 0,
      navHeight: 0,
      mode: 'STORE'
    }
  },
  methods:{
    back(){
      this.$tab.navigateBack()
    },
  },
  created () {
    const globalData = getApp().globalData
    this.statusHeight = globalData.statusBarHeight
    this.navHeight = globalData.navBarHeight
  }
}
</script>

<template>
  <view class="external-data-page">
    <view class="navbar">
      <view :style="{height: statusHeight + 'px'}" style="width: 100%"></view>
      <view :style="{height: navHeight + 'px'}" class="flex-row" style="width: 100%;">
        <view
            style="z-index: 9;margin-left: 20px;width: 20px;height: 20px;background-size: 100% 100%;background-image: url(https://resource.wukongcd.com/prod/mp/arrow.png)"
            @click="back"/>
        <view class="nav-container">
          <view class="mode" @click="mode= 'STORE'" :class="{'mode_active':mode === 'STORE'}">门店管理</view>
          <view class="mode" @click="mode= 'EXCHANGE'" :class="{'mode_active':mode === 'EXCHANGE'}">账单管理</view>
        </view>
      </view>
    </view>
    <Store ref="storeRef" v-if="mode === 'STORE'"
           style="flex: 1;display: flex;flex-direction: column;overflow: hidden"/>
    <Exchange ref="exchangeRef" v-if="mode === 'EXCHANGE'"
              style="flex: 1;display: flex;flex-direction: column;overflow: hidden"/>

  </view>
</template>

<style scoped lang="scss">
.external-data-page {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .navbar {
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    align-items: center;

    .nav-container {
      width: 100%;
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: center;
      transform: translateX(-20px);

      .mode {
        padding: 8rpx 0;
        color: #6F6A67;
        font-size: 28rpx;
        margin-right: 40rpx;

        &_active {
          font-size: 32rpx !important;
          font-weight: bold;
          color: #222222;
          position: relative;

          &:before {
            content: "";
            display: block;
            position: absolute;
            left: 50%;
            bottom: -6rpx;
            background: #222222;
            width: 30%;
            height: 4rpx;
            transform: translateX(-50%);
            border-radius: 6rpx;
          }
        }
      }
    }
  }
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.btn-icon {
  font-size: 32rpx;
  line-height: 1;
}

.btn-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 600;
  letter-spacing: 1rpx;
  line-height: 1;
}
</style>
