<script>
import TemplateImport from '@/subpkgExternal/pages/v2/components/TemplateImport.vue'
import ImportRecord from '@/subpkgExternal/pages/v2/components/ImportRecord.vue'
import BillImport from '@/subpkgExternal/pages/v2/components/BillImport.vue'
import MNav from '@/components/m-nav/m-nav.vue'

export default {
  name: 'ImportTask',
  components: { MNav, BillImport, ImportRecord, TemplateImport },
  data () {
    return {
      mode: 'BILL',
      timer: null
    }
  },
  computed: {
    companyId () {
      return this.$store.state.user.allyId
    },
    importTaskRecord () {
      return this.$store.state.external.importTaskRecord
    }
  },
  methods: {
    getRecord () {
      this.$store.dispatch('external/GetRecord')
    },
    startTimer () {
      if (this.timer) return
      this.timer = setInterval(() => {
        this.getRecord()
      }, 3 * 1000)
    }
  },
  created () {
    this.getRecord()
    this.startTimer()
  },
  beforeDestroy () {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
}
</script>

<template>
  <view>
    <MNav title="导入数据" is-show-back top-bar-color="#F7F7F7"
          status-bar-color="#F7F7F7"/>
    <view class="page-container">
      <view class="header flex-row">
        <view class="mode" :class="{'mode_active':mode === 'BILL'}"
              :style="{borderRadius:mode ==='TEMPLATE'?'0 0 18rpx 0 ':''}" @click="mode = 'BILL'">战报导入
        </view>
        <view class="mode" :class="{'mode_active':mode === 'TEMPLATE'}"
              :style="{borderRadius:mode ==='BILL'?'0 0  0 18rpx':''}" @click="mode = 'TEMPLATE'">模板导入
        </view>
      </view>
      <view class="container" :style="{flex:importTaskRecord && importTaskRecord.length?'':1}">
        <BillImport v-if="mode === 'BILL'"/>
        <TemplateImport v-if="mode === 'TEMPLATE'"/>
      </view>
      <ImportRecord v-if="importTaskRecord && importTaskRecord.length"
                    :style="{flex:importTaskRecord && importTaskRecord.length?1:''}"/>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  flex: 1;
  margin: 24rpx;
  border-radius: 18rpx 18rpx 0 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .header {
    background: white;

    .mode {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
      padding: 24rpx 0;
      text-align: center;
      background: #F3F3F3;
      border-radius: 0 0 18rpx 18rpx;

      &_active {
        font-weight: 500;
        font-size: 28rpx;
        color: #0052D9;
        background: white;
        border-radius: 0;
      }
    }
  }

  .container {
    padding: 24rpx 32rpx;
    overflow: hidden;
    border-radius: 0 0 24rpx 24rpx;
    background: white;
    margin-bottom: 24rpx;
  }
}
</style>
