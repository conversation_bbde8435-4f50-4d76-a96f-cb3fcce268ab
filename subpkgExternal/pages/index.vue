<template>
  <view class="external_v2_home_index" :style="{...external_v2_home_indexStyle}">
    <ImportTask v-if="activeTabbarIndex === 0"/>
    <ImportData v-if="activeTabbarIndex === 1" style="flex: 1;overflow:hidden;"/>
    <view class="tabbar">
      <uv-tabbar :value="activeTabbarIndex" @change="index => activeTabbarIndex = index" :fixed="true"
                 :placeholder="true" :safeAreaInsetBottom="true">
        <uv-tabbar-item :text="item.name" v-for="item in tabbarList" :key="item.id">
          <template #active-icon>
            <image class="tab-icon" :src="item.active"/>
          </template>
          <template #inactive-icon>
            <image class="tab-icon" :src="item.inactive"/>
          </template>
        </uv-tabbar-item>
      </uv-tabbar>
    </view>
  </view>
</template>

<script>
import MNav from '@/components/m-nav/m-nav.vue'
import ImportTask from '@/subpkgExternal/pages/v2/components/ImportTask.vue'
import ImportData from '@/subpkgExternal/pages/v2/components/ImportData.vue'

export default {
  components: { ImportTask, ImportData, MNav, },
  data () {
    return {
      activeTabbarIndex: 0,
      tabbarList: [{
        id: 'import',
        name: '导入数据',
        active: 'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/external_tabbar_import_active.png',
        inactive: 'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/external_tabbar_import.png'
      }, {
        id: 'store_bill',
        name: '门店账单',
        active: 'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/external_tabbar_data_active.png',
        inactive: 'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/external_tabbar_data.png'
      }]
    }
  },
  computed: {
    external_v2_home_indexStyle () {
      if (this.activeTabbarIndex === 1) {
        return { height: '100vh', overflow: 'hidden' }
      }
      return {}
    }
  },
  methods: {
  },
  onLoad () {
  }
}
</script>

<style lang="scss">
.external_v2_home_index {
  min-height: 100vh;
  //overflow: hidden;
  display: flex;
  flex-direction: column;

  .tab-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 6rpx;
  }
}
</style>
