<script>
import { findStoreListReq, storeMapper } from '@/subpkgExternal/api/home'
import { getOrgRouteFilter } from '@/utils/role'
import { getGraphResult } from '@/utils/requestUtil'
import ZbTable from '@/uni_modules/zb-table/components/zb-table/zb-table.vue'
import MScroll from '@/components/m-scroll/m-scroll.vue'
import { columns } from './storeProTableConfig'
import { findSettingApi, setDirectKeyApi } from '@/api/common'

export default {
  name: 'Store',
  components: {
    MScroll,
    ZbTable
  },
  data () {
    return {
      storeList: [],
      loading: false,
      columns: columns,
      after: null,
      hasNext: true,
      searchKeyword: '', // 搜索关键词
      // 编辑弹窗相关数据
      editStoreData: {
        storeId: '',
        storeName: '',
        operateGoal: '',
        rentShare: ''
      }
    }
  },
  methods: {
    rowClick (row) {
      // 弹出popup 显示门店ID和门店名称，编辑运营目标和分成
      this.editStoreData = {
        storeId: row.storeId || '',
        storeName: row.storeName || '',
        operateGoal: row.operateGoal || '',
        rentShare: row.rentShare || '',
        unitPrice: +row.unitPrice || ''
      }
      this.$refs.editStorePopup.open()
    },

    // 关闭编辑弹窗
    closeEditPopup () {
      this.$refs.editStorePopup.close()
    },

    // 保存编辑
    async saveStoreEdit () {
      const rentShare = this.editStoreData.rentShare
      const operateGoal = this.editStoreData.operateGoal
      const unitPrice = this.editStoreData.unitPrice
      if (!rentShare) return this.$modal.msg('请填写分成A')
      if (!operateGoal) return this.$modal.msg('请填写分成B')
      if (+rentShare > 100 || +rentShare < 0) return this.$modal.msg('分成A不能小于0或大于100')
      if (+operateGoal > 100 || +operateGoal < 10) return this.$modal.msg('分成B不能小于10或大于100')
      if (+operateGoal > +rentShare) return this.$modal.msg('分成A不能大于分成B')
      if (!unitPrice) return this.$modal.msg('请填写单价')
      if (+unitPrice < 0 || +unitPrice >= 99) return this.$modal.msg('单价过高')
      const revenueSharingStorekeeperKey = 'RevenueSharingStorekeeperDto'
      const operatingSettingKey = 'OperatingSettingDto'
      const billingConfigPowerbankKey = 'BillingConfigPowerbankDto'

      const revenueSharingStorekeeperResult = await findSettingApi({
        orgId: this.editStoreData.storeId,
        key: revenueSharingStorekeeperKey,
        mode: null
      })
      const revenueSharingStorekeeperObject = revenueSharingStorekeeperResult.data.findSetting.object

      const operatingSettingResult = await findSettingApi({
        orgId: this.editStoreData.storeId,
        key: operatingSettingKey,
        mode: null
      })
      const operatingSettingObject = operatingSettingResult.data.findSetting.object

      const billingConfigPowerbankResult = await findSettingApi({
        orgId: this.editStoreData.storeId,
        key: billingConfigPowerbankKey,
        mode: null
      })
      const billingConfigPowerbankObject = billingConfigPowerbankResult.data.findSetting.object

      await setDirectKeyApi({
        orgId: this.editStoreData.storeId,
        key: revenueSharingStorekeeperKey,
        ...{
          ...revenueSharingStorekeeperObject,
          rentShare
        }
      })
      await setDirectKeyApi({
        orgId: this.editStoreData.storeId,
        key: operatingSettingKey,
        ...{
          ...operatingSettingObject,
          goal: operateGoal
        }
      })

      await setDirectKeyApi({
        orgId: this.editStoreData.storeId,
        key: billingConfigPowerbankKey,
        ...{
          ...billingConfigPowerbankObject,
          unitPrice: +unitPrice * 100
        }
      })
      this.$modal.msg('修改成功')
      this.$refs.editStorePopup.close()
      await this.getDataList()
    },
    async getDataList (done) {
      if (this.loading) {
        return
      }
      if (!done) {
        this.after = null
        this.hasNext = true
        this.storeList = []
        this.$modal.loading('加载中...')
      }
      if (!this.hasNext) {
        return done && done('ok')
      }
      const variables = {
        first: 20,
        after: this.after,
        routeTypes: getOrgRouteFilter('PARENT').routeTypes,
        sort: 'id',
        dir: 'DESC',
        filter: {
          tagIds: [14],
          orgType: 'STORE',
          suspend: false,
          fuzzySearch: this.searchKeyword
        }
      }
      this.loading = true
      const result = await findStoreListReq(variables)
      const { list, pageInfo } = getGraphResult(result)
      console.log(list)
      this.storeList.push(...storeMapper(list))
      this.after = pageInfo.endCursor
      this.loading = false
      uni.hideLoading()
      if (pageInfo.hasNextPage) {
        done && done()
      } else {
        this.hasNext = false
        done && done('ok')
      }
    },

    // 搜索确认处理
    onSearchConfirm () {
      console.log('搜索关键词:', this.searchKeyword)
      this.after = null
      this.hasNext = true
      this.storeList = []
      this.getDataList()
    },
  },
  created () {
    this.getDataList()
  }
}
</script>

<template>
  <view style="flex: 1;overflow:hidden;display: flex;flex-direction: column;">
    <!-- 搜索区域 -->
    <view class="search-container">
      <uni-easyinput
          v-model="searchKeyword"
          placeholder="搜索门店名称"
          prefixIcon="search"
          :clearable="true"
          @confirm="onSearchConfirm"
          @clear="onSearchConfirm"
          class="search-input"
      />
    </view>

    <!-- 表格区域 -->
    <view style="flex: 1;overflow:hidden;">
      <MScroll :list="storeList" scroll-x first-loading no-refresh :loading="loading">
        <template #default>
          <zb-table
              ref="zbTable"
              :data="storeList"
              :columns="columns"
              show-header
              :pullUpLoading="getDataList"
              is-show-load-more
              @rowClick="rowClick"
          ></zb-table>
        </template>
      </MScroll>
    </view>

    <!-- 编辑门店弹窗 -->
    <uni-popup ref="editStorePopup" type="center" :animation="false" :mask-click="false">
      <view class="edit-popup-box">
        <view class="edit-popup-title">门店编辑</view>

        <!-- 门店信息显示 -->
        <view class="store-info-section">
          <view class="store-name">{{ editStoreData.storeName }}</view>
          <view class="external-id">ID：{{ editStoreData.storeId }}</view>
        </view>

        <!-- 编辑区域 -->
        <view class="edit-section">
          <view class="edit-row">
            <text class="edit-label">计费单价</text>
            <uni-easyinput
                v-model="editStoreData.unitPrice"
                type="number"
                :clearable="false"
                placeholder="请输入"
                class="edit-input"
            />
            <text class="unit-text"></text>
          </view>
          <view class="edit-row">
            <text class="edit-label">分成A</text>
            <uni-easyinput
                v-model="editStoreData.rentShare"
                type="digit"
                :clearable="false"
                placeholder="请输入"
                class="edit-input"
            />
            <text class="unit-text">%</text>
          </view>
          <view class="edit-row">
            <text class="edit-label">分成B</text>
            <uni-easyinput
                v-model="editStoreData.operateGoal"
                type="number"
                :clearable="false"
                placeholder="请输入"
                class="edit-input"
            />
            <text class="unit-text">%</text>
          </view>
        </view>

        <!-- 按钮区域 -->
        <view class="btn-box">
          <view class="cancel-btn" @click="closeEditPopup">取消</view>
          <view class="save-btn" @click="saveStoreEdit">确认</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<style scoped lang="scss">
.search-container {
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input {
  border-radius: 16rpx;

  :deep(.uni-easyinput__content) {
    border-radius: 16rpx;
    border: 2rpx solid #e5e5e5;
    background-color: #f8f9fa;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #007aff;
      background-color: #fff;
      box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
    }
  }

  :deep(.uni-easyinput__content-input) {
    font-size: 28rpx;
    color: #333;

    &::placeholder {
      color: #999;
    }
  }

  :deep(.uni-easyinput__content-prefix) {
    color: #666;
  }

  :deep(.uni-easyinput__content-clear) {
    color: #999;
  }
}

// 编辑弹窗样式
.edit-popup-box {
  width: 640rpx;
  padding: 48rpx 40rpx 36rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-sizing: border-box;

  .edit-popup-title {
    margin-bottom: 40rpx;
    text-align: center;
    font-size: 32rpx;
    color: #151515;
    font-weight: 600;
  }

  .store-info-section {
    margin-bottom: 32rpx;
    padding: 16rpx 24rpx;
    background: #F0F1F4;
    border-radius: 16rpx;
    .store-name{
      font-weight: 400;
      font-size: 28rpx;
      color: #2E2C2B;
      margin-bottom: 4rpx;
    }
    .external-id{
      font-weight: 300;
      font-size: 20rpx;
      color: #979494;
    }
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      font-size: 28rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .edit-section {
    margin-bottom: 40rpx;

    .edit-row {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      font-size: 28rpx;
      position: relative;
      &:last-child {
        margin-bottom: 0;
      }

      .edit-label {
        font-weight: 400;
        font-size: 28rpx;
        color: #7F8998;
        width: 112rpx;
        flex-shrink: 0;
        margin-right: 24rpx;
      }


      .edit-input {
        flex: 1;
        margin-right: 16rpx;

        :deep(.uni-easyinput__content-input) {
          font-size: 28rpx;
          color: #333;

          &::placeholder {
            color: #999;
          }
        }
      }

      .unit-text {
        color: #B4B8C0;
        font-size: 28rpx;
        font-weight: 400;
        position: absolute;
        right: 32rpx;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }

  .btn-box {
    display: flex;
    gap: 24rpx;

    .cancel-btn,
    .save-btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 28rpx;
      border-radius: 12rpx;
      transition: all 0.3s ease;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;

      &:active {
        background-color: #e8e8e8;
      }
    }

    .save-btn {
      background-color: #007aff;
      color: #fff;

      &:active {
        background-color: #0056cc;
      }
    }
  }
}
</style>
