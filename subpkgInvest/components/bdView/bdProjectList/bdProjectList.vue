<template>
  <view class="bd-project-list">
    <MNav title="项目列表" :is-show-back="true" top-bar-color="#F7F7F7" status-bar-color="#F7F7F7"/>
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view style="margin: 0 24rpx;padding: 24rpx 0">
        <uni-easyinput @input="onSearchInput"
                       @clear="onSearchInput"
                       placeholder="请输入项目名称"
                       placeholder-style="font-size: 24rpx;color: #B2B0AF;" prefixIcon="search"
                       prefix-icon-size="12"
                       v-model="searchQuery"/>
      </view>
      <view class="flex-row">
        <uni-data-select
            v-if="false"
            v-model="currentSort.value"
            :localdata="sortOptions"
            :clear="false"
            @change="handleSortChange"
        ></uni-data-select>
        <uni-data-select
            v-model="currentStatus.value"
            :localdata="statusOptions"
            :clear="false"
            @change="handleStatusChange"
        ></uni-data-select>
      </view>
    </view>

    <!-- 排序选项弹出层 -->
    <uni-popup ref="sortPopup" type="bottom" @change="onSortPopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">排序方式</text>
          <view class="popup-close" @tap="closeSortPopup">
            <uni-icons type="close" size="20" color="#999"></uni-icons>
          </view>
        </view>
        <view class="popup-body">
          <view
              v-for="(item, index) in sortOptions"
              :key="index"
              class="popup-item"
              :class="{ active: currentSort.value === item.value }"
              @tap="selectSort(item)"
          >
            <text>{{ item.text }}</text>
            <uni-icons v-if="currentSort.value === item.value" type="checkmarkempty" size="18" color="#3935FD"></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 状态选项弹出层 -->
    <uni-popup ref="statusPopup" type="bottom" @change="onStatusPopupChange">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">项目状态</text>
          <view class="popup-close" @tap="closeStatusPopup">
            <uni-icons type="close" size="20" color="#999"></uni-icons>
          </view>
        </view>
        <view class="popup-body">
          <view
              v-for="(item, index) in statusOptions"
              :key="index"
              class="popup-item"
              :class="{ active: currentStatus.value === item.value }"
              @tap="selectStatus(item)"
          >
            <text>{{ item.text }}</text>
            <uni-icons v-if="currentStatus.value === item.value" type="checkmarkempty" size="18" color="#3935FD"></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>

    <view class="project-list">
      <view class="list-header">
        <view class="header-item project">
          <view>项目</view>
          <view  style="color: #D2D2D2">状态</view>
        </view>
        <view class="header-item progress">进度</view>
        <view class="header-item amount">
          <view>佣金</view>
          <view style="color: #D2D2D2">投资金额</view>
        </view>
        <view class="header-item return">
          <view >实际月流水</view>
          <view style="color: #D2D2D2">预期月流水</view>
        </view>
      </view>
      <view style="flex: 1;overflow: hidden">
        <MScroll  style="height: 100%" :list="projectList" show-loading :loading="loading" @scrolltolower="getInvestorList(false)">
          <view
              v-for="(project, index) in projectList"
              :key="index"
              class="project-item"
              @click="goDetail(project.id)"
          >
            <view class="project-info">
              <view class="project-name text-ellipsis">{{ project.name }}</view>
              <view class="status-rating">
                <view class="flex-row" style="margin-bottom: 4rpx;">
                  <StatusTag :status="project.status"/>
                  <view class="cooperative-model" v-for="cooperativeModel in project.cooperativeModel">
                    {{ cooperativeModel.desc }}
                  </view>
                </view>
                <view class="project-rating">
                  <uni-rate :value="project.rating" size="12" readonly></uni-rate>
                </view>
              </view>
            </view>
            <view class="project-progress">
              <ArcBar size="50" :value="project.progress"/>
            </view>
            <view class="project-amounts">
              <view class="return-amount">{{ project.totalBusinessCommission }}</view>
              <view class="invest-amount">{{ project.actualInvestmentAmount }}</view>
            </view>
            <view class="project-returns">
              <view class="actual-return">{{ project.actualMonthlyTurnover }}</view>
              <view class="expected-return">{{ project.estimatedMonthlyTurnover }}</view>
            </view>
          </view>
        </MScroll>
      </view>
    </view>
  </view>
</template>

<script src="./bdProjectList.js">
</script>

<style lang="scss" scoped>
@import "bdProjectList";
</style>
