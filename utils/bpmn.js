import request from "@/utils/request";

/**
 * 启动流程
 * @param {string} processId - 流程ID
 * @param {Object} variables - 流程变量
 * @returns {Promise} - 返回一个Promise，包含启动流程的结果
 */
export function startProcess(processId, variables) {
    const inputVariables = formatInput(variables);
    return request({
        url: '/bpmn/graphql',
        method: 'post',
        data: {
            query: ` mutation($processId: String!, $variables: [VariableInput!]!) {
                startProcess(processId: $processId, variables: $variables) {
                    processKey
                }
            }`,
            variables: {
                processId,
                variables: inputVariables
            }
        }
    });
}

/**
 * 完成任务
 * @param {string} taskId - 任务ID
 * @param {Object} input - 输入变量
 * @returns {Promise} - 返回一个Promise，包含完成任务的结果
 */
export function completeTask(taskId, input) {
    return request
    ({
        url:'/bpmn/graphql',
        method:'post',
        data: {
            query: `
            mutation($taskId: String!, $inputVariables: [VariableInput!]!) {
                completeTask(taskId: $taskId variables:$inputVariables) {
                    id
                    processKey
                }
            }`,
            variables: {
                taskId,
                inputVariables: formatInput(input)
            }
        }
    })
}

/**
 * 根据流程键和任务定义ID获取任务
 * @param {Object} params - 参数对象
 * @param {string} params.processKey - 流程键
 * @param {string} params.taskDefinitionId - 任务定义ID
 * @returns {Promise} - 返回一个Promise，包含任务列表和总数
 */
export function getTaskByProcessKey({ processKey, taskDefinitionId }) {
    console.log(processKey, taskDefinitionId)
    return request({
        url: '/bpmn/graphql',
        method: 'post',
        data: {
            query:`
        {
          tasks(
            page:{ pageSize:100 }
            query:{
              processKey: "${processKey}"
              taskDefinitionId: "${taskDefinitionId}"
              sort: {field: "creationTime", order: DESC}
              state: CREATED
            }
          ) {
            items {
              ...on Task {
                id
                variables {
                  id,
                  name,
                  value,
                  isValueTruncated
                }
              }
            }
            total
          }
      }`
        }
    })
}

export function getProcessByKey({ processKey }) {
    return request({
        url: '/bpmn/graphql',
        method: 'post',
        data: {
            query:`
        {
          processes(query: { processKey: "${processKey}", sort: {field: "creationTime", order: DESC}}, page: {pageSize: 100, },) {
            total,
            sortValues,
            items {
              ... on Process {
                processDefinitionId
                processId: id,
                creationTime,
                completionTime,
                status,
                state,
                variables {
                  id,
                  name,
                  value,
                }
                users 
              }
              ... on Task {
                processKey
                taskId: id
              }
            }
          }
      }`
        }
    })
}

/**
 * 格式化输入变量
 * @param {Object} input - 输入变量对象
 * @returns {Array} - 返回格式化后的变量数组
 */
function formatInput(input) {
    const inputVariables = []
    for (let reqKey in input) {
        inputVariables.push({
            name: reqKey,
            value: JSON.stringify(input[reqKey])
        })
    }
    return inputVariables
}
