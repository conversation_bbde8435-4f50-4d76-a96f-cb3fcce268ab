<template>
  <view>
    <view class="content">
      <view>
        <CheckoutRole/>
      </view>
      <view class="card-stat">
        <view class="private-domain-user-card">
          <view style="font-size: 28rpx;color: rgba(255,255,255,0.85);margin-bottom: 16rpx">引流用户数</view>
          <view style="font-size: 48rpx;color: #FFFFFF;font-weight: 600;margin-bottom: 8rpx">{{ privateDomainStat.totalExtUser }}</view>
          <view style="font-size: 24rpx;color: rgba(255,255,255,0.75);">
            本月新增：{{ privateDomainStat.currentMonthTotalNewContactCount }}
          </view>
        </view>
        <view class="order-stat-card">
          <view style="font-size: 28rpx;color: #6F6A67;margin-bottom: 16rpx">租借宝总数</view>
          <view style="font-size: 48rpx;color: #2E2C2B;font-weight: 600;margin-bottom: 8px">{{ orderStat.allOrder }}</view>
          <view style="font-size: 24rpx;color: #979494;">本月新增：{{ orderStat.thisMonthOrder }}</view>
        </view>
      </view>

      <view class="charts">
        <view style="display: flex;justify-content: space-between;align-items: center">
          <view style="font-size: 32rpx; color: #2E2C2B; font-weight: 600;">日变化趋势</view>
          <view class="flex-row" style="font-size: 24rpx;color: #000000;">
            <view class="flex-row">
              <view style="width: 40rpx;height: 16rpx;background: linear-gradient(to top, #3935FD 0%, rgba(107,139,255,0.5) 100%), #D9D9D9;border-radius: 2rpx 2rpx 2rpx 2rpx;"></view>
              <view style="margin-left: 16rpx">引流人数</view>
            </view>
            <view class="flex-row" style="margin-left: 24rpx">
              <view style="width: 40rpx;height: 0rpx;border: 2rpx solid #3935FD;"></view>
              <view  style="margin-left: 16rpx">租借宝数</view>
            </view>
          </view>
        </view>
        <qiun-data-charts
            type="mix"
            :opts="chartsOpts"
            :chartData="chartData"
        />
      </view>

      <view class="private-domain-user-list-card">
        <view style="font-size: 32rpx; color: #2E2C2B; font-weight: 600;margin-bottom: 32rpx">引流用户列表</view>
        <scroll-view
            class="user-list"
            scroll-y
            @scrolltolower="loadExtUserDetail"
            :style="{ height: '300rpx' }"
        >
          <view class="user-item" v-for="(user, index) in privateDomainStat.extUsers" :key="index">
            <view style="display: flex">
              <image src="/static/logo.png"></image>
              <view class="name">{{ user.name }}</view>
            </view>
            <view class="time">{{ user.joinTime }}</view>
          </view>
        </scroll-view>
      </view>

    </view>


    <uv-tabbar @change="tabbarChange">
      <uv-tabbar-item text="门店管理">
        <template v-slot:inactive-icon>
          <OssImage icon="private-domain-store-manage.png" style="width: 48rpx;height: 48rpx;"/>
        </template>
      </uv-tabbar-item>
      <uv-tabbar-item text="提现">
        <template v-slot:inactive-icon>
          <OssImage icon="storekeeper-private-domain-tixian.png" style="width: 48rpx;height: 48rpx;"/>
        </template>
      </uv-tabbar-item>
      <uv-tabbar-item text="我的">
        <template v-slot:inactive-icon>
          <OssImage icon="private-domain-profile.png" style="width: 48rpx;height: 48rpx;"/>
        </template>
      </uv-tabbar-item>
    </uv-tabbar>
  </view>
</template>

<script>
import { manageId, userList } from "@/utils/role";

import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";
import { formatDate } from "@/uni_modules/uni-dateformat/components/uni-dateformat/date-format";
import {
  findExtUserDetailReq,
  findExtUserListReq,
  findOrgDetailByIdReq,
  findWeComPrivateDomainStatReq
} from "@/subpkgPrivateDomain/api/privateDomain";
import OssImage from "@/components/oss-image/oss-image.vue";
import CheckoutRole from "@/components/checkout-role/checkout-role.vue"

export default {
  components: {
    OssImage,
    CheckoutRole
  },
  data() {
    return {
      value: 0,
      roleId: "", // 身份id
      privateDomainStat: {},
      wxCpUserId: "",
      orderStat: {},
      chartData: {},
      extUserList: [],
      extUserDetails: [],
      pageSize: 10,
      currentPage: 1,
    };
  },
  methods: {
    tabbarChange(value) {
      console.log("value", value)
      switch (value) {
        case 0:
          return this.$tab.navigateTo('/subpkgShop/shop-manage/shop-manage')
        case 1:
          return this.$tab.navigateTo('/subpkgPrivateDomain/pages/withdraw/withdraw')
        case 2:
          return this.$tab.navigateTo('/pages/my/accountAndRole/accountAndRole')
      }

    },
    async getExtUserList() {
      const { data } = await findExtUserListReq(this.manageId);
      this.extUserList = data.result;
      await this.loadExtUserDetail();
    },
    async loadExtUserDetail() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      const extUserIds = this.extUserList.slice(start, end);

      if (extUserIds.length > 0) {
        const { data } = await findExtUserDetailReq(extUserIds);

        const userDetailMap = {};
        data.result.forEach(detail => {
          console.log(detail)
          const externalContact = detail.externalContact;
          if (externalContact) {
            userDetailMap[externalContact.externalUserId] = {
              name: externalContact.name,
              avatar: externalContact.avatar,
              createTime: detail.followedUsers.find(followedUser => followedUser.userId === this.wxCpUserId).createTime * 1000
            };
          }
        });

        const newUserDetails = extUserIds.map(userId => userDetailMap[userId] || {});

        this.extUserDetails = this.extUserDetails.concat(newUserDetails);
        this.currentPage++;
      }
    },
    formatDate,
    dayjs,
    async getPrivateDomainStat() {
      const { data } = await findWeComPrivateDomainStatReq(+this.manageId)
      this.privateDomainStat = {
        totalExtUser: data.result.countGroupChatMember,
        currentMonthTotalNewContactCount: data.result.countCurrentMonthGroupChatMember,
        extUsers: data.result.members.map(item => ({
          name: item.name,
          joinTime: formatDate(+item.joinTime * 1000, 'yyyy-MM-dd hh:mm:ss')
        })),
        groupChatMemberTrend: data.result.groupChatMemberTrend,
      }
    },
    async getOrgInfo() {
      const { data } = await findOrgDetailByIdReq(this.manageId)
      this.wxCpUserId = data.result.responsiblePerson.wxCpUserId
      const framework = data.result.framework
      this.orderStat = {
        allOrder: framework.allOrder.dataset.countOrder,
        lastMonthOrder: framework.lastMonthOrder.dataset.countOrder,
        thisMonthOrder: framework.thisMonthOrder.dataset.countOrder,
        seriesData: framework.seriesData
      }
    },
    toShopManage() {
      uni.navigateTo({
        url: "/subpkgShop/shop-manage/shop-manage",
      });
    },
    toAccountAndRole() {
      uni.navigateTo({
        url: "/pages/my/accountAndRole/accountAndRole",
      });
    },
    async getServerData() {
      await Promise.all([this.getPrivateDomainStat(), this.getOrgInfo()])
      let res = {
        categories: this.orderStat?.seriesData?.map(item => {
          const originalDate = item.date;
          const [year, month, day] = originalDate.split('-');
          return `${+month}/${day}`;
        }),
        series: [
          {
            name: "引流人数",
            index: 1,
            type: "column",
            data: this.privateDomainStat.groupChatMemberTrend.map(item => item.count)
          },
          {
            name: "租借宝数",
            type: "line",
            color: "#3935FD",
            data: this.orderStat?.seriesData?.map(item => item.dataset.countOrder)
          }
        ]
      };
      this.chartData = JSON.parse(JSON.stringify(res));
    },
  },
  computed: {
    userList,
    manageId,
    chartsOpts() {
      return {
        color: ["#3935FD", "#3935FD"],
        padding: [15, 15, 15, 15],
        enableScroll: false,
        legend: {
          show: false, // 显示图例
          position: 'top', // 图例位置，可以调整为 'top', 'bottom', 'left', 'right'
          layout: 'horizontal', // 图例布局
          itemGap: 20, // 图例项间距
        },
        xAxis: {
          disableGrid: true,
          title: ""
        },
        yAxis: {
          disabled: true,
          disableGrid: false,
          splitNumber: 5,
          gridType: "dash",
          gridColor: "#fafafa",
          padding: 10,
          data: [
            {
              position: "left",
              title: "",
            },
            {
              position: "right",
              min: 0,
              title: "",
              textAlign: "left"
            }
          ]
        },
        extra: {
          mix: {
            column: {
              width: 20
            }
          }
        }
      }
    }
  },
  async onLoad() {
    await this.getServerData();
    this.getExtUserList();
  }
}
</script>

<style lang="scss" scoped>
.content {
  padding: 14rpx 24rpx;
}


.card-stat {
  margin-top: 16rpx;
  display: flex;
  justify-content: space-between;
  $padding: 24rpx;

  .private-domain-user-card, .order-stat-card {
    display: flex;
    flex-direction: column;
    padding: $padding;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
  }

  .private-domain-user-card {
    width: calc(402rpx - #{2 * $padding});
    height: calc(210rpx - #{ 2 * $padding});
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(57, 53, 253, 0.1);
    background: #3935FD;
  }

  .order-stat-card {
    width: calc(284rpx - #{2 * $padding});
    height: calc(210rpx - #{ 2 * $padding});
    background: #FFFFFF;
    box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  }
}

.charts {
  $padding: 24rpx;
  margin: 16rpx auto;
  padding: $padding;
  border-radius: 8rpx;
  background: #FFFFFF;
}


.private-domain-user-list-card {
  margin: 16rpx 0;
  background: #FFFFFF;
  padding: 24rpx;
  border-radius: 8rpx;

  .user-list {
    .user-item {
      display: flex;
      justify-content: space-between;
      padding: 12rpx 0;

      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 24rpx;
      }

      .name {
        font-size: 28rpx;
        color: #2E2C2B;
      }

      .time {
        font-size: 28rpx;
        color: rgb(178, 176, 175);
      }
    }
  }
}

::v-deep .uni-select-lay-select {
  height: 17px !important;
  border: none !important;
  padding-left: 0 !important;
  padding-right: 40rpx !important;
}

::v-deep .uni-select-lay .uni-select-lay-select .uni-select-lay-icon::before {
  display: none !important;
}

::v-deep .uni-select-lay .uni-select-lay-select .uni-select-lay-input {
  font-size: 24rpx !important;
}

::v-deep .uni-select-lay-item {
  font-size: 24rpx !important;
  padding: 20rpx 20rpx !important;
  line-height: 40rpx !important;
}

::v-deep .uni-select-lay .uni-select-lay-options .uni-select-lay-item.active {
  background: #fa8b5d !important;
}

::v-deep .uni-select-lay .uni-select-lay-options .nosearch {
  font-size: 24rpx !important;
}

::v-deep .uni-select {
  border: none !important;
  height: 17px !important;
}

::v-deep .uni-select__input-box {
  height: 17px !important;
}

::v-deep .uni-select__input-text {
  margin-right: 8rpx !important;
  font-size: 24rpx !important;
}

::v-deep .uni-select__selector-item {
  font-size: 24rpx !important;
}

</style>
