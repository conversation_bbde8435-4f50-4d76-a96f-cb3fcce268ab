<script>
import {
  createWeComConfigReq,
  createWxCpUserReq,
  findWxCpUserIdReq,
  hasDefaultExtUserReq
} from "@/subpkgPrivateDomain/api/privateDomain";
import { manageId } from "@/utils/role";
import { createChatGroup, isInWeCom, weComLogin } from "@/utils/weComUtil";
import { toast } from "@/utils/common";
import OssImage from "@/components/oss-image/oss-image.vue";

export default {
  name: "CreateGroupChat",
  components: { OssImage },
  props: {
    storeName: String,
    orgId: String | Number,
  },
  data() {
    return {
      applyUrl: '12899408800',
      groupName: '',
      freeTime:60,
      welcome: '',
      wxCpUserId: '',
      hasDefaultExtUser: false,
    }
  },
  methods: {
    openPreviewImage(){
      uni.previewImage({urls:[this.xzsQRCode]})
    },
    async getWxCpUserId() {
      const { data } = await findWxCpUserIdReq(this.manageId)
      this.wxCpUserId = data.result.responsiblePerson.wxCpUserId
    },
    async checkHasDefaultExtUser() {
      const { data } = await hasDefaultExtUserReq()
      console.log('checkHasDefaultExtUser', data)
      this.hasDefaultExtUser = data
    },
    // 提交申请加入企业微信
    async submitApply() {
      toast('发送申请...')
      await createWxCpUserReq()
      uni.hideLoading()
      toast('发送成功')
    },

    // 创建群聊
    async submitHandle() {
      if (!this.verifySuccess) {
        return toast('请先完成所有步骤')
      }

      if (!this.groupName || !this.welcome) {
        return toast('请填写完整信息')
      }
      uni.showLoading({
        title: '创建中...'
      });
      try {
        const groupChatId = await this.createGroupChat()
        if(!groupChatId){
          return this.$modal.msg('创建群聊失败，请检查是否实名')
        }
        const data = {
          groupChatId,
          orgId: +this.orgId,
          configType: 'GROUP_CHAT',
          freeTime: this.freeTime,
          welComeInfo: {
            welComeMsg: this.welcome
          }
        }
        console.log('data:', data);
        await createWeComConfigReq(data)
        toast('创建成功')
        uni.hideLoading()
        this.$emit('refresh')
      } catch (e) {
        toast(e.message)
        uni.hideLoading()
      }

    },
    async createGroupChat() {
      try {
        await weComLogin()
        return await createChatGroup([this.wxCpUserId, 'WuKongXiaoZhuShou'], this.groupName)
      } catch (error) {
        console.log(error)
        uni.hideLoading()
        toast(`创建群聊失败`)
      }
    },
    init() {
      this.getWxCpUserId()
      this.checkHasDefaultExtUser()
    }
  },
  computed: {
    xzsQRCode(){
      return `https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/private-domain-xzs.png?key=${Date.now()}`
    },
    phone() {
      return this.$store.state.user.phone;
    },
    manageId,
    isInWeCom,
    verifySuccess() {
      return this.wxCpUserId && this.isInWeCom && this.hasDefaultExtUser
      // return true
    },
    freeTimeOptions(){
      return [{
        text: '30分钟',
        value: 30
      }, {
        text: '1小时',
        value: 60
      }, {
        text: '2小时',
        value: 120
      }, {
        text: '3小时',
        value: 180
      }]
    }
  },
  created() {
    this.init()
    uni.$on('refreshCreateGroupChat', () => {
      this.init()
    })
  },
  destroyed() {
    uni.$off('refreshCreateGroupChat')
  },
  watch: {
    storeName: {
      handler(newValue, oldValue) {
        this.groupName = newValue
      }
    },
    groupName: {
      handler(newValue, oldValue) {
        this.welcome = `欢迎加入【${newValue}】充电小分队！为您送上专属见面礼——每日1小时免费充电，扫码即刻免费使用！`
      }
    },
  }
}
</script>

<template>
  <view style="height: 100%;display: flex;flex-direction: column;justify-content: space-between">
    <view>
      <view class="status-box">
        <view class="status-item">
          <view class="status-header">
            <view class="status-icon">
              <OssImage
                  :icon="wxCpUserId?'private-domain-status-success%402x.png':'private-domain-status-error%402x.png'"
                  style="width: 100%;height: 100%;"/>
            </view>
            <view class="status-text">{{ wxCpUserId ? '已加入企业微信' : '未加入企业微信' }}</view>
          </view>
          <template v-if="!wxCpUserId">
            <view class="form-item">
              <text class="form-label">申请账号
                <text class="required">*</text>
              </text>
              <input type="text" class="form-input form-disable" :value="phone" disabled/>
            </view>
            <button class="submit-btn" @click="submitApply">发出申请</button>
          </template>

        </view>

        <view class="status-item">
          <view class="status-header">
            <view class="status-icon">
              <OssImage
                  :icon="isInWeCom?'private-domain-status-success%402x.png':'private-domain-status-error%402x.png'"
                  style="width: 100%;height: 100%;"/>
            </view>
            <view class="status-text">
              <view>{{ isInWeCom ? '已使用企业微信App打开小程序' : '未使用企业微信App打开小程序' }}</view>
              <view class="status-desc" v-if="!isInWeCom && wxCpUserId">
                操作流程：企业微信-工作台-“悟空快充管理工具”小程序
              </view>

            </view>
          </view>

        </view>

        <view class="status-item">
          <view class="status-header">
            <view class="status-icon">
              <OssImage
                  :icon="hasDefaultExtUser?'private-domain-status-success%402x.png':'private-domain-status-error%402x.png'"
                  style="width: 100%;height: 100%;"/>
            </view>
            <view class="status-text">
              <view>{{ hasDefaultExtUser ? '已添加小助手' : '未添加小助手' }}</view>
              <view class="status-desc" v-if="!hasDefaultExtUser && wxCpUserId && isInWeCom">长按识别二维码添加小助手</view>
            </view>
          </view>

        </view>

        <view class="qrcode-container" v-if="!hasDefaultExtUser && wxCpUserId && isInWeCom">
          <image
              show-menu-by-longpress
              @click="openPreviewImage"
              class="qrcode"
              :src="xzsQRCode"
              mode="aspectFit"></image>
        </view>
      </view>

      <view class="group-info">
        <view class="section-title">群聊信息</view>

        <view class="form-item">
          <text class="form-label">群聊名称
            <text class="required">*</text>
          </text>
          <input
              type="text"
              class="form-input"
              :class="{'form-disable': !verifySuccess}"
              placeholder="群聊名称"
              v-model="groupName"
              :disabled="!verifySuccess"
          />
        </view>

        <view class="form-item">
          <text class="form-label">会员免费时长
            <text class="required">*</text>
          </text>
          <uni-data-select v-model="freeTime" :localdata="freeTimeOptions" :clear="false"></uni-data-select>
        </view>

        <view class="form-item" v-if="false">
          <text class="form-label">进群欢迎语
            <text class="required">*</text>
          </text>
          <textarea
              class="form-textarea"
              :class="{'form-disable': !verifySuccess}"
              placeholder="欢迎加入【小龙坎火锅（高新南店）】充电小分队！为您提供专属面对面一每日小时免费充电，白搭即刻免费使用！"
              v-model="welcome"
              :disabled="!verifySuccess"
          ></textarea>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button
          :class="['create-btn', verifySuccess ? 'active' : '']"
          :style="verifySuccess ? 'background: linear-gradient( 95deg, #F9983E 0%, #FD5935 100%);' : 'background: linear-gradient( 90deg, #D9D9D9 0%, #B2B0AF 100%);'"
          @click="submitHandle"
      >
        创建企业微信群
      </button>
    </view>
  </view>
</template>

<style scoped lang="scss">

.content {
  flex: 1;
}

.status-box {
  border-radius: 8px;
  margin: 24rpx 0;
}

.status-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;
  background-color: #FFFFFF;
  padding: 24rpx;

}

.status-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.status-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-bottom: 5px;
  background-size: 100% 100%;
}

.error {
  background: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/private-domain-status-error%402x.png');
}

.success {
  background: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/private-domain-status-success%402x.png');
}

.status-text {
  font-size: 28rpx;
  color: #2E2C2B;
  margin-bottom: 5px;
  font-weight: bold;
}

.status-desc {
  font-size: 24rpx;
  color: #979494;
  font-weight: 400;
  margin-top: 8rpx;
}

.form-item {
  width: 100%;
  margin-bottom: 24rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #7F8998;
  margin-bottom: 20rpx;
}

.required {
  color: #FF6666;
}

.form-input {
  height: 40px;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 28rpx;
}

.form-disable {
  background: #F3F3F3;
  border: 1rpx solid #D9DCE2 !important;
  color: #979494 !important;
}

.form-textarea {
  width: calc(100% - 20px);
  height: 100px;
  border: 1px solid #EEEEEE;
  border-radius: 4px;
  padding: 10px;
  font-size: 28rpx;
}

.submit-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(95deg, #F9983E 0%, #FD5935 100%);
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.qrcode {
  width: 200px;
  height: 200px;
}

.group-info {
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  font-size: 32rpx;
  color: #2E2C2B;
  margin-bottom: 15px;
  font-weight: bold;
}

.footer {
  padding: 15px;
}

.create-btn {
  width: 100%;
  height: 44px;
  border-radius: 4px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 34rpx;
}
</style>
