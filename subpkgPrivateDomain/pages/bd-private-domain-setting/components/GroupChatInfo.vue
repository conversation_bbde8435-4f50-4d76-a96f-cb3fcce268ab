<script>
import {
  createWxCpUserV2Req,
  findGroupChatListReq,
  findPrivateDomainReq,
  findStoreKeeperDetailByIdReq,
  findWxCpUserIdReq
} from '@/subpkgPrivateDomain/api/privateDomain'
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format'
import { manageId } from '@/utils/role'

export default {
  name: 'GroupChatInfo',
  props: ['orgId', 'storeKeeperId','freeTime'],
  data () {
    return {
      privateDomainQrCode: '',
      chatList: [],
      wxCpUserId: '',
      storeKeeperWxCpUserId: ''
    }
  },
  methods: {
    formatDate,
    async getPrivateDomainQrCode () {
      const { data } = await findPrivateDomainReq(this.orgId)
      this.privateDomainQrCode = data.result
    },
    async getChatList () {
      const { data } = await findGroupChatListReq(this.orgId)
      this.chatList = data.result.map(x => ({
        chatId: x.chatId,
        name: x.name,
        owner: x.owner,
        memberList: x.memberList.map(y => y.name),
        createTime: formatDate((+x.createTime) * 1000, 'yyyy-MM-dd hh:mm:ss'),
        freeTime:this.getFreeText()
      }))
    },
    getFreeText(){
      if(this.freeTime % 60 === 0){
        return `${this.freeTime / 60}小时`
      }
      return `${this.freeTime}分钟`
    },
    async getWxCpUserId () {
      const { data } = await findWxCpUserIdReq(this.manageId)
      this.wxCpUserId = data.result.responsiblePerson.wxCpUserId
    },
    async getStoreKeeperWxCpUserId () {
      const { data } = await findStoreKeeperDetailByIdReq(this.storeKeeperId)
      this.storeKeeperWxCpUserId = data.result.responsiblePerson.wxCpUserId
    },
    async addWxCpUser () {
      this.$modal.loading()
      try {
        await createWxCpUserV2Req(this.storeKeeperId)
        await this.getStoreKeeperWxCpUserId()
        this.$modal.closeLoading()
        this.$modal.msg('邀请成功')
      } catch (e) {
        this.$modal.closeLoading()
      }
    }
  },
  computed: {
    manageId,
  },
  watch: {
    orgId: {
      handler (newValue, oldValue) {
        this.getPrivateDomainQrCode()
        this.getChatList()
        this.getWxCpUserId()
      },
      immediate: true
    },
    storeKeeperId: {
      handler (newValue, oldValue) {
        console.log('storeKeeperId', this.storeKeeperId)
        this.getStoreKeeperWxCpUserId()
      },
      immediate: true
    }
  }
}
</script>

<template>
  <view class="created-group">
    <view class="group_3 flex-col">
      <image
          class="image_3"
          referrerpolicy="no-referrer"
          :src="privateDomainQrCode"
      />
      <view class="section_1 flex-row justify-between">
        <view class="group_4 flex-col"></view>
        <text class="text_3">扫码即可加入门店群聊</text>
        <view class="group_5 flex-col"></view>
      </view>
      <view class="section_2 flex-col">
        <view class="text-wrapper_1" v-for="item in chatList" :key="item.chatId">
          <view>
            <text class="text_4">群名称：</text>
            <text class="paragraph_1">
              {{ item.name }}
            </text>
          </view>
          <view>
            <text class="text_5">管理员：</text>
            <text class="paragraph_2">
              {{ item.owner }}
            </text>
          </view>
          <view>
            <text class="text_6">创建时间：</text>
            <text class="paragraph_3">
              {{ item.createTime }}
            </text>
          </view>
          <view>
            <text class="text_6">私域优惠：</text>
            <text class="paragraph_3">
              {{ item.freeTime }}
            </text>
          </view>
          <text class="text_7">规模(人)：</text>
          <text class="text_8">{{ item.memberList.length }}</text>
        </view>
      </view>
    </view>
    <view class="footer">
      <view class="common-btn" style="width: 686rpx;text-align: center" v-if="!storeKeeperWxCpUserId"
            @click="addWxCpUser">邀请商户进入企微
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.created-group {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  width: 686rpx;

  padding: 24rpx 0;
  background-color: #FFFFFF;
  border-radius: 8px;
  margin: 40rpx 32rpx 0 32rpx;

  .image_3 {
    width: 638rpx;
    height: 638rpx;
    margin: 24rpx 0 0 24rpx;
  }

  .section_1 {
    height: 48rpx;
    margin: 24rpx 0 0 150rpx;
    align-items: center;

    .group_4 {
      background-color: rgba(217, 217, 217, 1);
      border-radius: 8px;
      width: 40rpx;
      height: 2rpx;
    }

    .text_3 {
      height: 48rpx;
      overflow-wrap: break-word;
      color: rgba(151, 148, 148, 1);
      font-size: 24rpx;
      letter-spacing: 1px;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      line-height: 48rpx;
      margin-left: 24rpx;
    }

    .group_5 {
      background-color: rgba(217, 217, 217, 1);
      border-radius: 8px;
      width: 40rpx;
      height: 2rpx;
      margin: 0 0 0 24rpx;
    }
  }

  .section_2 {
    border-radius: 8px;
    padding: 8rpx 16rpx;
    border: 1px solid #f0f0f0;
    justify-content: flex-center;
    margin: 24rpx 24rpx;

    .text-wrapper_1 {
      width: 590rpx;
      overflow-wrap: break-word;
      font-size: 0;
      letter-spacing: 1px;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      line-height: 48rpx;

      .text_4 {
        width: 590rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .paragraph_1 {
        width: 590rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_5 {
        width: 590rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .paragraph_2 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_6 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .paragraph_3 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_7 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_8 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }
    }

    .section_3 {
      background-color: rgba(240, 240, 240, 1);
      width: 590rpx;
      height: 2rpx;
      margin: 20rpx 0 0 24rpx;
    }

    .text-wrapper_2 {
      width: 590rpx;
      height: 192rpx;
      overflow-wrap: break-word;
      font-size: 0;
      letter-spacing: 1px;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      line-height: 48rpx;
      margin: 20rpx 0 16rpx 24rpx;

      .text_9 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .paragraph_4 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_10 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_11 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(111, 106, 103, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .paragraph_5 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_12 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .paragraph_6 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_13 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(151, 148, 148, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }

      .text_14 {
        width: 590rpx;
        height: 192rpx;
        overflow-wrap: break-word;
        color: rgba(46, 44, 43, 1);
        font-size: 24rpx;
        letter-spacing: 1px;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 48rpx;
      }
    }
  }
}

.footer {
  padding: 32rpx;
}
</style>
