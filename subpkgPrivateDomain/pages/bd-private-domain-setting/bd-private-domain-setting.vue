<template>
  <view class="container">

    <!-- 根据状态显示不同的内容 -->
    <view class="content">
      <GroupChatInfo v-if="hasGroupChat" :org-id="orgId" :store-keeper-id="storeKeeperId" :free-time="freeTime" style="height: 100%;"/>
      <CreateGroupChat :store-name="storeName" :org-id="orgId" @refresh="getWeComConfig" v-else/>
    </view>
  </view>
</template>

<script>
import CreateGroupChat from "@/subpkgPrivateDomain/pages/bd-private-domain-setting/components/CreateGroupChat.vue";
import GroupChatInfo from "@/subpkgPrivateDomain/pages/bd-private-domain-setting/components/GroupChatInfo.vue";
import { findWeComConfigReq } from "@/subpkgPrivateDomain/api/privateDomain";

export default {
  components: {
    CreateGroupChat,
    GroupChatInfo
  },
  data() {
    return {
      hasGroupChat: false,
      orgId: '',
      storeKeeperId:'',
      storeName: '',
      freeTime:''
    }
  },
  onLoad(options) {
    this.orgId = options.orgId
    this.storeKeeperId = options.storeKeeperId
    this.storeName = options.name
    console.log(this.storeName)
    this.getWeComConfig()
  },
  async onShow() {
    if(this.orgId && this.storeName){
      uni.$emit('refreshCreateGroupChat')
    }
  },
  methods: {
    async getWeComConfig() {
      const { data } = await findWeComConfigReq({ orgId: +this.orgId })
      this.hasGroupChat = !!data.result.length
      if(this.hasGroupChat){
        this.freeTime = data.result[0].userCard.freeTime
      }
    },

  },
}
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  background-color: #F5F5F5;
  display: flex;
  flex-direction: column;

  .content {
    height: 100%;
  }
}


</style>
