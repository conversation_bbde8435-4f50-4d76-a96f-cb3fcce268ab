<template>
  <view class="container">
    <mNav
        title="悟空快充"
        topBarColor="transparent"
        statusBarColor="transparent"
        is-show-back
        :bgImage="'https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/shop-banner.png'"
    />
    <image
        class="bg"
        v-if="false"
        :style="{ height: bgHeight + 'px' }"
        src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/shop-banner.png"
    ></image>
    <view
        class="notification-badge"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
        @click="handleNotificationClick"
        :style="{ left: badgePosition.x + 'px', top: badgePosition.y + 'px' }"
    >
      <img class="notice-icon" src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/notice.png" alt="">
      <view class="count" v-if="unReadCount>0">{{ unReadCount }}</view>
    </view>
    <view class="banner-bg" :style="{ '--nav-height': -navHeight + 'px' }">
      <view class="select-role-box">
        <view class="change-role-box" v-if="userList.length > 1">
          <uni-data-select
              v-model="roleId"
              :localdata="userList"
              @change="changeRole"
              :clear="false"
              placeholder="请选择"
          ></uni-data-select>
        </view>
        <view class="change-role-text" v-else>
          <text class="role-text">{{ orgNameAndRole }}</text>
        </view>
      </view>
      <view class="money-box">
        <view class="money-box-content" @click="toPaymentDetail">
          <view class="money-left money-item">
            <view class="money-text">
              <text>总收款</text>
            </view>
            <view class="money-value">
              <text class="money-value-unit">￥</text>
              <text class="money-value-text">{{ -settledMoney }}</text>
            </view>
          </view>
          <view class="money-right money-item">
            <view class="money-text">{{
                unsettledMoney <= 0 ? "余额" : "预收款"
              }}</view>
            <view class="money-value">
              <text
                  class="money-value-unit"
                  :class="{ 'premoney-text': unsettledMoney > 0 }"
              >￥</text
              >
              <text
                  class="money-value-text"
                  :class="{ 'premoney-text': unsettledMoney > 0 }"
              >{{ Math.abs(unsettledMoney) }}</text
              >
            </view>
          </view>
        </view>
        <view
            class="take-btn"
            @click="take"
            :class="{ 'take-btn-disabled': unsettledMoney < 1 }"
        >{{ taskId ? "待验证" : "提现" }}</view
        >
        <view class="take-record-tips" @click="toWithdrawRecord">
          <text>查看提现记录</text>
          <text class="take-record-icon"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getIndexData } from "@/api/subpkgShop/index.js";
import { changeRole, modifyPassword } from "@/api/login.js";
import mNav from "@/components/m-nav/m-nav.vue";
import { toast } from "@/utils/common";
import {
  isManager,
  isProvider,
  isStoreKeeper,
  loginRelaunchPage,
  orgId,
  orgNameAndRole,
  userList,
} from "@/utils/role.js";
import { setToken } from "@/utils/auth";
import { getVerifyMoneyTask, getWithdrawingList, } from "@/api/subpkgShop/withdraw.js";
import { reportReceivableAmount } from "@/api/common/report.js";
import { flattenObject, formatVariables } from "@/utils/function.js";
import { hasKAVipListPermi } from '@/utils/permissionList.js';
import { findWeComConfigReq } from "@/subpkgPrivateDomain/api/privateDomain";

export default {
  data() {
    return {
      roleId: "", // 身份id

      unsettledMoney: 0, // 待结算金额
      settledMoney: 0, // 已结算金额
      shopNum: 0, // 门店数
      storeKeeperNum: 0, // 服务商商户数
      contract: {}, // 商户合约
      panelList: [],
      chartData: {},
      modeSharing: "STOREKEEPER_SHARING",
      modePrepaySharing: "STOREKEEPER_PREPAY_SHARING",
      modePrepayRent: "STOREKEEPER_RENT",
      accountsList: [], // 提现账号列表
      withdrawalSetting: {}, // 提现设置
      generalSetting: {
        // 账单显示设置
        billShow: true,
        billDetail: false,
        billHistoryDays: 7,
        chartShow: false,
      },
      opts: {
        color: ["#fb6e15"],
        padding: [15, 9, 0, 9],
        enableScroll: false,
        legend: {
          show: false,
        },
        xAxis: {
          disableGrid: true,
        },
        yAxis: {
          gridType: "dash",
          dashLength: 1,
          disabled: true,
          data: [
            {
              min: 0,
            },
          ],
        },
        extra: {
          column: {
            type: "meter",
            width: 17,
            activeBgColor: "#000000",
            activeBgOpacity: 0.08,
            barBorderCircle: true,
            meterBorder: 0,
            meterFillColor: "#fb6e15",
          },
        },
      },

      password: "",
      confirmPassword: "",

      taskId: "", // 打款验证金额的验证；

      openSetting: {},	// 通用设置
      oldX: 0,
      oldY: 0,
      areaWidth: 500, // 设置宽度为 500rpx
      areaHeight: 500,
      badgePosition: { x: 300, y: 380 },
      touchStartX: 0,
      touchStartY: 0,
      unReadCount: 0
    };
  },
  components: {
    mNav,
  },
  computed: {
    manageId() {
      return this.$store.state.user.manageId;
    },
    navHeight() {
      return (
          getApp().globalData.navBarHeight + getApp().globalData.statusBarHeight
      );
    },
    hasWithdrawing() {
      return this.$store.state.index.hasWithdrawing;
    },
    needResetPassword() {
      return this.$store.state.user.needResetPassword;
    },
    isStoreKeeper,
    isProvider,
    userList,
    orgNameAndRole,
    isManager,
    orgId,
    hasKAVipListPermi,
  },
  async onPullDownRefresh() {
    let res = await this.fetchData();
    uni.stopPullDownRefresh();
  },
  async onLoad(options) {
    this.roleId = this.manageId;
    reportReceivableAmount();
    await this.fetchData();
    console.log(this.isManager, this.accountsList.length)
    if (this.isManager && this.accountsList.length) {
      this.taskId = await this.getVerifyAccount();
    }
    if (this.needResetPassword) {
      this.$refs.modifyPasswordPopup.open();
    }
    if(options.redirectUrl){
      uni.navigateTo({
            url: options.redirectUrl
          }
      )
    }
  },
  async onShow() {
    if (this.accountsList.length) {
      this.taskId = "";
      this.taskId = await this.getVerifyAccount();
    }
  },
  methods: {
    async changeRole() {
      let res = await changeRole(this.roleId);
      setToken(res.data);
      this.$store.commit("SET_TOKEN", res.data);
      this.$modal.loading("加载中");
      this.$store.dispatch("GetInfo").then((res) => {
        this.$modal.closeLoading();
        this.initLogin();
      });
    },
    handleTouchStart(event) {
      // 记录触摸开始的位置
      const touch = event.touches[0];
      console.log(touch)
      this.touchStartX = touch.clientX;
      this.touchStartY = touch.clientY;
      // 导航到列表页面
      // uni.navigateTo({
      // 	url: '/subpkgProfit/pages/notification/notification' // 替换为目标页面的路径
      // });
    },
    handleTouchMove(event) {
      // 更新当前位置
      const touch = event.touches[0];
      let newX = touch.clientX;
      let newY = touch.clientY;
      console.log("newX+newY"+newX+"这是"+newY)

      if (newX > 350){
        newX = 300;
      }
      if (newX < 25){
        newX = 25;
      }
      if (newY > 600){
        newY = 600;
      }
      if (newY < 200){
        newY = 200;
      }
      // 设置新的位置
      this.badgePosition.x = newX;
      this.badgePosition.y = newY;

      // 阻止默认滚动行为
      event.preventDefault();
    },
    handleTouchEnd(event) {
      // 清除触摸开始位置
      this.touchStartX = 0;
      this.touchStartY = 0;
    },
    handleNotificationClick(event) {
      // 导航到列表页面
      uni.navigateTo({
        url: '/subpkgProfit/pages/notification/notification' // 替换为目标页面的路径
      });
    },
    initLogin() {
      this.$modal.closeLoading();

      loginRelaunchPage();
    },
    async fetchData() {
      this.$modal.loading("加载中");
      let indexDataRes = await getIndexData();
      indexDataRes.data.findCurrentOrganization = flattenObject(indexDataRes.data.findCurrentOrganization)
      let developerPhone =
          indexDataRes.data.findCurrentOrganization.developer.username || "";
      let developerName =
          indexDataRes.data.findCurrentOrganization.developer.realName || "";
      this.$store.commit("index/SET_DEVELOPERNAME", developerName);
      this.$store.commit("index/SET_DEVELOPERPHONE", developerPhone);
      this.panelList = indexDataRes.data.findMyAllPortalData;
      this.shopNum = indexDataRes.data.findCurrentOrganization.countStore;
      this.storeKeeperNum =
          indexDataRes.data.findCurrentOrganization.countStoreKeeper;
      this.withdrawalSetting =
          indexDataRes.data.findCurrentOrganization.withdrawalSetting;
      this.accountsList = indexDataRes.data.findCurrentOrganization.accounts;
      if (indexDataRes.data.findCurrentOrganization.generalSetting) {
        this.generalSetting =
            indexDataRes.data.findCurrentOrganization.generalSetting;
        this.generalSetting.chartShow = this.generalSetting.chartShow === null ? this.generalSetting.billShow : this.generalSetting.chartShow;
      }

      let dayData = indexDataRes.data.findCurrentOrganization;
      this.unsettledMoney = this.getUnsettledMoney(indexDataRes);
      this.settledMoney = this.getSettledMoney(indexDataRes)
      // this.settledMoney = dayData.current.settledMoney
      //	? dayData.current.settledMoney / 100
      // 	: 0; // 已结算金额
      this.contract = dayData.contract;
      this.contract.platformFee = dayData.platformFee;

      this.drawChart(indexDataRes.data.findCurrentOrganization.orderWeekSeries);

      // 一些通用设置的处理
      // this.getOpenSetting(indexDataRes)

      this.$modal.closeLoading();
    },
    getUnsettledMoney(indexDataRes) {
      const dayData = indexDataRes.data.findCurrentOrganization;
      let unsettledMoney = dayData.current.receivable

      if (this.orgId == 38886) {
        unsettledMoney = unsettledMoney > 0 ? unsettledMoney - 4560 : unsettledMoney;	// 38886预付商户需要补齐45.6元金额
      }
      return unsettledMoney ? unsettledMoney / 100 : 0; // 待结算金额;
    },
    getSettledMoney(indexDataRes) {
      const curOrgRes = indexDataRes.data.findCurrentOrganization;
      let settledMoney = curOrgRes.current.settledMoney ? curOrgRes.current.settledMoney : 0;
      let redemptionSettingAmountSetting = curOrgRes.redemptionSetting && curOrgRes.redemptionSetting.object && curOrgRes.redemptionSetting.object.amount ? curOrgRes.redemptionSetting.object.amount : 0;

      // 因为已支付是负数，所以扣减已支付需要相加
      settledMoney = (settledMoney + redemptionSettingAmountSetting) / 100;

      console.log(settledMoney, redemptionSettingAmountSetting, '111')

      return settledMoney;
    },
    getOpenSetting(openSettingRes) {
      this.openSetting = (openSettingRes.data.findFirstOpenSettings && openSettingRes.data.findFirstOpenSettings.settingBody) || { chartShow: this.generalSetting.billShow };

      this.openSetting.chartShow = this.openSetting.chartShow === false ? this.openSetting.chartShow : this.generalSetting.billShow;
    },
    drawChart(chartData) {
      let categories = [];
      let data1 = [];
      let data2 = [];
      let data = []; // 需要把运营订单和对账周期结合起来；
      let historyTime = this.getHistoryDaysAgo();

      chartData.forEach((item) => {
        let nowTime = new Date(item.week + " 23:59:59").getTime();

        categories.push(item.week.slice(5).replace("-", "/"));

        nowTime > historyTime
            ? data.push(item.countOrder)
            : data.push(item.countValidOrder);
        data1.push(item.countOrder);
        data2.push(item.countValidOrder);
      });
      let res = {
        categories,
        series: [
          {
            name: "交易单数",
            textColor: "#fb6e15",
            data: data,
            textOffset: 0,
          },
        ],
      };
      this.chartData = JSON.parse(JSON.stringify(res));
    },
    getHistoryDaysAgo() {
      var today = new Date(); // 获取当前日期和时间
      var time = new Date(); // 创建一个新的日期对象

      time.setDate(today.getDate() - (this.generalSetting.billHistoryDays - 1)); // 将新日期对象设置为billHistoryDays天结束的0点

      // 将新日期对象的时间设置为零点
      time.setHours(0);
      time.setMinutes(0);
      time.setSeconds(0);
      time.setMilliseconds(0);

      return time.getTime();
    },
    showPopup({ type = "center", popupRef }) {
      this.$refs[popupRef].open(type);
    },
    closePopup(popupRef) {
      this.$refs[popupRef].close();
    },
    async take() {
      let { withdrawalSetting, unsettledMoney, settledMoney } = this;
      unsettledMoney = -unsettledMoney;
      // if (unsettledMoney <= 0) {
      // 	toast('暂无可提现金额哦')
      // 	return
      // };
      if (
          unsettledMoney > 0 &&
          withdrawalSetting &&
          withdrawalSetting.minimal &&
          unsettledMoney < withdrawalSetting.minimal / 100
      ) {
        // toast(`可提现金额需大于${ withdrawalSetting.minimal / 100 }元`);
        // return;
      }

      // if ((!withdrawalSetting || (withdrawalSetting && !withdrawalSetting.minimal)) &&  unsettledMoney > 0 && unsettledMoney < 1) {
      // 	toast('可提现金额需大于1元')
      // 	return
      // };

      this.$modal.loading();
      let res = await getWithdrawingList({ id: this.manageId });
      this.$modal.closeLoading();
      if ((res.data.processes.total || this.hasWithdrawing) && !this.taskId) {
        toast("您有正在提现中的流程哦");
        return;
      }

      if (!withdrawalSetting || withdrawalSetting && !withdrawalSetting.allowMode) {
        toast("请联系BD为您开通提现");
        return;
      }

      // 有需要验证金额的时候，先进到验证金额中
      if (this.taskId) {
        uni.navigateTo({
          url: `/subpkgShop/withdraw-verify-account/withdraw-verify-account?id=${this.taskId}`,
        });
        return;
      }

      uni.navigateTo({
        url: `/subpkgShop/withdraw-apply/withdraw-apply?receivableMoney=${unsettledMoney}&offlineMoney=${-settledMoney}`,
      });
    },
    showPopup({ type, popupRef }) {
      this.$refs[popupRef].open(type);
    },
    toPaymentDetail() {
      if (
          !this.generalSetting.billShow && this.generalSetting.billMonthShow
      ) {
        uni.navigateTo({
          url: "/subpkgShop/bill-month/bill-month",
        });
      } else if (this.generalSetting.billShow) {
        uni.navigateTo({
          url: "/subpkgShop/day-profit/day-profit",
        });
      }
    },
    async getVerifyAccount() {
      let taskList = await this.fetchVerifyMoneyList();
      let taskListId = [];
      taskList.forEach((task) => {
        taskListId.push(task.accountId + "");
      });

      let list = this.accountsList;
      let taskId = "";
      list.forEach((item) => {
        if (item.state != "VALID") {
          let taskKey = -1;
          for(let i = 0; i < taskListId.length; i++) {
            if (taskListId[i] == item.id) {
              taskKey = i;
            }
          }
          item.isMoneyVerify = taskKey > -1 ? 1 : 0;
          taskId = taskKey > -1 ? taskList[taskKey].taskId : "";
        }
      });
      return taskId;
    },
    async fetchVerifyMoneyList() {
      let res = await getVerifyMoneyTask(this.manageId);

      return formatVariables(res.data.tasks.items);
    },
    toWithdrawRecord() {
      uni.navigateTo({
        url: "/subpkgShop/withdraw-record/withdraw-record",
      });
    },
    toShopManage() {
      uni.navigateTo({
        url: "/subpkgShop/shop-manage/shop-manage",
      });
    },
    toAccountAndRole() {
      uni.navigateTo({
        url: "/pages/my/accountAndRole/accountAndRole",
      });
    },
    toStorekeeperManage() {
      uni.navigateTo({
        url: "/subpkgShop/storekeeper-list/storekeeper-list",
      });
    },
    toKAVip() {
      uni.navigateTo({
        url: "/subpkgShop/vip-record/vip-record",
      });
    },
    async modifyPassword() {
      let { password, confirmPassword } = this;
      if (!password || !confirmPassword) {
        toast("请填写密码");
        return;
      }

      if (password != confirmPassword) {
        toast("两次密码不相同");
        return;
      }

      let res = await modifyPassword({ password });
      this.$refs.modifyPasswordPopup.close();
      this.$store.commit("SET_NEEDRESETPASSWORD", false);
      toast("修改成功");
    },
    cancelModify() {
      this.$refs.modifyPasswordPopup.close();
    },
    async storekeeperLoginHandler() {
      if (this.merchantId) {
        return this.$tab.reLaunch(`/subpkg/pages/activate-share/activate-share?id=${this.merchantId}`)
      }
      const hasPrivateDomainSetting = await this.checkHasPrivateDomainSetting()
      if (hasPrivateDomainSetting) {
        this.$tab.reLaunch("/subpkgPrivateDomain/pages/storekeeper-private-domain/storekeeper-private-domain")
      } else {
        this.$tab.reLaunch("/pages/index-shop")
      }
    },
    async checkHasPrivateDomainSetting() {
      const {data} = await findWeComConfigReq({orgId:+this.$store.state.user.manageId })
      return data.result.length
    }
  },
};
</script>

<style lang="scss" scoped>
.notification-badge {
  position: absolute;
  width: 55px;
  height: 55px;
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 16px;
  cursor: move;
  z-index: 9999; /* 确保悬浮窗始终在最上面 */
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.16);
}
.notice-icon{
  width: 32px;
  height: 32px;
}

.count {
  position: absolute;
  display: flex;
  top: 8px;
  right: 6px;
  width: 18px;
  height: 18px;
  background-color: red;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 15px;
  text-align: center;
}
/deep/ .uni-popup__wrapper {
  border-radius: 10px;
}
/deep/ .support-container .uni-popup__wrapper {
  width: 750rpx;
  border-radius: 10px;
  overflow: hidden;
}
/deep/ .vue-ref {
  padding-bottom: 0 !important;
}

::v-deep .uni-select {
  border: none !important;
  height: 18px !important;
}
::v-deep .uni-select__input-box {
  height: 18px !important;
}
::v-deep .change-role-box .uni-select__selector-scroll {
  max-height: 220px !important;
}
::v-deep .uni-select__selector-scroll {
  max-height: 220px !important;
}
::v-deep .uni-select__input-text {
  margin-right: 8rpx;
  color: rgba(45, 45, 45, 0.9) !important;
  font-size: 24rpx;
  text-align: right;
}
::v-deep .change-role-box .uni-select__input-text {
  color: rgba(45, 45, 45, 0.9) !important;
  font-size: 24rpx;
  text-align: left;
  max-width: 480rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #fff;
}
::v-deep .uni-popper__arrow {
  display: none;
}
::v-deep .uni-select__selector-item {
  justify-content: center;
  border-bottom: 1px solid rgba(45, 45, 45, 0.1) !important;
  padding: 0 !important;
  margin: 0 20rpx !important;
  letter-spacing: 2rpx;
  &:nth-last-child(1) {
    border-bottom: none !important;
  }
}
::v-deep .uni-icons {
  // width: 24rpx;
  // height: 16rpx;
  // background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/storekeeper-list-slide-down.png');
  // background-size: 24rpx 16rpx;
  // color: transparent !important;
  // background-repeat: no-repeat;
  // background-position: center center;
}
.container {
  padding-bottom: $uni-support-height;
}
.bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  z-index: -1;
}
.banner-bg {
  background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/shop-banner.png");
  background-repeat: no-repeat;
  background-size: 100% 223px;
  background-position: left var(--nav-height);
}

.select-role-box {
  max-width: 200px;
  .change-role-text {
    max-width: 480rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: rgba(45, 45, 45, 0.9);
    .role-text {
      padding-left: 24rpx;
      color: rgba(45, 45, 45, 0.9);
      font-size: 24rpx;
    }
  }
}

.money-box {
  margin-top: -20px;
  margin-bottom: 4px;
  .money-box-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 26px;
    padding: 0 60rpx;
    .money-item {
      width: 268rpx;
      height: 65px;
      padding-top: 8px;
      background: rgba(255, 234, 218, 0.66);
      border-radius: 6px;
      text-align: center;
      box-sizing: border-box;
      .money-text {
        margin-bottom: 1px;
        font-size: 10px;
        line-height: 14px;
        color: #1b1b1b;
      }
      .money-value {
        line-height: 34px;
        .money-value-unit {
          font-size: 12px;
          color: #1b1b1b;
        }
        .premoney-text {
          color: #e36b10;
        }
        .money-value-text {
          font-size: 24px;
          color: 24px;
        }
      }
    }
  }
  .take-btn {
    width: 356rpx;
    height: 36px;
    margin: 21px auto 0;
    background: linear-gradient(62deg, #ffb55f 0%, #fd8f3c 100%, #fd9342 100%);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
    border-radius: 21px 21px 21px 21px;
    text-align: center;
    line-height: 36px;
    letter-spacing: 5px;
    color: #fff;
    font-size: 14px;
  }
  .take-record-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    font-size: 10px;
    color: rgba(27, 27, 27, 0.9);
    text-align: center;
    .take-record-icon {
      display: block;
      width: 4px;
      height: 7px;
      margin-left: 10px;
      background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-right-arrow.png");
      background-size: 4px 7px;
    }
  }
}

.day-data {
  .data-box-row {
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    margin-bottom: 20rpx;
    &:nth-last-child(1) {
      margin-bottom: 0;
    }
  }
  .day-data-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    padding-left: 60rpx;
    font-size: 28rpx;
    color: #1b1b1b;
    line-height: 40rpx;
  }
  .data-info-icon {
    display: block;
    width: 12px;
    height: 12px;
    margin-left: 10px;
    background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-icon-black.png");
    background-size: 12px 12px;
  }
  .data-box {
    .data-box-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 228rpx;
      height: 112px;
      padding-top: 14px;
      background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-bg.png");
      background-size: 228rpx 112px;
      box-sizing: border-box;
      .data-item-icon {
        width: 22px;
        height: 22px;
        margin-bottom: 6px;
        background-size: 22px 22px;
      }
      .data-item-icon1 {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-icon1.png");
      }
      .data-item-icon2 {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-icon2.png");
      }
      .data-item-icon3 {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-icon3.png");
      }
      .data-item-icon4 {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-icon4.png");
      }
      .data-item-icon5 {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-icon5.png");
      }
      .data-item-icon6 {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-icon6.png");
      }
      .data-item-content {
        margin-bottom: 11px;
        color: #1b1b1b;
        font-size: 14px;
        line-height: 20px;
      }
      .data-item-title {
        color: #1b1b1b;
        font-size: 10px;
        line-height: 14px;
      }
    }
  }
}

.chart-box {
  margin: 20rpx 24rpx 28rpx;
  padding: 16rpx 36rpx 56rpx;
  background-color: #fff;
  box-shadow: 0px 2px 4px 0px rgba(72, 35, 5, 0.26);
  border-radius: 24rpx;
  .data-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .data-title-text {
      color: #1b1b1b;
      font-size: 28rpx;
      line-height: 32rpx;
      font-weight: 500;
    }
    .chart-legend {
      display: flex;
      font-size: 20rpx;
      line-height: 24rpx;
      .sum-order,
      .vali-order {
        display: flex;
        align-items: center;
        .sum-order-circle,
        .vali-order-circle {
          width: 12rpx;
          height: 12rpx;
          margin-right: 6rpx;
          background-color: #fb6e15;
          border-radius: 12rpx;
        }
        .vali-order-circle {
          background-color: #fb6e15;
        }
      }
      .sum-order {
        margin-right: 48rpx;
      }
    }
  }
}

.manage-box {
  margin: 0 24rpx 16px;
  padding: 0 16px 0;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0px 1px 4px 0px rgba(72, 35, 5, 0.22);
  .manage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 17px 0;
    border-bottom: 1px solid rgba(21, 21, 21, 0.07);
    color: #1b1b1b;
    .manage-item-left {
      display: flex;
      align-items: center;
      font-size: 16px;
      .manage-item-icon {
        display: inline-block;
        width: 34rpx;
        height: 28rpx;
        margin-right: 8px;
        background-size: 34rpx 28rpx;
      }
      .shop-manage-icon {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/shop-icon.png");
      }
      .my-item-icon {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/my-icon.png");
      }
      .vip-item-icon {
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-item-icon2.png");
      }
    }
    .manage-item-right {
      display: flex;
      align-items: center;
      .manage-item-right-text {
        margin-right: 26rpx;
        color: #a1a1a1;
        font-size: 28rpx;
      }
    }
  }
  .shop-manage {
    border-bottom: none;
  }

  .more-icon {
    display: block;
    width: 8px;
    height: 14px;
    background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/index-shop-right-arrow.png");
    background-size: 8px 14px;
  }
}

.popup-box {
  padding: 20px 20px 18px;
  .pre-money-tip-title {
    margin-bottom: 13px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #151515;
  }

  .tip-content {
    text-align: center;
    font-size: 14px;
  }
  .pre-money-tip-content {
    margin-bottom: 10px;
    color: #6c6c6c;
    font-size: 14px;
    text-align: left;
  }
  .mon-tip-content,
  .day-tip-content {
    margin-bottom: 30px;
    font-size: 15px;
    color: #6c6c6c;
    line-height: 21px;
  }
  .pre-money-tip-block {
    padding: 8px 10px;
    margin-bottom: 18px;
    background-color: #f5f5f5;
    border-radius: 4px;
    text-align: left;
    color: #6c6c6c;
    font-size: 13px;
  }
  .tip-btn {
    width: 220px;
    height: 48px;
    margin: 0 auto;
    line-height: 48px;
    text-align: center;
    background: linear-gradient(87deg, #ff922d 0%, #fb6610 100%);
    box-shadow: 0px 4px 10px 0px rgba(207, 95, 12, 0.35);
    border-radius: 24px;
    color: #fff;
    font-size: 18px;
  }
}
.day-popup-box {
  padding-top: 40px;
}

.modify-popup-box {
  width: 630rpx;
  padding: 28rpx 30rpx;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0px 3px 7px 0px rgba(64, 18, 0, 0.2);
  box-sizing: border-box;
  .popup-title {
    margin-bottom: 24rpx;
    padding-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    letter-spacing: 1px;
    border-bottom: 1px solid rgba(45, 45, 45, 0.1);
  }
  .popup-row {
    font-size: 28rpx;
    line-height: 40rpx;
    border-bottom: 1px solid rgba(45, 45, 45, 0.1);
    .popup-row-title {
      width: 140rpx;
      flex-shrink: 0;
    }
    .popup-row-value {
      flex: 1;
      .password-input {
        height: 90rpx;
        text-align: right;
      }
    }
  }
  .popup-tips {
    margin-top: 16rpx;
    font-size: 24rpx;
    line-height: 34rpx;
    color: rgba(45, 45, 45, 0.6);
    .popup-tips-item {
      margin-bottom: 16rpx;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }
  }
  .btn-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    margin-top: 28rpx;
    .btn {
      width: 200rpx;
      height: 60rpx;
      box-sizing: border-box;
      border-radius: 16rpx;
      text-align: center;
      line-height: 60rpx;
      letter-spacing: 1px;
      font-size: 28rpx;
    }
    .confirm-btn {
      background-color: #ff7222;
      color: #fff;
    }
    .cancel-btn {
      border: 1px solid #ff7222;
      color: #ff7222;
    }
  }

}
</style>
