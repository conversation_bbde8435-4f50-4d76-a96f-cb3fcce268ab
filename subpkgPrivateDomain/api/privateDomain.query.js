export const findWeComConfigQuery = `
    #graphql
    query findWeComConfig(
        $filter:WeComConfigFilter){
        result: findWeComConfig(
            filter: $filter
        ){
            id
            configId
            configType
            userCardId
            userCard {
                freeTime
            }
            qrCode
            welComeInfo {
                welComeMsg
                images
            }
        }
    }
`

export const findWeComPrivateDomainStatQuery = `
    #graphql
    query findWeComPrivateDomainStat($orgId:Int){
        result: findWeComPrivateDomainStat(orgId: $orgId){
            countGroupChatMember
            countCurrentMonthGroupChatMember
            groupChatMemberTrend {
                count
                date
            }
            members {
                name
                joinTime
            }
        }
    }
`

export const findExtUserListQuery = `
#graphql
query findExtUserList($orgId: ID!){
    result: findExtUserList(orgId: $orgId)
}
`

export const findExtUserDetailQuery = `
    #graphql
    query findExtUserDetails($extUserIds:[String]!){
        result: findExtUserDetails(extUserIds: $extUserIds){
            externalContact {
                externalUserId
                name
                avatar
                type
                gender
            }
			followedUsers {
				userId
				createTime
                
            }
        }
    }
`

export const findStoreKeeperDetailByIdQuery = `
    #graphql
    query findOrgDetailById($id: ID!) {
        result: findOrgDetailById(id: $id) {
            ... on OrgStoreKeeperDetailDTO {
                responsiblePerson {
                    wxCpUserId
                    phone
                }
                id
                name
                ally {
                    id
                }
            }
        }
    }
`

export const findOrgDetailByIdQuery = `
    #graphql 
    query findOrgDetailById($id: ID!) {
        result: findOrgDetailById(id: $id) {
            ... on OrgStoreKeeperDetailDTO {
                responsiblePerson {
                    wxCpUserId
                }
                id
                name
                framework (scope : STOREKEEPER) {
                    seriesData(seriesRange : LAST_7_DAYS) {
                        dataset {
                            countOrder
                        }
                        date
                    }
                    allOrder: dateRange(range: ALL) {
                        dataset {
                            countOrder
                        }
                    }
                    thisMonthOrder: dateRange(range: THIS_MONTH) {
                        dataset {
                            countOrder
                        }
                    }
                    lastMonthOrder: dateRange(range: LAST_MONTH){
                        dataset {
                            countOrder
                            
                        }
                    }
                }
            }
        }
    }
`

export const findPrivateDomainQrCodeQuery = `
    #graphql
    query findPrivateDomainQrCode($orgId:ID!){
        result: findPrivateDomainQrCode(orgId: $orgId)
    }
`

export const findGroupChatListQuery = `
    #graphql
    query findGroupChatList($orgId: ID!){
        result: findGroupChatList(orgId:$orgId){
            chatId
            name
            owner
            createTime
            memberList{
                userId
                type
                name
                
            }
        }
    }
`

export const findWxCpUserIdQuery = `
    #graphql
    query findOrgDetailById($id: ID!) {
        result: findOrgDetailById(id: $id) {
            responsiblePerson{
                wxCpUserId
            }
        }
    }
`
