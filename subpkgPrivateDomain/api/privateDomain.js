import {
  findExtUserDetailQuery,
  findExtUserListQuery, findGroupChatListQuery,
  findOrgDetailByIdQuery, findPrivateDomainQrCodeQuery, findStoreKeeperDetailByIdQuery,
  findWeComConfigQuery,
  findWeComPrivateDomainStatQuery, findWxCpUserIdQuery
} from './privateDomain.query'
import { getRequest, graphqlRequest, postRequest } from "@/utils/requestUtil";

const BASE_URL = '/core/weCom'

export const findWeComConfigReq = (filter) => graphqlRequest(findWeComConfigQuery, { filter })
export const findWeComPrivateDomainStatReq = orgId => graphqlRequest(findWeComPrivateDomainStatQuery, { orgId })
export const findExtUserListReq = orgId => graphqlRequest(findExtUserListQuery, { orgId })
export const findExtUserDetailReq = extUserIds => graphqlRequest(findExtUserDetailQuery, { extUserIds })
export const findOrgDetailByIdReq = id => graphqlRequest(findOrgDetailByIdQuery, { id })
export const findPrivateDomainReq = orgId => graphqlRequest(findPrivateDomainQrCodeQuery, { orgId })
export const findGroupChatListReq = orgId => graphqlRequest(findGroupChatListQuery, { orgId })
export const findWxCpUserIdReq = id => graphqlRequest(findWxCpUserIdQuery, { id })
export const findStoreKeeperDetailByIdReq = id => graphqlRequest(findStoreKeeperDetailByIdQuery,{id})

export const createWeComConfigReq = data => postRequest(`${BASE_URL}/config`, data)
export const hasDefaultExtUserReq = () => getRequest(`${BASE_URL}/hasDefaultExtUser`)
export const storekeeperHasPrivateDomain = () => getRequest(`${BASE_URL}/hasPrivateDomain`)
export const createWxCpUserReq = () => postRequest(`${BASE_URL}/addUser`)

export const createWxCpUserV2Req = (orgId) => postRequest(`${BASE_URL}/addUserV2`, { orgId })
