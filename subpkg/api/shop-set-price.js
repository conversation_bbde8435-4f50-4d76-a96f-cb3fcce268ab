import request from "@/utils/request";
import { uploadVerifyDir } from "@/utils/constant";
import { getToday } from "@/utils/function.js";

let today = getToday();

// 设置计费规则
export function setPriceRule(rules) {
	return request({
		url: `/core/setting/direct/BillingConfigPowerbankDto`,
		method: "post",
		params: { orgId: rules.shopId, key: 'BillingConfigPowerbankDto' },
		data: rules,
	});
}

// 商户信息获取
export function getShopInfo({ id, cursor }) {
	cursor = cursor ? `\"${cursor}\"` : null;
	return request({
		url: "/graphql",
		method: "post",
		data: {
			query: `{
				findStoreById(id: ${id},) {
					id,
					name,
					businessHours,
					openingTime,
					closingTime,
					image,
					storeKeeper {
						ally {
							id
						}
					}
					poi {
						province,
						city,
						district,
						address,
						longitude,
						latitude,
					},
					chargeConfig {
						id,
						freeTime,
						billingUnit,
						unitPrice,
						cappedDay,
						cappedAll,
						priceRule,
						forbiddenStartTime,
						forbiddenEndTime,
						rentThreshold
						hasRule
						label
					}
					chargeConfigSetting: setting(key:"BillingConfigPowerbankDto", mode: INHERIT_ROOT) {
						id
						indexId
						timestamp
						key
						object
					}			
				}
			}`,
		},
	});
}