import { getMerchantById, addShop, getOssSTSToken, findCategoryList } from "@/subpkg/api/add-store.js";
import { findTemplateByIdApi } from '@/api/common.js';
import { toast } from "@/utils/common";
import { flattenObject, formatMinUnitToHour } from "@/utils/function";
import AddressParse from "@/utils/wonday-address-parse/dist/zh-address-parse.min.js";
import { getPicture, upload } from "@/utils/tool.js";
import mNav from '@/components/m-nav/m-nav.vue';

export default {
	data() {
		return {
			merchantId: "",
			merchant: {}, // 商铺信息
			shopName: "",
			region: [],
			address: {
				province: "广东省",
				city: "深圳市",
				district: "南山区",
				address: "",
				longitude: "",
				latitude: "",
			},
			startTime: "09:00",
			endTime: "18:00",
			avatar: "",
			avatarTemp: "",
			hadSave: false,

			chargeConfig: {},	// 门店的计费规则
			categoryId: '',
			categoryList: [],
		};
	},
	filters: {
		formatMinUnitToHour,
	},
  components: {
		mNav,
	},
	async onLoad(option) {
		this.findCategoryList();
		this.merchantId = parseInt(option.merchantId, 10);
		let merchant = await getMerchantById(this.merchantId);
		this.merchant = flattenObject(merchant.data.findStoreKeeperById);
		// let res = await findTemplateByIdApi({ orgId: this.merchantId, key: "BillingConfigPowerbankDto", templateId: this.merchant.chargeConfig.indexId })
		this.chargeConfig = this.merchant.chargeConfig;
	},
	methods: {
		timeChange(e, type) {
			if (this.hadSave)	return;
			let time = e.detail.value;
			this[type] = time;
		},
		chooseLocation() {
			let that = this;
			if (this.hadSave)	return;
			uni.chooseLocation({
				success: (res) => {
					let latitude = res.latitude;
					let longitude = res.longitude;
					that.address.latitude = res.latitude;
					that.address.longitude = res.longitude;

					let result = AddressParse(res.address);
					console.log(res, res.address, result, "地址");
					that.address.province = result.province;
					that.address.city = result.city;
					that.address.district = result.area;
					let address = result.detail
						? result.name + "(" + result.detail + ")"
						: result.name;
					that.address.address = address ? address : res.name;
					that.region = [
						that.address.province,
						that.address.city,
						that.address.district,
					];
				},
				fail: (err) => {
					console.log(err, "获取地理位置失败");
				},
			});
		},
		async uploadPic() {
			if (this.hadSave)	return;
			let uploadImgTemp = await getPicture();
			this.$modal.loading("上传中");
			try {
				let uploadImgNameChunk = uploadImgTemp.split("/");
				let filePath = uploadImgNameChunk[uploadImgNameChunk.length - 1];
				let ossSTSToken = await getOssSTSToken();
				let { policy, accessid, signature, dir, host } =
					ossSTSToken.data.getOssSignature;

				let extraData = {
					host,
					key: dir + filePath,
					policy,
					signature,
					OSSAccessKeyId: accessid,
					"x-oss-content-type": "image/jpg",
					tempPath: uploadImgTemp,
					filePath: uploadImgNameChunk[uploadImgNameChunk.length - 1],
				};

				console.log(extraData, "extraData");
				let uploadRes = await upload(extraData);
				// 因为上传文件后，并不能立即访问到资源，所以展示临时地址
				this.avatar = host + "/" + dir + extraData.filePath;
				this.avatarTemp = uploadImgTemp;

				this.$modal.closeLoading();
			} catch (e) {
				console.log(e);
				toast("上传失败");
			}
		},
		async findCategoryList() {
			let res = await findCategoryList();
			let list = res.data.findCategory;
	
			let categoryTwo = [];
			list[0].children.forEach(item => {
				categoryTwo.push( {
					text: item.text,
					value: item.value
				})
			})
			this.categoryList = [list, categoryTwo];
		},
		changeColumn(e) {
			const { detail } = e;
			if (detail.column != 0) return;

			let categoryTwo = [];
			this.categoryList[0][detail.value].children.forEach(item => {
				categoryTwo.push( {
					text: item.text,
					value: item.value
				})
			});
			
			this.$set(this.categoryList, 1, categoryTwo);
		},
		changeCategory(e) {
			this.categoryId = e.detail.value;
		},
		del() {
			if (this.hadSave)	return;
			this.avatar=''
		},
		async save() {
			let {
				shopName,
				address,
				startTime,
				endTime,
				merchantId,
				avatar,
				categoryList,
				categoryId
			} = this;
			if (!shopName || !address.address) {
				toast("请填写必要信息");
				return;
			}
			let params = {
				name: shopName,
				poi: address,
				openingTime: startTime,
				closingTime: endTime,
				storeKeeperId: merchantId,
				image: avatar,
				categoryId: categoryId ? categoryList[1][categoryId[1]].value : null,
			};

			uni.showLoading({
				title: "加载中...",
				mask: true,
			});
			return await addShop(params);
		},
		async addShopDetail() {
			let res = await this.save();
			uni.hideLoading();
			if (res.code == 200) {
				this.hadSave = true;
				uni.navigateBack();
			}
		},
		back() {
			uni.navigateBack();
		},
		async continueAdd() {
			let res = await this.save();
			uni.hideLoading();
			console.log(res)
			if (res.code == 200)
				this.reset();
		},
		reset() {
			this.hadSave = false;
			this.shopName = '';
			this.address = {
				province: "广东省",
				city: "深圳市",
				district: "南山区",
				address: "",
				longitude: "",
				latitude: "",
			};
			this.avatar = "";
			this.avatarTemp = "";
		},
	},
};
