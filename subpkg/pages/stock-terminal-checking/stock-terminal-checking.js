import { getTerminalDiagnostic, resetNormalMode, resetSimCard, resetTerminal, checkingPing } from '@/subpkg/api/stock-terminal-checking';

export default {
  data() {
		return {
      sn: '',
      iotInfo: {},
      cardInfo: {},
      signalData: {},
			terminalReport: null,
      lineOpts: {
				color: ["#F97A1B"],
				padding: [35, 30, 0, 15],
				legend: {
					show: true,
				},
				xAxis: {
					disableGrid: true,
					axisLine: {
						show: false,
					},
					fontSize: 10,
				},
				yAxis: {
					disabled: true,
					disableGrid: true,
					data: [
            {
              min: -120
            }
          ]
				},
				extra: {
          line: {
            type: "curve",
          },
          mount: {
            type: "triangle",
            widthRatio: 1,
            borderWidth: 2,
            linearType: "custom"
          },
          markLine: {
            type: "solid",
            dashLength: 4,
            data: [
							{
                value: -90,
								labelFontSize: 11,
                lineColor: "rgba(35, 184, 94, .4)",
                showLabel: true,
                labelAlign: "right",
                labelOffsetX: 0,
                labelOffsetY: 0,
                labelText: "极好",
                labelFontColor: "#666666",
                labelBgColor: "#fff",
                labelBgOpacity: 0.8
              },
							{
                value: -96,
								labelFontSize: 11,
                lineColor: "rgba(35, 184, 94, .3)",
                showLabel: true,
                labelAlign: "right",
                labelOffsetX: 0,
                labelOffsetY: 0,
                labelText: "一般",
                labelFontColor: "#666666",
                labelBgColor: "#fff",
                labelBgOpacity: 0.8
              },
              {
                value: -101,
								labelFontSize: 11,
                lineColor: "rgba(249, 122, 27, .3)",
                showLabel: true,
                labelAlign: "right",
                labelOffsetX: 0,
                labelOffsetY: 0,
                labelText: "较差",
                labelFontColor: "#666666",
                labelBgColor: "#fff",
                labelBgOpacity: 0.8
              },
							{
                value: -110,
								labelFontSize: 11,
                lineColor: "rgba(255, 0, 0, .3)",
                showLabel: true,
                labelAlign: "right",
                labelOffsetX: 0,
                labelOffsetY: 0,
                labelText: "极差",
                labelFontColor: "#666666",
                labelBgColor: "#fff",
                labelBgOpacity: 0.8
              },
            ]
          }
        }
			},
      delay: 0,
    }
  },
  async onLoad(option) {
    let sn = option.sn;
		this.sn = sn;
    this.fetchData();
    
  },
  methods: {
    async fetchData() {
      this.$modal.loading("加载中");
      let res = await getTerminalDiagnostic(this.sn);
      this.$modal.closeLoading();
      let detail = res.data.findTerminalDiagnostic;

      this.iotInfo = detail.baseInfo;
      this.cardInfo = detail.cardInfo;
			this.terminalReport = detail.terminalReport;
      this.formatSignalData(detail.terminalReport)
    },
    formatSignalData(data) {
			data = data.slice(0, 7);
      if (data && data.length) {
        let monKey = [];
        let monValue = [];
        data.forEach((item) => {
          monKey.unshift(item.reportTime.slice(11, 16));
          monValue.unshift(item.networkStrength ? item.networkStrength : 0);
        });

        this.initLineChart(monKey, monValue,);
      }
			
		},
    initLineChart(monKey, monValue,) {
		// 	let res = {
		// 		series: [
		// 			{
		// 				data: [{"name":"一班","value":82},{"name":"二班","value":63},{"name":"三班","value":86},{"name":"四班","value":65},{"name":"五班","value":79}]
		// 			}
		// 		]
		// 	};
		// this.signalData = JSON.parse(JSON.stringify(res));
		// 	return;
			let chartData = {
				categories: monKey,
				series: [
					{
						name: "信号走势",
						data: monValue,
						textOffset: -5,
					},
				],
			};
			this.signalData = JSON.parse(JSON.stringify(chartData));
		},
    async resetNormalMode() {
      let { sn } = this;
      this.$modal.loading('设置中');
      let res = await resetNormalMode(sn);

      this.$modal.closeLoading();
      this.$modal.msgSuccess('设置成功');
      this.fetchData();
    },
    async resetTerminal() {
      let { sn } = this;

      this.$modal.loading('重启中');
      let res = await resetTerminal(sn);
      this.$modal.closeLoading();
      this.$modal.msgSuccess('重启成功')
    },
    async resetSimCard() {
      let { sn } = this;

      this.$modal.loading('重启中');
      let res = await resetSimCard(sn);
      this.$modal.closeLoading();
      this.$modal.msgSuccess('重启成功');
    },
    async checkingPing() {
      let { sn } = this;

      this.$modal.loading('发送中');
      let res = await checkingPing(sn);
      this.delay = res.data;
      this.$modal.closeLoading();
    },
    clipboard(text) {
			uni.setClipboardData({
				data: text,
				success: () => {
					uni.showToast({
						title: "复制成功",
						icon: "success",
					});
				},
			});
		},
  },
}