import { findAgentById } from "@/subpkg/api/agent-detail";
import {
	hasProfitOperationPermi,
	hasEditAgentPermi,
	hasBdProfitPermi,
} from "@/utils/permissionList.js";
import { toast } from "@/utils/common";
import { orgTypeList } from '@/utils/role.js';
import { getFrameworkDateRange, mixFrameworkCountTerminal, mixFrameworkCountPowerbank} from '@/utils/orgFunction.js';

export default {
	data() {
		return {
			id: "",
			detail: {},
		};
	},
	computed: {
		isToAddAgent() {
			return this.$store.state.agent.isToAddAgent;
		},
		isManager() {
			return this.$store.getters.isManager;
		},
		hasProfitOperationPermi,
		hasEditAgentPermi,
		hasBdProfitPermi,
		orgTypeList,
	},
	onLoad: async function (option) {
		this.id = parseInt(option.id, 10);
		this.fetchList();
	},
	async onShow() {
		if (!this.isToAddAgent) return;

		this.fetchList();
	},
	methods: {
		async fetchList() {
			this.$modal.loading("加载中");
			let res = await findAgentById({orgId: this.id});
			this.detail = this.formatDetail(res.data.findOrgDetailById);
			this.$modal.closeLoading();
		},
    formatDetail(detail) {
      detail.statisticTotal = getFrameworkDateRange(detail.framework, 'current');
      detail.countTerminal = mixFrameworkCountTerminal(detail.framework);
      detail.countPowerbank = mixFrameworkCountPowerbank(detail.framework);

			return detail;
		},
		toEdit(item) {
			if (!this.hasEditAgentPermi) {
				toast("您没有权限修改哦");
				return;
			}
			uni.navigateTo({
				url: `/subpkg/pages/agent-edit/agent-edit?id=${this.id}`,
			});
		},
		toProfitBill() {
			uni.navigateTo({
				url: `/subpkg/pages/profit-bill-all/profit-bill-all?agentId=${this.id}&isAll=1`,
			})
		},
		toStoreKeeperList() {
			uni.navigateTo({
				url: `/subpkg/pages/storekeeper-list/storekeeper-list?agentId=${this.id}`,
			});
		},
		toStock(type) {
			uni.navigateTo({
				url: `/subpkg/pages/stock/stock?agentId=${this.id}&tab=${type}`,
			});
		},
		toSetPermission() {
			uni.navigateTo({
				url: `/subpkgProfit/pages/set-permission/set-permission?id=${this.id}`,
			});
		},
		calPowerbankAllRatio(statistic) {
			if (!statistic.countAll) {
				return "0";
			}
			if (!statistic.countEquity) {
				return "--";
			}
			return (
				((statistic.countAll / statistic.countEquity) * 100).toFixed(1) + "%"
			);
		},
	},
};