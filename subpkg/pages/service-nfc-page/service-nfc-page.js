import { getPowerbankBySn } from '@/subpkg/api/service-shop';

export default {
  data() {
    return {
      nfc: null,
			sn: '',
			list: [],
			tableList: [],
    };
  },
  unload() {
    if (this.nfc) {
			this.mifareClassic.close();
			this.mifareClassic = null;
			this.nfc.stopDiscovery();
			this.nfc = null;
		}
  },
	onUnload() {
		this.clearNfc();
	},
	watch: {
		async sn(newVal, oldVal) {
			if (newVal.length === 22) {
				this.addPowerbankSn(newVal);
			} else if (newVal.length > 22) {
				uni.showToast({
					icon: "error",
					title: "SN码为22位",
					duration: 1000,
				});
			}
		},
	},
  methods: {
		toPowerbankDetail(item, key) {
			uni.navigateTo({
				url: `/subpkg/pages/stock-powerbank-detail/stock-powerbank-detail?sn=${item.sn}`,
			});
		},
		delPowerbank(item, key) {
			console.log(key, 'key')
			this.list.splice(key, 1);
			this.tableList.splice(key, 1);
		},
		async addPowerbankSn(sn) {
			let res = await getPowerbankBySn(sn);

			if (res.data.findPowerbankById && res.data.findPowerbankById.sn) {
				if (this.list.indexOf(sn) > -1) {
					uni.showToast({
						icon: "error",
						title: "SN码已存在",
						duration: 1000,
					});
				} else {
					this.list.unshift(sn);
					this.tableList.unshift(res.data.findPowerbankById)
				}
			} else {
				uni.showToast({
					icon: "error",
					title: "未找到该设备",
					duration: 1000,
				});
			}
		},
		clipboard() {
			let { list } = this;
			console.log(1234)
			uni.setClipboardData({
				data: list.join('\n'),
				success: () => {
					uni.showToast({
						title: "复制成功",
						icon: "success",
					});
				},
				fail: (err) => {
					console.log(err)
					uni.showToast({
						title: "复制失败",
						icon: "none",
					});
				},
			});
		},
    nfcRead() {
			this.nfc = wx.getNFCAdapter();
			let _this = this;

			if (!this.nfc) return;
			this.nfc.startDiscovery({
				success(res) {
					_this.nfc.onDiscovered((res) => {
						if (_this.nfc.tech.nfcA) {
							let mifareClassic = _this.nfc.getNfcA();
							_this.mifareClassic = mifareClassic;
							mifareClassic.connect({
								success(res) {
									let str = [0x1b, 0x55, 0xaa, 0x5a, 0x5a]; // 0x1B 密码指令
									let NFCdata = new Uint8Array(str).buffer;
									mifareClassic.transceive({
										data: NFCdata,
										success: function (res) {
											// 第一次返回pack，可以判断是否对应
											_this.powerbankSn = "";
											console.log("发送数据并解密成功, 接收数据如下:", res);
											let str = [0x3a, 0x0a, 0x10];
											mifareClassic.transceive({
												// 此时返回真正的数据;
												data: new Uint8Array(str).buffer,
												success: function (res) {
													_this.readNfcInfo(res);
												},
												fail: function (err) {
													wx.showToast({
														title: "校验失败,请重试!",
														icon: "none",
													});
												},
											});
										},
										fail: function (err) {
											wx.showToast({
												title: "校验失败,请重试!",
												icon: "none",
											});
										},
									});
								},
								fail(err) {
									console.log(err, "err");
									// wx.showToast({
									//   title: "NFC连接失败!",
									//   icon: "none",
									// });
								},
							});
						}
					});
				},
				fail(err) {
					_this.clearNfc();
					_this.nfcErrorToast(err);
				},
			});
		},
		readNfcInfo(res) {
			let { list } = this;
			const data = new Uint8Array(res.data);
			let str = "";

			data.forEach((asciiCode) => {
				let item = String.fromCharCode(asciiCode);

				str += item;
			});
			console.log(str, "str"); // 获取到了宝SN
			str = str.slice(0, 22);	// 获取到的宝SN

			if (list.indexOf(str) > -1) {
				wx.showToast({
					title: "已添加该设备!",
					icon: "none",
				});
				return;
			}
			this.addPowerbankSn(str);
		},
		clearNfc() {
			if (this.nfc) {
				if (this.mifareClassic) {
					this.mifareClassic.close();
					this.mifareClassic = null;
				}
				if (this.nfc) {
					this.nfc.stopDiscovery();
				}
				this.nfc = null;
			}
		},
		nfcErrorToast(err) {
			if (!err.errCode) {
				wx.showToast({
					title: "请检查NFC功能是否正常!",
					icon: "none",
				});
				return;
			}
			switch (err.errCode) {
				case 13000:
					wx.showToast({
						title: "设备不支持NFC!",

						icon: "none",
					});

					break;

				case 13001:
					wx.showToast({
						title: "系统NFC开关未打开!",

						icon: "none",
					});

					break;

				case 13019:
					wx.showToast({
						title: "用户未授权!",

						icon: "none",
					});

					break;

				case 13010:
					wx.showToast({
						title: "未知错误!",

						icon: "none",
					});

					break;
			}
		},
  },
}