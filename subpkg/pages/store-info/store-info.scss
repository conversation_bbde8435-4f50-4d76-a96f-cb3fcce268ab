
::v-deep .uni-select {
  width: 188rpx !important;
	border: 1px solid #FFFFFF !important;
	height: 30px !important;
  border-radius: 15px 2px 2px 15px !important;
  padding-left: 18px !important;
}
::v-deep .uni-select__input-text {
	color: #fff !important;
	font-size: 24rpx !important;
}

::v-deep .uni-select-lay {
	height: 30px !important;
	border-radius: 0 15px 15px 0;
}
::v-deep .uni-select-lay-select {
	border: 1px solid #fff !important;
}

.store-info-contain {
  padding-bottom: 137px;
}

.banner-box {
  width: 100%;
  min-height: 217px;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/store-info-bg-2.png');
  background-size: 100% calc(100% + var(--bg-size));
  background-position: left var(--nav-height);
  background-repeat: no-repeat;
  box-sizing: border-box;
  .banner-box-content {
    min-height: 217px;
    padding-top: 30px;
    box-sizing: border-box;
    .banner-box-header {
      margin: 0 60rpx 24px;
    }
    .avatar-box {
      width: 70px;
      height: 70px;
      margin-right: 24rpx;
      .avatar {
        width: 70px;
        height: 70px;
        border-radius: 50%;
      }
    }
    .store-name {
      justify-content: center;
      font-size: 16px;
      line-height: 22px;
      font-weight: bold;
      color: #fff;
      letter-spacing: 1rpx;
      word-break: break-all;
    }
    .edit-icon {
      flex-shrink: 0;
      display: block;
      width: 30rpx;
      height: 30rpx;
      margin-left: 24rpx;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/white-edit-icon.png');
      background-size: 30rpx 30rpx;
    }
    .store-openingHours {
      font-size: 12px;
      line-height: 17px;
      color: #fff;
    }
    .level-row {
      margin: 5px 0;
      .level {
        width: 78rpx;
        height: 17px;
        margin-right: 16rpx;
        line-height: 15px;
        text-align: center;
        box-sizing: border-box;
        font-size: 12px;
        border-radius: 6rpx;
      }
      .category {
        font-size: 28rpx;
        line-height: 40rpx;
        letter-spacing: 1rpx;
        color: #FFFFFF;
      }
    }
    .level1,
    .level2 {
      background: linear-gradient( 270deg, rgba(64,158,255,0.25) 0%, rgba(64,158,255,0.1) 100%);
      border: 1px solid #409EFF;
      color: #409EFF;
    }
    .level3,
    .level4 {
      background: linear-gradient( 270deg, rgba(0,128,0,0.25) 0%, rgba(0,128,0,0.1) 100%);
      border: 1px solid #008000;
      color: #008000;
    }
    .level5,
    .level6 {
      background: linear-gradient( 270deg, rgba(153,50,205,0.25) 0%, rgba(153,50,205,0.1) 100%);
      border: 1px solid #9932CD;
      color: #9932CD;
    }
    .level7,
    .level8 {
      background: linear-gradient( 270deg, rgba(205,127,50,0.25) 0%, rgba(205,127,50,0.1) 100%);
      border: 1px solid #CD7F32;
      color: #CD7F32;
    }
    .store-info-item {
      margin: 0 60rpx 3px;
      font-size: 12px;
      line-height: 17px;
      color: #fff;
    }
    .map-icon {
      display: inline-block;
      width: 32rpx;
      height: 32rpx;
      margin-left: 22rpx;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/address-white-icon.png');
      background-size: 32rpx 32rpx;
    }
  }
}

.tab {
	display: flex;
	height: 48rpx;
	margin: 30rpx 30rpx 34rpx;
	background-color: #fff;
	border: 1px solid #F1712D;
	border-radius: 24rpx;
	overflow: hidden;
	.tab-item {
		flex: 1;
		text-align: center;
		border-right: 1px solid #F1712D;
		color: #F1712D;
		font-size: 24rpx;
		line-height: 48rpx;
		&:nth-last-child(1) {
			border-right: none;
		}
	}
	.tab-item-active {
		background-color: #F1712D;
		color: #fff;
	}
}

.store-chart-box {
  height: 542rpx;
  margin: 24rpx 36rpx 0;
  padding-top: 40rpx;
  box-shadow: inset 0px 0px 6px 0px rgba(81,23,0,0.26);
  border-radius: 24rpx;
}


.store-terminal-box {
  .table-box {
    margin: 0 40rpx 0px;
  }
  .device-table-tr {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-size: 24rpx;
    line-height: 34rpx;
    .device-table-th {
      text-align: center;
      font-weight: bold;
    }
    .device-table-td {
      text-align: center;
    }
    .device-table-th-1 {
      width: 310rpx;
      flex-shrink: 0;
      text-align: left;
    }
    .device-table-th-2 {
      width: 220rpx;
    }
    .device-table-th-3 {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center;
      width: 140rpx;
      font-size: 24rpx;
      line-height: 34rpx;
    }
  }
  .store-point {
    margin-left: 20rpx;
    font-size: 24rpx;
    line-height: 28rpx;
    color: rgba(45,45,45,0.6);
  }
  .store-tag {
    .store-tag-item {
      font-size: 24rpx;
      line-height: 34rpx;
      padding: 0 18rpx 2rpx;
      color: rgba(255,101,19,0.9);
      border: 1px solid rgba(255,101,19,0.9);
      border-radius: 8rpx;
    }
    .store-tag-item-active {
      background-color: rgba(255,101,19,0.9);
      color: #fff;
    }
  }
  .device-table-title-tr {
    padding: 20rpx 22rpx 12rpx;
    color: rgba(45,45,45,0.6);
    border-bottom: 1rpx #ebeef5 solid;
  }
  .device-table-row-tr {
    padding: 20rpx 8rpx 22rpx 0;
    margin: 0 22rpx;
    color: rgba(45,45,45,0.9);
    border-bottom: 1rpx #ebeef5 solid;
  }
  .device-table-empty {
    text-align: center;
    font-size: 24px;
    color: #999;
    line-height: 50px;
  }
}

.store-charge-box {
  margin: 0 36rpx;
  .store-charge-header-title {
    margin: 16rpx 0;
    font-size: 28rpx;
    line-height: 40rpx;
    color: rgba(45,45,45,0.9);
  }
  .store-charge-edit {
    width: 24rpx;
    height: 24rpx;
    background-size: 24rpx 24rpx;
    background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/storekeeper-info-charge-edit.png');
  }
  .store-charge-content {
    padding: 32rpx 34rpx;
    border: 1px solid rgba(45,45,45,0.1);
    border-radius: 16rpx;
    .store-charge-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      font-size: 24rpx;
      line-height: 34rpx;
      color: rgba(45,45,45,0.9);
      .store-charge-title {
        margin-top: 20rpx;
        color: rgba(45,45,45,0.5);
      }
    }
    .store-charge-line {
      width: 2rpx;
      height: 70rpx;
      background: rgba(45,45,45,0.06);
    }
  }
  .store-charge-footer {
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #ccc;
    .store-charge-item {
      flex-direction: row;
    }
    .store-charge-forbidden-item {
      margin-top: 10rpx;
    }
  }
  .store-charge-content-empty {
    justify-content: center;
    font-size: 26rpx;
    letter-spacing: 4rpx;
    color: #f1712d;
  }
  .charge-config-box {
		display: flex;
		width: 100%;
		height: 55px;
		background: #FAFAFA;
		border-radius: 4px 4px 4px 4px;
		border: 1px solid #EBE9E7;
		.charge-config-item {
			position: relative;
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-self: center;
			padding-top: 8px;
			.charge-config-price {
				margin-bottom: 2px;
				background: linear-gradient(4.2688682312579694e-7deg, #F9983E 0%, #FD5935 100%);
				background-clip: text;
				color: transparent;
				font-size: 14px;
				line-height: 20px;
				font-weight: bold;
				letter-spacing: 1px;
			}
			.charge-config-time {
				font-size: 12px;
				line-height: 17px;
				color: #979494;
				letter-spacing: 1px;
			}
		}
		.charge-config-item::after {
			display: block;
			content: "";
			position: absolute;
			right: 0;
			top: 22px;
			width: 1px;
			height: 12px;
			background: #EBE9E7;
		}
	}
}

.list-end {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  color: rgba(45,45,45,0.66);
  font-size: 24rpx;
  line-height: 34rpx;
  text-align: center;
  .list-end-content {
    margin: 0 38rpx;
  }
  .list-end-line {
    height: 1px;
    width: 80rpx;
    background-color: rgba(45,45,45,0.2);;
  }
}

.fixed-bottom {
  position: fixed;
  left: 0;
  bottom: 0;
  height: 110px;
  width: 100%;
  padding-top: 48rpx;
  background: #FFFFFF;
  box-shadow: 0px -1px 3px 0px rgba(57,32,11,0.2);
  border-radius: 12px 12px 0px 0px;
  box-sizing: border-box;
  display: flex;
  gap: 20rpx;
  .activate-btn {
    width: 335rpx;
    height: 84rpx;
    margin: 0 auto;
    line-height: 84rpx;
    text-align: center;
    font-size: 24rpx;
    letter-spacing: 2rpx;
  }
}

.edit-popup-box {
	padding: 40rpx 48rpx 36rpx;
	border-radius: 8px;
  background-color: #fff;

	.edit-popup-title {
		margin-bottom: 40rpx;
		text-align: center;
		font-size: 32rpx;
		color: #151515;
		font-weight: 600;
	}
	.modify-avatar-box {
		width: 60px;
		height: 60px;
		margin: 20px auto;
		.edit-avatar {
			width: 100%;
			height: 100%;
			border-radius: 6px;
		}
	}
	.edit-row {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		font-size: 28rpx;
		.edit-name {
			flex-shrink: 0;
			color: #6C6C6C;
		}

		.region-select {

			.region-content {
				width: 460rpx;
				overflow: hidden;
				white-space: nowrap;
			}
		}
		
		.input-element {
			flex: 1;
			//display: flex;
			//align-items: center;
		}
		.name-input {
			width: 438rpx;
			height: 70rpx;
			padding: 0 8rpx;
			border-bottom: 1px solid rgba(21,21,21,0.07);
			box-sizing: border-box;
		}
		.region-select {
			height: 70rpx;
      padding-left: 8rpx;
			line-height: 70rpx;
			border-bottom: 1px solid rgba(21,21,21,0.07);
		}
		.address-input {
			height: 70rpx;
			padding-left: 8rpx;
			border-bottom: 1px solid rgba(21,21,21,0.07);
		}
		.time-input {
			height: 70rpx;
			width: 104rpx;
			line-height: 70rpx;
			border-bottom: 1px solid rgba(21,21,21,0.07);
			text-align: center;
		}
    .add-row-right {
      flex: 1;
      padding-left: 8rpx;
      height: 70rpx;
      line-height: 70rpx;
			border-bottom: 1px solid rgba(21,21,21,0.07);
    }
		.time-split-icon {
			margin: 0 24rpx
		}
	}
	.district-row {
		overflow: hidden;
	}
	.btn-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 40rpx;
    width: 544rpx;
		.save-btn,
		.cancel-btn {
			width: 220rpx;
			height: 70rpx;
			text-align: center;
			line-height: 70rpx;
			border-radius: 48rpx;
		}
		.save-btn {
			color: #fff;
			background: linear-gradient(87deg, #FF922D 0%, #FB6610 100%);
			box-shadow: 0px 4px 10px 0px rgba(207,95,12,0.35);
		}
		.cancel-btn {
			border: 1px solid rgba(21,21,21,0.13);
		}
	}
}