<template>
	<view>
		<view class="bill-detail">
			<view class="banner">
				<view class="banner-store">{{ detail.rentStore.name }}</view>
				<view class="">
					<view class="banner-item banner-order-item">
						<view class="banner-item-left banner-order-item-left">
							<image
								src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/profit-bill-detail-order-icon.png"
								class="banner-item-icon"
							></image>
						</view>
						<view class="banner-item-right banner-order-item-right">
							<view class="banner-item-title">订单号</view>
							<view class="banner-item-value">
								<text>{{ detail.sn }}</text>
								<text class="clip-btn" @click="clipboard">复制</text>
							</view>
						</view>
					</view>
					<view class="banner-item banner-profit-item">
						<view class="banner-item-left banner-profit-item-left">
							<image
								src="https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/profit-bill-detail-profit-icon.png"
								class="banner-item-icon"
							></image>
						</view>
						<view class="banner-item-right banner-profit-item-right">
							<view class="banner-item-title">订单收益</view>
							<view
								class="banner-item-value flex-row"
								v-if="detail.stage != 'RENTED' && detail.stage != 'CREATED'"
							>
								<view>
									<text class="banner-money-unit">￥</text>
									<text
										class="banner-money fb-500"
										:class="{'money-del-line-white': detail.refund}"
										v-if="isAgentType && hasProfitOperationPermi"
										>{{
											exchangeDataSet.agentManagerProfit
												? exchangeDataSet.agentManagerProfit / 100
												: 0
										}}</text
									>
									<text
										class="banner-money fb-500"
										:class="{'money-del-line-white': detail.refund}"
										v-if="isAgentType && !hasProfitOperationPermi"
										>{{
											exchangeDataSet.agentEmpProfit
												? exchangeDataSet.agentEmpProfit / 100
												: 0
										}}</text
									>
									<text
										class="banner-money fb-500"
										:class="{'money-del-line-white': detail.refund}"
										v-if="isAlly && hasProfitOperationPermi"
										>{{
											exchangeDataSet.allyManagerProfit
												? exchangeDataSet.allyManagerProfit / 100
												: 0
										}}</text
									>
									<text
										class="banner-money fb-500"
										:class="{'money-del-line-white': detail.refund}"
										v-if="isAlly && !hasProfitOperationPermi"
										>{{
											exchangeDataSet.allyEmpProfit
												? (exchangeDataSet.allyEmpProfit / 100).toFixed(2)
												: 0
										}}</text
									>
								</view>
								<view class="refund-btn" v-if="detail.refund"> 已退款 </view>
							</view>
							<view class="banner-item-rented" v-if="detail.stage == 'RENTED'"
								>租借中</view
							>
							<view class="banner-item-rented" v-if="detail.stage == 'CREATED'"
								>-</view
							>
						</view>
					</view>
				</view>
			</view>
			<view class="sum-box" @click="toBillOrder">
				<view
					class="sum-item sum-item-header"
					:class="{ 'refund-icon': detail.type === 'REFUND_ORDER' }"
				>
					<view class="sum-item-title"
						>订单实{{ detail.type === "REFUND_ORDER" ? "退" : "收" }}</view
					>
					<view
						class="sum-item-value"
						v-if="detail.stage == 'RENTED' || detail.stage == 'CREATED'"
					>
						<text class="sum-item-value-unit">￥</text>
						<text class="sum-item-value-money">--</text>
					</view>
					<view class="sum-item-value" v-else>
						<text class="sum-item-value-unit">￥</text>
						<text class="sum-item-value-money" :class="{'money-del-line': detail.refund}">{{
							exchangeDataSet.netRevenueManager
								? exchangeDataSet.netRevenueManager / 100
								: 0
						}}</text>
					</view>
				</view>
				<view
					class="sum-item sum-item-revenue"
					:class="{ 'sum-item-revenue-bottom0': !detail.discountCard }"
				>
					<view class="flex-row-between">
						<view class="sum-item-title">主营业务收入</view>
						<view
							class="sum-item-value"
							v-if="detail.stage == 'RENTED' || detail.stage == 'CREATED'"
						>
							<text class="sum-item-unit">￥</text>
							<text class="sum-item-money">--</text>
						</view>
						<view class="sum-item-value" v-else>
							<text class="sum-item-unit">￥</text>
							<text class="sum-item-money" :class="{'money-del-line': detail.refund}">{{
								exchangeDataSet.revenueManager
									? exchangeDataSet.revenueManager / 100
									: 0
							}}</text>
						</view>
					</view>
					<view
						class="sum-item-info"
						v-if="detail && detail.chargeConfig"
					>
						<text v-if="isPowerbankOrder(detail)"
							>{{ detail.chargeConfig.freeTime + "分钟内免费," }}{{ detail.chargeConfig.billingUnit | formatMinUnitToHour
							}}{{ detail.chargeConfig.unitPrice / 100 + "元" }}</text
						>
						<block v-if="isCableOrder(detail)">
							<text v-for="(item, key) in detail.store.orgConfig.cableChargeConfig.object.chargePlanList">{{ item.time / 60 }}小时/{{ item.price/100 }}元<block v-if="key < detail.store.orgConfig.cableChargeConfig.object.chargePlanList.length-1">,</block></text>
						</block>
					</view>
				</view>
				<view
					class="sum-item-line"
					v-if="exchangeDataSet.expenseDiscount"
				></view>
				<view
					class="sum-item sum-item-discount"
					v-if="exchangeDataSet.expenseDiscount"
				>
					<view class="flex-row-between">
						<view class="sum-item-title">VIP优惠</view>
						<view
							class="sum-item-value"
							v-if="detail.stage == 'RENTED' || detail.stage == 'CREATED'"
						>
							<text class="sum-item-unit">￥</text>
							<text class="sum-item-money">--</text>
						</view>
						<view class="sum-item-value" v-else>
							<text class="sum-item-unit">￥</text>
							<text class="sum-item-money" :class="{'money-del-line': detail.refund}">{{
								exchangeDataSet.expenseDiscount
									? exchangeDataSet.expenseDiscount / 100
									: 0
							}}</text>
						</view>
					</view>
					<view class="sum-item-info">{{
						detail.discountCard.discountText.textShort
					}}</view>
				</view>
				<view
					class="sum-item-line"
					v-if="isPurchaseOrder(detail)"
				></view>
				<view
					class="sum-item sum-item-discount"
					v-if="isPurchaseOrder(detail)"
				>
					<view class="flex-row-between">
						<view class="sum-item-title">购买详情</view>
						<view class="sum-item-value">
						</view>
					</view>
					<view class="sum-item-info">
						{{ detail.discountCard && detail.discountCard.text ? detail.discountCard.text.titleText : '-' }}
					</view>
				</view>
			</view>
			<view class="expense-box">
				<view class="expense-item expense-item-top">
					<view class="expense-item-title">服务费</view>
					<view class="sum-item-value" v-if="detail.stage == 'RENTED' && false">
						<text class="expense-item-value-unit">￥</text>
						<text class="expense-item-value-money">--</text>
					</view>
				</view>
				<view class="expense-item" v-if="detail.agent && !isPartner && !isInvestor && !isProvider">
					<view class="expense-item-header flex-row-between">
						<view class="expense-item-title">居间服务费</view>
						<view class="expense-item-value">
							<text class="expense-item-value-unit">￥</text>
							<text :class="{'money-del-line': detail.refund}">{{
								exchangeDataSet.agentEmpProfit
									? exchangeDataSet.agentEmpProfit / 100
									: 0
							}}</text>
						</view>
					</view>
					<view class="expense-item-info flex-row-between">
						<text>{{ detail.agent.companyFullName }}</text>
						<view v-if="!detail.sold && detail.agentContract">
							<text class="storekeeper-mode">{{
								detail.agentContract.modeString.replace("代理商", "代理")
							}}</text>
							<text class="special-text"
								>{{ detail.agentContract.rentShare }}%</text
							>
						</view>
						<view v-if="detail.sold && detail.agentContract">
							<text class="storekeeper-mode"
								>售宝分成{{ detail.agentContract.soldShare / 100 }}元</text
							>
						</view>
					</view>
				</view>
				<view
					class="expense-item"
					v-if="
						detail.agent &&
						hasProfitOperationPermi &&
						exchangeDataSet.expenseChannelIntangible && !isPartner && !isInvestor && !isProvider
					"
				>
					<view class="expense-item-header flex-row-between">
						<view class="expense-item-title">居间现金折扣</view>
						<view class="expense-item-value">
							<text class="expense-item-value-unit">￥</text>
							<text :class="{'money-del-line': detail.refund}">{{
								exchangeDataSet.expenseChannelIntangible
									? exchangeDataSet.expenseChannelIntangible / 100
									: 0
							}}</text>
						</view>
					</view>
				</view>
				<view class="expense-item">
					<view class="expense-item-header flex-row-between">
						<view class="expense-item-title">商户服务费</view>
						<view
							class="expense-item-value"
							v-if="detail.stage == 'RENTED' || detail.stage == 'CREATED'"
						>
							<text class="expense-item-value-unit">￥</text>
							<text class="expense-item-value-money">--</text>
						</view>
						<view v-else>
							<view class="expense-item-value">
								<text class="expense-item-value-unit">￥</text>
								<text class="expense-item-value-money" :class="{'money-del-line': detail.refund}">{{
									exchangeDataSet.storeKeeperActualProfit
										? exchangeDataSet.storeKeeperActualProfit / 100
										: 0
								}}</text>
							</view>
						</view>
					</view>
					<view class="expense-item-info storekeeper-item-info">
						<view>{{ detail.storeKeeper.companyFullName }}</view>
						<view v-if="!detail.sold && detail.storeKeeperContract">
							<text class="storekeeper-mode">{{
								detail.storeKeeperContract.modeString
							}}</text>
							<text class="special-text"
								>{{ detail.storeKeeperContract.rentShare }}%</text
							>
						</view>
						<view v-if="detail.sold && detail.storeKeeperContract">
							<text class="storekeeper-mode"
								>分成{{ detail.storeKeeperContract.soldShare / 100 }}元</text
							>
						</view>
					</view>
				</view>
				<view
					class="expense-item"
					v-if="
						hasProfitOperationPermi &&
						exchangeDataSet.expenseStoreKeeperIntangible && !isPartner && !isInvestor && !isProvider
					"
				>
					<view class="expense-item-header flex-row-between">
						<view class="expense-item-title">商户现金折扣</view>
						<view class="expense-item-value">
							<text class="expense-item-value-unit">￥</text>
							<text class="expense-item-value-money" :class="{'money-del-line': detail.refund}">{{
								exchangeDataSet.expenseStoreKeeperIntangible
									? exchangeDataSet.expenseStoreKeeperIntangible / 100
									: 0
							}}</text>
						</view>
					</view>
					<view class="expense-item-info"></view>
				</view>
				<view class="expense-item" v-if="!isPartner && !isInvestor && !isProvider">
					<view class="expense-item-header flex-row-between">
						<view class="expense-item-title">系统服务费</view>
						<view
							class="expense-item-value"
							v-if="detail.stage == 'RENTED' || detail.stage == 'CREATED'"
						>
							<text class="expense-item-value-unit">￥</text>
							<text class="expense-item-value-money">--</text>
						</view>
						<view class="expense-item-value" v-else>
							<text class="expense-item-value-unit">￥</text>
							<text class="expense-item-value-money" :class="{'money-del-line': detail.refund}">{{
								exchangeDataSet.expenseService
									? exchangeDataSet.expenseService / 100
									: 0
							}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="refund-box" v-if="detail.refundExchanges && detail.refundExchanges.length">
				<view class="refund-box-header">退款记录</view>
				<view class="refund-box-content">
					<view class="refund-item" v-for="(refundItem, key) in detail.refundExchanges">
						<view class="refund-item-top flex-row-between">
							<view class="refund-item-title">用户退款</view>
							<view class="refund-item-money"><text class="refund-item-money-unit">¥</text><text>{{ refundItem.amount / 100 }}</text></view>
						</view>
						<view class="refund-item-middle flex-row-between">
							<view class="refund-item-sn">退款原因：</view>
							<view class="refund-item-time">{{ refundItem.refundReason }}</view>
						</view>
						<view class="refund-item-middle flex-row-between">
							<view class="refund-item-sn">退款单号：{{ refundItem.sn }}</view>
							<view class="refund-item-time">{{ refundItem.timestamp ? refundItem.timestamp.replace('T', ' ').slice(5, 16) : '' }}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="sold-box" v-if="detail.sold">
				<view class="flex-row-between">
					<view class="sold-title"> 购宝成本 </view>
					<view class="sold-value">
						<text class="sold-value-unit">￥</text>
						<text class="sold-value-money" :class="{'money-del-line': detail.refund}">{{
							exchangeDataSet.expenseCost
								? exchangeDataSet.expenseCost / 100
								: 0
						}}</text>
					</view>
				</view>
				<view class="sold-tips">仅供参考，不计入订单收益</view>
			</view>
		</view>
	</view>
</template>
<script>
import { findExchangeById } from "@/api/subpkg/profit.js";
import { paymentModeList, paymentTypeList } from "@/utils/constant.js";
import { formatTime, formatMinUnitToHour } from "@/utils/function.js";
import { isAlly, isAgentType, isManager, isPartner, isInvestor, isProvider } from "@/utils/role.js";
import { hasProfitOperationPermi } from "@/utils/permissionList.js";

export default {
	data() {
		return {
			sn: "",
			detail: {},
			exchangeDataSet: {},
			paymentModeList,
			paymentTypeList,
		};
	},
	computed: {
		isAgentType,
		isManager,
		isAlly,
		hasProfitOperationPermi,
		isPartner, isInvestor, isProvider
	},
	filters: {
		formatMinUnitToHour,
	},
	async onLoad(option) {
		this.sn = option.sn;
		this.$modal.loading("加载中");
		let res = await findExchangeById({ sn: this.sn });
		this.detail = res.data.findExchangeById;
		this.exchangeDataSet = this.detail.dataSet;
		this.detail.createdDate = formatTime(this.detail.createdDate);
		this.$modal.closeLoading();
	},
	methods: {
		clipboard() {
			let { detail } = this;
			uni.setClipboardData({
				data: detail.sn,
				success: () => {
					uni.showToast({
						title: "复制成功",
						icon: "success",
					});
				},
			});
		},
		toBillOrder() {
			uni.navigateTo({
				url: `/subpkg/pages/profit-bill-order/profit-bill-order?sn=${this.detail.sn}`,
			});
		},
		isPowerbankOrder(detail) {
			return detail.type == 'USER_ORDER'
		},
		isCableOrder(detail) {
			return detail.type == 'CABLE_ORDER'
		},
		isPurchaseOrder(detail) {
			return detail.type == 'PURCHASE_ORDER'
		}
	},
};
</script>
<style scoped lang="scss">
.bill-detail {
	padding-top: 30rpx;
	padding-bottom: 50px;
	.banner {
		height: 161px;
		padding-top: 8px;
		border-radius: 20rpx;
		background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/profit-bill-detail-banner.png");
		background-size: 100% 161px;
		box-sizing: border-box;
		.banner-store {
			min-height: 17px;
			margin-bottom: 11px;
			text-align: center;
			color: #fff;
			letter-spacing: 1px;
			line-height: 17px;
			font-size: 12px;
		}
		.banner-item {
			display: flex;
			align-items: center;
			padding-left: 100rpx;
			.banner-item-icon {
				width: 56rpx;
				height: 28px;
			}
			.banner-item-title,
			.banner-item-value {
				margin-bottom: 2px;
				color: #fff;
				letter-spacing: 6rpx;
				font-size: 24rpx;
				line-height: 17px;
			}
			.banner-item-title {
				font-size: 10px;
				line-height: 14px;
			}
			.banner-item-rented {
				font-size: 24px;
				color: #fff;
				font-weight: 500;
				line-height: 34px;
				letter-spacing: 2px;
			}
			.banner-item-value {
				margin-bottom: 0;
			}
		}
		.banner-order-item {
			margin-bottom: 18px;
		}
		.banner-order-item-left {
			margin-right: 26rpx;
		}
		.banner-profit-item-left {
			margin-right: 26rpx;
		}
		.banner-profit-item-right {
			.banner-item-value {
				margin-top: -3px;
				letter-spacing: 2px;
			}
			.banner-money-unit {
				font-size: 16px;
			}
			.banner-money {
				font-size: 28px;
				line-height: 39px;
			}
		}
		.banner-order-item-right {
			margin-top: -2px;
			.clip-btn {
				margin-left: 20rpx;
				padding: 0 4rpx 0 6rpx;
				font-size: 10px;
				border: 1px solid rgba(255, 255, 255, 0.6);
				line-height: 12px;
				min-height: 12px;
				border-radius: 4rpx;
				color: rgba(255, 255, 255, 0.6);
				letter-spacing: 0;
			}
		}
		.banner-money {
			font-size: 40rpx;
		}
		.refund-btn {
			margin-left: 36rpx;
			padding: 3px 28rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			border-image: linear-gradient(
					360deg,
					rgba(249, 212, 226, 0),
					rgba(255, 229, 239, 1)
				)
				0 0;
			background: rgba(255, 255, 255, 0.36);
			color: #fff;
		}
	}
	.sum-box {
		margin: 26rpx 24rpx 0;
		padding: 40rpx 34rpx 28rpx;
		background-color: #fff;
		border-radius: 24rpx;
		box-shadow: 0px 1px 2px 0px rgba(81, 23, 16, 0.26);
		.sum-item {
			.sum-item-title {
				font-size: 24rpx;
				color: #1b1b1b;
				font-weight: 500;
				letter-spacing: 1px;
				line-height: 34rpx;
			}
			.sum-item-value {
				font-size: 34rpx;
				color: #333;
				font-weight: 600;
			}
			.sum-item-info {
				font-size: 24rpx;
				color: #999;
			}
		}
		.sum-item-header {
			padding-bottom: 12rpx;
			background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/profit-bill-detail-money-icon.png");
			background-size: 100rpx 86rpx;
			background-repeat: no-repeat;
			background-position: right 8rpx;
			border-bottom: 1px solid rgba(27, 27, 27, 0.08);
			.sum-item-value {
				margin-top: 12rpx;
				color: rgba(27, 27, 27, 0.7);
				.sum-item-value-unit {
					font-size: 28rpx;
				}
				.sum-item-value-money {
					font-size: 40rpx;
					line-height: 56rpx;
				}
			}
		}
		.refund-icon {
			background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/profit-refund-icon.png");
		}
		.sum-item-revenue {
			padding: 30rpx 0;
			.sum-item-value {
				color: rgba(27, 27, 27, 0.68);
				.sum-item-unit {
					font-size: 10px;
				}
				.sum-item-money {
					font-size: 16px;
					line-height: 22px;
				}
			}
			.sum-item-info {
				margin-top: 8rpx;
			}
		}
		.sum-item-revenue-bottom0 {
			padding-bottom: 0;
		}
		.sum-item-line {
			height: 1px;
			margin-right: 60rpx;
			background-color: rgba(27, 27, 27, 0.08);
		}
		.sum-item-discount {
			padding-top: 30rpx;
			.sum-item-value {
				color: rgba(27, 27, 27, 0.68);
				.sum-item-unit {
					font-size: 10px;
				}
				.sum-item-money {
					font-size: 16px;
					line-height: 22px;
				}
			}
		}
	}
	.expense-box {
		margin: 34rpx 24rpx 0;
		padding: 0 34rpx;
		background-color: #fff;
		border-radius: 24rpx;
		box-shadow: 0px 1px 2px 0px rgba(81, 23, 16, 0.26);
		.expense-item {
			padding: 30rpx 0rpx 32rpx;
			border-bottom: 1px solid rgba(27, 27, 27, 0.08);
			&:nth-last-child(1) {
				border-bottom: none;
			}
			.expense-item-title {
				font-size: 24rpx;
				color: rgba(27, 27, 27, 0.8);
				font-weight: 500;
				line-height: 34rpx;
				letter-spacing: 1px;
			}
			.expense-item-header {
				margin-bottom: 8rpx;
				.expense-item-value {
					letter-spacing: 1px;
					color: rgba(27, 27, 27, 0.68);
					.expense-item-value-unit {
						font-size: 10px;
					}
					.expense-item-value-money {
						font-size: 16px;
						line-height: 22px;
					}
				}
			}
			.expense-item-info {
				font-size: 10px;
				color: rgba(27, 27, 27, 0.3);
				font-weight: 500;
				letter-spacing: 1px;
			}
			.storekeeper-item-info {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.storekeeper-mode {
					margin-right: 6rpx;
					font-size: 20rpx;
				}
			}
			.special-text {
				color: #fac59f;
			}
		}
		.expense-item-top {
			padding: 28rpx 0 16rpx 0;
		}
	}
	.sold-box {
		padding: 14rpx 60rpx;
		margin: 34rpx 56rpx 0;
		background-color: #fff;
		box-shadow: 0px 1px 2px 0px rgba(81, 23, 16, 0.26);
		border-radius: 24rpx;
		.sold-title {
			color: rgba(27, 27, 27, 0.7);
			line-height: 14px;
			letter-spacing: 1px;
			font-size: 12px;
		}
		.sold-value-unit {
			font-size: 10px;
			color: rgba(27, 27, 27, 0.68);
		}
		.sold-value-money {
			color: rgba(27, 27, 27, 0.68);
			font-size: 14px;
			line-height: 20px;
		}
		.sold-tips {
			margin-top: 1px;
			color: rgba(27, 27, 27, 0.3);
			line-height: 14px;
			font-size: 10px;
			letter-spacing: 1px;
		}
	}
	.refund-box {
		margin: 34rpx 24rpx 0;
    padding: 0 34rpx;
		background-color: #fff;
		box-shadow: 0px 1px 2px 0px rgba(81, 23, 16, 0.26);
		border-radius: 24rpx;
		.refund-box-header {
			padding-top: 28rpx;
			padding-bottom: 16rpx;
			font-size: 24rpx;
			font-weight: bold;
			line-height: 34rpx;
			border-bottom: 1px solid rgba(27,27,27,0.08);
		}
		.refund-box-content {
			letter-spacing: 2rpx;
			.refund-item {
				padding: 28rpx 0;
				border-bottom: 1px solid rgba(27,27,27,0.08);
				&:nth-last-child(1) {
					border-bottom: none;
				}
				.refund-item-top {
					margin-bottom: 4px;
					font-size: 24rpx;
					line-height: 34rpx;
					.refund-item-title {
						color: #000000;
						font-size: 400;
					}
					.refund-item-money {
						color: #EA1C1C;
						font-size: 32rpx;
						line-height: 44rpx;
						font-weight: bold;
						.refund-item-money-unit {
							font-size: 20rpx;
						}
					}
				}
				.refund-item-middle {
					font-size: 20rpx;
					line-height: 28rpx;
					color: rgba(27,27,27,0.3);
					letter-spacing: 0;
				}
			}
		}
	}
}
.order-voucher {
	padding-top: 40rpx;
	padding-bottom: 40rpx;
	.order-voucher-time {
		display: block;
		margin: 20rpx 20rpx;
	}
}
.more-icon {
	display: inline-block;
	width: 10px;
	height: 10px;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/common-slide-right.png");
	background-size: 10px 10px;
}
.content-between {
	display: flex;
	justify-content: space-between;
}
.box {
	margin: 0 20rpx;
	padding: 30rpx 30rpx;
	border-radius: 24rpx;
	background-color: #fff;
}
.row-top {
	margin-top: 20rpx;
}

.money-del-line {
	position: relative;
	&::after {
		display: block;
		content: "";
		position: absolute;
		left: -10%;
		top: 50%;
		transform: rotate(20deg);
		width: 120%;
		height: 1px;
		background-color: #5F5F5F;
		box-shadow: 0px 2px 4px 0px rgba(72, 35, 5, 0.26);
	}
}
.money-del-line-white {
	position: relative;
	&::after {
		display: block;
		content: "";
		position: absolute;
		left: -10%;
		top: 50%;
		transform: rotate(20deg);
		width: 120%;
		height: 2px;
		background-color: #fff;
		box-shadow: 0px 2px 4px 0px rgba(255, 255, 255, 0.26);
	}
}
</style>
