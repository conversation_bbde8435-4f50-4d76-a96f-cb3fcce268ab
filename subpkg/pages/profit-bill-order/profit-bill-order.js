import { findExchangeById } from "@/api/subpkg/profit.js";
import { formatTime } from "@/utils/function.js";
import { isManager } from "@/utils/role.js";
import {
	hasPauseOrderPermi,
	hasFinishOrderPermi,
	hasRefundOrderPermi,
} from "@/utils/permissionList.js";
import { toast } from "@/utils/common";

export default {
	data() {
		return {
			sn: "",
			detail: {},
			billTrade: {},
			pauseInfo: {},
			typeTextList: {
				CREATED: "等待用户付款",
				ADJUSTED: "已运营",
				SEPARATED: "已分账",
				ACCOUNTED: "已记账",
				FIRST_CLEARING: "分账中",
				FIRST_CLEAR_DONE: "分账完成",
				FIRST_CLEAR_FAILURE: "分账失败",
				SECOND_CLEARING: "秒结中",
				SECOND_CLEAR_DONE: "秒结完成",
				SECOND_CLEAR_FAILURE: "秒结失败",
				SCHEDULE_CLEARING: "日结中",
				SCHEDULE_CLEAR_DONE: "日结完成",
				SCHEDULE_CLEAR_FAILURE: "日结失败",
				DONE: "已完成",
				RENTED: "租借中",
				CANCELLED: "已取消",
			},
			isPause: false,
		};
	},
	computed: {
		isManager,
		hasPauseOrderPermi,
		hasFinishOrderPermi,
		hasRefundOrderPermi,
	},
	onLoad(option) {
		this.sn = option.sn;
	},
	async onShow() {
		this.$modal.loading("加载中");
		let res = await findExchangeById({ sn: this.sn });
		this.detail = res.data.findExchangeById;
		this.detail.createdDate = formatTime(this.detail.createdDate);
		this.billTrade = this.detail;

		if (this.detail.operations && this.detail.operations.length && this.detail.operations[0].pauseInfo) {
			this.pauseInfo = this.detail.operations[this.detail.operations.length - 1].pauseInfo;
			let timestamp = new Date().getTime();
			let pauseEndTime = new Date(this.pauseInfo.pauseEndTime).getTime();
			let pauseStartTime = new Date(this.pauseInfo.pauseStartTime).getTime();
			if (
				timestamp >= pauseStartTime &&
				timestamp <= pauseEndTime &&
				this.detail.stage == "RENTED"
			) {
				this.isPause = true;
			}
		}
		
		this.$modal.closeLoading();
	},
	methods: {
		clipboard(data) {
			uni.setClipboardData({
				data: data,
				success: () => {
					uni.showToast({
						title: "复制成功",
						icon: "success",
					});
				},
			});
		},
		toOperate(type) {
			uni.navigateTo({
				url: `/subpkg/pages/profit-bill-order-operate/profit-bill-order-operate?orderId=${this.detail.orderId}&type=${type}&sn=${this.sn}`,
			});
		},
		toPowerbankPage(sn) {
			uni.navigateTo({
				url: `/subpkg/pages/stock-powerbank-detail/stock-powerbank-detail?sn=${sn}`,
			});
		},
		toTerminalPage(sn) {
			uni.navigateTo({
				url: `/subpkg/pages/stock-terminal-detail/stock-terminal-detail?sn=${sn}`,
			});
		},
		toCablePage(sn) {
			uni.navigateTo({
				url: `/subpkg/pages/stock-cable-detail/stock-cable-detail?sn=${sn}`,
			});
		},
		isPowerbankOrder(detail) {
			return detail.type == 'USER_ORDER'
		},
		isCableOrder(detail) {
			return detail.type == 'CABLE_ORDER'
		},
		isPurchaseOrder(detail) {
			return detail.type == 'PURCHASE_ORDER'
		}
	},
};
