import {
	getOrgVip,
	getMyVip,
	getMyVipList,
	getOrgVipList,
  getStoreKeeperList,
  getOrgSumData,
} from "@/subpkg/api/vip";
import { isProvider, manageId, orgId, getOrgRouteFilter } from "@/utils/role.js";
import { hasSendVipPerPermi } from "@/utils/permissionList.js";

export default {
	data() {
		return {
			timeTab: 1,
			recordList: [],
      detail: {},
			orgFramework: {},
			statistic: {},
			pageInfo: {},
		};
	},
	computed: {
		type() {
			return this.$store.state.user.type;
		},
		isManager() {
			return this.$store.getters.isManager;
		},
		isProvider,
		hasSendVipPerPermi,
    orgId,
    manageId,
	},
	async onLoad() {
		this.pageInfo = {};
		this.timeTab = 1;
		this.$modal.loading("加载中...");
		let { isManager, orgId, manageId } = this;
    let res1 = await getOrgSumData({ id: isManager ? orgId : manageId });
		this.orgFramework = res1.data.findOrgDetailById.framework;
    this.statistic = res1.data.findOrgDetailById.framework.dateRange.dataset;
    this.$modal.closeLoading();

		let listRes = await this.getList();
		this.recordList = this.flatNodeList(listRes.content);

    return;
		let getVipList = isManager ? getOrgVip : getMyVip;
		let getVipListString = isManager
			? "findStoreKeeperByCurrentOrg"
			: "findStoreKeeperByCurrentDeveloper";
		let statisticString = isManager
			? "findCurrentOrganization"
			: "findCurrentEmployee";
		let res = await getVipList({ page: this.pageInfo.endCursor });
		this.$modal.closeLoading();
		this.recordList = this.flatNodeList(res.data[getVipListString].content);
		this.pageInfo = res.data[getVipListString].pageInfo;
		this.statistic = res.data[statisticString];
	},
	async onPullDownRefresh() {
		this.pageInfo = {};
		let res = await this.getList();
		uni.stopPullDownRefresh();
		this.recordList = this.flatNodeList(res.content);
		this.pageInfo = res.pageInfo;
	},
	async onReachBottom() {
		if (!this.pageInfo.hasNextPage) return;
		this.$modal.loading('加载中');
		let res = await this.getList();
		let arr = this.flatNodeList(res.content);
		this.recordList = [...this.recordList, ...arr];
		this.$modal.closeLoading();
	},
	methods: {
		async getList1() {
			this.$modal.loading("加载中...");
			let { isManager } = this;
			let getVipList = isManager ? getOrgVipList : getMyVipList;
			let getVipListString = isManager
				? "findStoreKeeperByCurrentOrg"
				: "findStoreKeeperByCurrentDeveloper";
			let res = await getVipList({ cursor: this.pageInfo.endCursor });
			this.pageInfo = res.data[getVipListString].pageInfo;
			this.$modal.closeLoading();
			return res.data[getVipListString];
		},
    async getList() {
			const { pageInfo } = this;

			const orgRouteFilter = getOrgRouteFilter();
      let res = await getStoreKeeperList({ orgRouteFilter, after: pageInfo.endCursor });
      this.pageInfo = res.data.findOrgDetailPaging.pageInfo;
      return res.data.findOrgDetailPaging;
    },
		async changeTab(tab) {
			this.timeTab = tab;
		},
		toRecordDetail(item) {
			uni.navigateTo({
				url: `/subpkg/pages/vip-record/vip-record?searchText=${item.companyFullName}&storeKeeperId=${item.id}`,
			});
		},
		toRecord() {
			uni.navigateTo({
				url: `/subpkg/pages/vip-record/vip-record`,
			});
		},
		toGrant() {
			let { tab } = this;
			uni.navigateTo({
				url: `/subpkg/pages/vip-send/vip-send?type=${this.type}`,
			});
		},
		flatNodeList(edges) {
			let arr = [];
			edges.forEach((item) => {
				arr.push(item.node);
			});
			return arr;
		},
		calcRate(num1, num2) {
			if (!num1 || !num2) return 0;
			return parseInt(num1 / num2 * 100, 10);
		},
	},
};