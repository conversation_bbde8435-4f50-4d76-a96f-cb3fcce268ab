import { setPriceRule, getShopInfo } from "@/subpkg/api/shop-set-price";
import { getTemplateListApi, setTemplateApi } from '@/api/common';
import { orgId } from '@/utils/role.js';
import { toast } from "@/utils/common";

export default {
	data() {
		return {
			allyId: null,
			shopId: "",
			defaultSetting: {
				freeTime: 3,
				unitPrice: 500,
				unitTimeType: 1,
				billingUnit: 60,
				cappedDay: 3000,
				cappedAll: 9900,
				forbidden: "ALL_ALLOWED",
				forbiddenStartTime: '',
				forbiddenEndTime: '',
				rentThreshold: 4900,
			},
			unitType: 1,
			unitTimeList: [
				{
					text: "15分钟",
					value: 5,
				},
				{
					text: "20分钟",
					value: 4,
				},
				{
					text: "30分钟",
					value: 2,
				},
				{
					text: "60分钟",
					value: 1,
				},
			],
			customUnit: "",
			forbiddenRule: [
				{
					text: "全部可用",
					value: "ALL_ALLOWED",
				},
				{
					text: "禁用全部微信支付",
					value: "FORBIDDEN_WECHAT",
				},
				{
					text: "禁用微信支付分",
					value: "FORBIDDEN_WECHAT_CREDIT",
				},
				{
					text: "禁用全部支付宝",
					value: "FORBIDDEN_ALIPAY",
				},
				{
					text: "禁用支付宝先享后付",
					value: "FORBIDDEN_ALIPAY_CREDIT",
				},
			],
			custom: {
				freeTime: 3,
				unitPrice: 5,
				unitTimeType: 1,
				billingUnit: 60,
				cappedDay: 30,
				cappedAll: 99,
				forbidden: "ALL_ALLOWED",
				forbiddenStartTime: '',
				forbiddenEndTime: '',
				rentThreshold: 4900,
			},

      // 引入模板
      templateList: [], // 模板列表
      selectIndexId: '', // 默认引用的模板
		};
	},
  computed: {
    orgId,
  },
	async onLoad(option) {
		this.$modal.loading("加载中...");
		this.shopId = option.id;

		this.$modal.closeLoading();
		let res = await this.getShopData();
		this.getIndexList();
	},
	methods: {
		async getShopData() {
			this.$modal.loading("加载中");
			let res = await getShopInfo({ id: this.shopId, page: 0 });
			this.allyId = res.data.findStoreById.storeKeeper && res.data.findStoreById.storeKeeper.ally ? res.data.findStoreById.storeKeeper.ally.id : this.orgId;

			this.selectIndexId = res.data.findStoreById.chargeConfigSetting.indexId || -1;
			this.defaultSetting = res.data.findStoreById.chargeConfig;
			// 金额都是以分为单位
			this.defaultSetting.unitPrice = this.defaultSetting.unitPrice / 100;
			this.defaultSetting.cappedDay = this.defaultSetting.cappedDay / 100;
			this.defaultSetting.cappedAll = this.defaultSetting.cappedAll / 100;
			this.defaultSetting.rentThreshold = this.defaultSetting.rentThreshold || 4900;
			this.unitType =
				this.defaultSetting.billingUnit == 60
					? 1
					: this.defaultSetting.billingUnit == 30
					? 2
					: this.defaultSetting.billingUnit == 20
					? 4
					: this.defaultSetting.billingUnit == 15
					? 5
					: 3;
			this.customUnit =
				this.unitType == 3
					? (this.defaultSetting.billingUnit / 60).toFixed(0)
					: "";
			// 把自定义的规则设为默认的规则；
			this.custom = { ...this.defaultSetting };
			this.custom.unitTimeType = [60, 30].indexOf(this.custom.billingUnit) + 1;
			this.$modal.closeLoading();
			return res.data;
		},
    async getIndexList() {
      let res = await getTemplateListApi({ orgId: this.allyId, key: "BillingConfigPowerbankDto" });
      this.templateList = res.data.findSettingIndexes.filter(item => {
        return item.orgId != 1
      });
    },
    chooseTemplate(template) {
      this.selectIndexId = template.indexId;
    },
		async setTemplate() {
			if (this.selectIndexId > 0) {
				let res = await setTemplateApi({ key: 'BillingConfigPowerbankDto', orgId: this.shopId, templateId: this.selectIndexId });

				let selectTemplateArr = this.templateList.filter(item => item.indexId == this.selectIndexId);
				const selectTemplate = selectTemplateArr[0];

				let params = {
					shopId: this.shopId,
					unitPrice: (selectTemplate.object.unitPrice).toFixed(0),
					cappedDay: selectTemplate.object.cappedDay,
					cappedAll: selectTemplate.object.cappedAll,
					freeTime: selectTemplate.object.freeTime,
					billingUnit: selectTemplate.object.billingUnit,
					id: selectTemplate.object.id,
					priceRule: selectTemplate.object.priceRule,
					forbiddenStartTime: selectTemplate.object.forbiddenStartTime || null,
					forbiddenEndTime: selectTemplate.object.forbiddenEndTime || null,
					rentThreshold: selectTemplate.object.rentThreshold || 4900,
				};

				uni.$emit("handleClickSetTerminalPrice", { data: params });
				uni.navigateBack();
			} else {
				this.set();
			}
		},
		changeBillingUnitType(type, value) {
			this[type] = value;
		},
		changeRadio(e, type) {
			this.custom[type] = e.detail.value;
		},
		blurCustomUnit() {
			let { customUnit } = this;
			let isInteger = Math.floor(customUnit) == customUnit;
			if (!isInteger) {
				this.customUnit = Math.floor(customUnit);
				toast("计费单元需是整数");
				return;
			}
			if (customUnit < 2 || customUnit > 24) {
				this.customUnit = 2;
				toast("计费单元为2-24小时");
				return;
			}
		},
		back() {
			uni.navigateBack();
		},
		async set() {
			uni.showLoading({
				title: "加载中...",
			});
			if (this.custom.freeTime < 3) {
				toast("免费时长最少3分钟");
				return;
			}
			if (this.custom.freeTime > 1440) {
				toast("免费时长最多1440分钟");
				return;
			}

			if (this.unitType == 3 && !this.customUnit) {
				toast("请输入计费单元小时");
				return;
			}
			this.custom.billingUnit =
				this.unitType == 1
					? 60
					: this.unitType == 2
					? 30
					: this.unitType == 4
					? 20
					: this.unitType == 5
					? 15
					: this.customUnit * 60;
			let params = {
				shopId: this.shopId,
				unitPrice: (this.custom.unitPrice * 100).toFixed(0),
				cappedDay: this.custom.cappedDay * 100,
				cappedAll: this.custom.cappedAll * 100,
				freeTime: this.custom.freeTime,
				billingUnit: this.custom.billingUnit,
				id: this.custom.id,
				forbiddenStartTime: this.defaultSetting.forbiddenStartTime,
				forbiddenEndTime: this.defaultSetting.forbiddenEndTime,
				rentThreshold: this.defaultSetting.rentThreshold || 4900
			};
			let res = await setPriceRule(params);
			uni.hideLoading();
			if (res.code == 200) {
				uni.showToast({
					title: "保存成功",
					duration: 1000,
				});
				uni.$emit("handleClickSetTerminalPrice", { data: params });
				uni.navigateBack();
			}
		},
    toHistoryPage() {
      uni.navigateTo({
				url: `/subpkg/pages/storekeeper-contract-history/storekeeper-contract-history?id=${this.shopId}&filterType=chargeSetting`,
			});
    },
	},
};