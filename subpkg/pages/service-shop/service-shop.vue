<template>
	<view class="service-shop-contain">
		<view class="header">
			<view class="header-content flex-row">
				<view class="header-filter-type">
					<uni-data-select
						v-model="filterType"
						:localdata="typeList"
						@change="changeType"
						:clear="false"
						placeholder="请选择"
					></uni-data-select>
				</view>
				<view class="header-filter-input">
					<input
						class="search-input"
						@confirm="search"
						confirm-type="search"
						v-if="filterType != 3"
						placeholder="点击输入"
						type="text"
						v-model="searchText"
					/>
					<input
						class="search-input"
						v-if="filterType == 3"
						v-model="sn"
						placeholder="请输入22位SN码"
						type="text"
						:adjust-position="false"
					/>
				</view>
				<view class="header-filter-btn" @click="search" v-if="filterType != 3"
					>搜索</view
				>
			</view>
		</view>
		<block v-if="list.length || storeKeeperList.length">
			<view class="shop-list" v-if="filterType == 1 || !searchText || !inited">
				<view class="shop-item" v-for="(item, key) in list" :key="key">
					<view @click="toStore(item, item.storeKeeper)">
						<view class="item-header">
							<view class="item-header-left">
								<view
									class="item-header-tag"
									v-if="key === 0 && !inited"
								></view>
								<text
									class="item-header-name"
									:class="{ 'item-header-name-first': key === 0 && !inited }"
									>{{ item.name }}</text
								>
							</view>
							<view class="item-header-right" v-if="item.distance">
								<text class="gps-icon"></text>
								<text class="">{{ item.distance }}</text>
							</view>
						</view>
						<view class="item-address">{{
							item.poi.province +
							item.poi.city +
							item.poi.district +
							item.poi.address
						}}</view>
						<view
							class="item-storekeeper"
							:class="{ 'border-none': !item.terminals.length }"
							>隶属商户：{{ item.storeKeeper.companyFullName }}</view
						>
					</view>

					<view class="item-owership-list">
						<view
							class="owership-item"
							v-for="(terminal, terminalKey) in item.terminals"
							:key="terminal.sn"
							@tap.stop="toTerminalService(terminal, terminalKey)"
						>
							<view class="flex-row-between">
								<view class="owership-title">
									<text
										class="owership-icon"
										:class="{
											'owership-icon-gray': !terminal.online,
										}"
									></text>
									<text :class="{'special-text': terminal.online,}">{{ terminal.aliasSn ? terminal.aliasSn : terminal.sn }}{{terminal.point ? `(${terminal.point})` : ''}}</text>
									<text
										class="owership-ower"
										v-if="orgId != terminal.owner.id"
									></text>
								</view>
								<view class="terminal-online-icon" :style="{ backgroundImage: 'url(' + getNetStrengthIcon(terminal) + ')' }"></view>
							</view>
							<view class="owership-status-list" v-if="terminal.online">
								<view class="owership-status-left flex-row">
									<view class="owership-status-item flex-row">
										<text class="powerbanks-icon"></text>
										<text>充电宝:{{ terminal.powerBanks.length }}</text>
									</view>
									<view class="owership-status-item flex-row">
										<text class="channel-icon"></text>
										<text>{{ terminal.channels }}仓机</text>
									</view>
									<view class="owership-status-item flex-row">
										<text class="powerbanks-icon"></text>
										<text>租借中:{{ terminal.rentOrderNumber }}</text>
									</view>
								</view>
								<view class="">
									<text
										v-if="false"
										class="online-icon"
										:style="{
											backgroundImage:
												'url(' + getNetStrengthIcon(terminal) + ')',
										}"
									></text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="storekeeper-list" v-else-if="filterType == 2 && inited">
				<view
					class="storekeeper-item"
					v-for="(storekeeper, key) in storeKeeperList"
					:key="storekeeper.id"
				>
					<view class="storekeeper-name">{{
						storekeeper.companyFullName
					}}</view>
					<block v-if="storekeeper.listStore.length">
						<view
							class="shop-item"
							v-for="(item, key) in storekeeper.listStore"
							:key="item.id"
						>
							<view @click="toStore(item, storekeeper)">
								<view class="item-header">
									<view class="item-header-left">
										<text class="item-header-name">{{ item.name }}</text>
									</view>
									<view class="item-header-right" v-if="item.distance">
										<text class="gps-icon"></text>
										<text class="">{{ item.distance }}</text>
									</view>
								</view>
								<view class="item-address">{{
									item.poi.province +
									item.poi.city +
									item.poi.district +
									item.poi.address
								}}</view>
							</view>
							<view class="item-owership-list">
								<view
									class="owership-item"
									v-for="(terminal, terminalKey) in item.terminals"
									:key="terminal.sn"
									@click="toTerminalService(terminal, terminalKey)"
								>
								<view class="flex-row-between">
									<view class="owership-title">
										<text
											class="owership-icon"
											:class="{
												'owership-icon-gray': !terminal.online,
											}"
										></text>
										<text :class="{'special-text': terminal.online,}">{{ terminal.aliasSn ? terminal.aliasSn : terminal.sn }}{{terminal.point ? `(${terminal.point})` : ''}}</text>
										<text
											class="owership-ower"
											v-if="orgId != terminal.owner.id"
										></text>
									</view>
									<view class="terminal-online-icon" :style="{ backgroundImage: 'url(' + getNetStrengthIcon(item) + ')' }"></view>
								</view>

									<view class="owership-status-list" v-if="terminal.online">
										<view class="owership-status-left flex-row">
											<view class="owership-status-item flex-row">
												<text class="channel-icon"></text>
												<text>{{ terminal.channels }}仓机</text>
											</view>
											<view class="owership-status-item flex-row">
												<text class="powerbanks-icon"></text>
												<text>充电宝:{{ terminal.powerBanks.length }}</text>
											</view>
										</view>
										<view class="">
											<text class="online-icon"></text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</block>
					<block v-else>
						<view class="shop-item shop-item-empty">
							<view>暂无门店</view>
						</view>
					</block>
				</view>
			</view>
		</block>
		<view
			class="shop-empty"
			v-if="
				(!list.length && filterType == 1) ||
				(!storeKeeperList.length && filterType == 2 && inited) ||
				(!list.length && filterType == 3 && !inited)
			"
		>
			{{
				filterType == 1 || filterType == 3
					? "您附近暂时没有门店"
					: "请输入正确的商户名"
			}}哦~
		</view>
		<view class="scan-btn-box">
			<view class="scan-btn" @click="toSan">
				<text class="scan-icon"></text>
				<text class="scan-title">扫一扫</text>
			</view>
		</view>
		<mLoading :show="loading" />
	</view>
</template>

<script src="./service-shop.js"></script>

<style lang="scss" scoped>
::v-deep .uni-select {
	height: 30px;
	padding-left: 36rpx !important;
	box-sizing: border-box;
	border: none !important;
	border-bottom: none !important;
}
::v-deep .uni-select__input-box {
	height: 30px;
	box-sizing: border-box;
}
.service-shop-contain {
	padding-bottom: 87px; // 抹平置顶的元素
	// padding: 20rpx 30rpx 90px;
}

.header {
	position: sticky;
	top: 0;
	left: 0;
	width: 100%;
	height: 58px;
	padding-top: 14px;
	margin-bottom: 12px;
	background: #ffffff;
	box-shadow: 0px 2px 6px 0px rgba(81, 23, 0, 0.26);
	box-sizing: border-box;
	z-index: 90;
	.header-content {
		margin: 0 24rpx;
		height: 30px;
		box-sizing: border-box;
		border: 1px solid rgba(45, 45, 45, 0.1);
		background: #f8f8f8;
		border-radius: 15px;
		.header-filter-type {
			width: 220rpx;
		}
	}
	.header-filter-input {
		flex: 1;
		.search-input {
			height: 30px;
			padding-left: 24rpx;
			border-left: 1px solid rgba(45, 45, 45, 0.1);
			font-size: 24rpx;
		}
	}
	.header-filter-btn {
		flex-shrink: 0;
		padding-left: 8rpx;
		padding-right: 26rpx;
		font-size: 24rpx;
		color: #ff8540;
		letter-spacing: 3px;
	}
}


.storekeeper-list {
	margin: 0 24rpx;
	.storekeeper-name {
		padding: 20rpx 36rpx 18rpx;
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		background-color: #ff8541;
	}
	.storekeeper-item {
		margin-bottom: 24rpx;
		border-radius: 24rpx;
		background: #ffffff;
		box-shadow: 0px 1px 2px 0px rgba(81, 23, 0, 0.26);
		overflow: hidden;
		&:nth-last-child(1) {
			margin-bottom: 0;
		}
	}
	.shop-item {
		margin: 0 36rpx;
		padding-left: 0;
		padding-right: 0;
		border-radius: 0;
		box-shadow: none;
		border-bottom: 1px solid rgba(45, 45, 45, 0.3);
		&:nth-last-child(1) {
			border-bottom: none;
		}
	}
	.shop-item-empty {
		font-size: 28rpx;
		color: #999;
		text-align: center;
	}
}

.shop-list {
	margin: 0 24rpx;
}
.shop-item {
	padding: 24rpx 36rpx;
	margin-bottom: 24rpx;
	border-radius: 24rpx;
	background: #ffffff;
	box-shadow: 0px 1px 2px 0px rgba(81, 23, 0, 0.26);
	&:nth-last-child(1) {
		margin-bottom: 0;
	}
}

.item-header {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
}
.item-header-left {
	display: flex;
	align-items: center;
}
.item-header-tag {
	position: absolute;
	left: -44rpx;
	top: 0;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/near-icon.png");
	width: 54rpx;
	height: 40rpx;
	background-size: 54rpx 40rpx;
}
.item-header-tag-active {
	border: 1px solid #f60;
	color: #f60;
}
.item-header-name {
	font-size: 28rpx;
	color: rgba(45, 45, 45, 0.9);
}
.item-header-name-first {
	padding-left: 20rpx;
}
.item-header-right {
	display: flex;
	align-items: center;
	color: #999;
	font-size: 24rpx;
}
.gps-icon {
	display: inline-block;
	width: 20rpx;
	height: 20rpx;
	margin-right: 10rpx;
	margin-left: 10rpx;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/gps-icon.png");
	background-size: 20rpx 20rpx;
}
.item-address,
.item-storekeeper {
	margin-bottom: 8rpx;
	color: #999;
	font-size: 24rpx;
}
.item-storekeeper {
	margin-bottom: 0;
	padding-bottom: 16rpx;
	border-bottom: 1px solid rgba(45, 45, 45, 0.1);
}
.border-none {
	border-bottom: none;
}

.owership-item {
	margin-top: 20rpx;
	padding-bottom: 24rpx;
	font-size: 24rpx;
	border-bottom: 1px solid #eee;
	color: #888;
	&:nth-last-child(1) {
		border-bottom: none;
		padding-bottom: 0;
	}
}
.owership-title {
	display: flex;
	align-items: center;
	margin-top: 6px;
	font-size: 20rpx;
	line-height: 28rpx;
}
.owership-icon {
	display: inline-block;
	width: 36rpx;
	height: 36rpx;
	margin-right: 14rpx;
	background-size: 36rpx 36rpx;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/service-shop-sn-icon.png");
}
.owership-icon-gray {
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/service-shop-sn-gray.png");
}
.owership-ower {
	display: inline-block;
	width: 116rpx;
	height: 28rpx;
	margin-left: 32rpx;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/service-shop-ower-icon.png");
	background-size: 116rpx 28rpx;
}
.owership-status-list {
	display: flex;
	justify-content: space-between;
	margin-top: 20rpx;
	color: #f60;
}
.special-text {
	color: #f60;
}
.powerbanks-icon,
.channel-icon {
	display: inline-block;
	width: 36rpx;
	height: 36rpx;
	margin-right: 14rpx;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/service-shop-powerbank-icon.png");
	background-size: 36rpx 36rpx;
}
.channel-icon {
	margin-left: 64rpx;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/service-shop-channel-icon.png");
}
.owership-status-item:nth-last-child(1) {
	margin-left: 64rpx;
}

.online-icon {
	display: inline-block;
	width: 36rpx;
	height: 36rpx;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/stock-online-icon.png");
	background-size: 36rpx 36rpx;
}

.shop-empty {
	padding: 40rpx 0;
	text-align: center;
	font-size: 26rpx;
	color: #999;
}

.scan-btn-box {
	position: fixed;
	bottom: constant(safe-area-inset-bottom);
	bottom: env(safe-area-inset-bottom);
	left: 0;
	display: flex;
	align-items: center;
	width: 100%;
	height: 176rpx;
	padding-bottom: 20rpx;
}
.scan-btn {
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	bottom: 40rpx;
	// width: 304rpx;
	// height: 36px;
	// background: linear-gradient(121deg, #ffbc65 0%, #ff817a 100%);
	// box-shadow: 0px 1px 2px 0px rgba(81, 23, 16, 0.16);
	// border-radius: 18px;
	color: #fff;
	// text-align: center;
	font-size: 32rpx;
	box-sizing: border-box;
	letter-spacing: 1px;
	// font-weight: bold;
	width: 466rpx;
	height: 48px;
	background: linear-gradient(90deg, #ef9a6b 9%, #f3693d 100%);
	box-shadow: 0px 1px 2px 0px rgba(81, 23, 16, 0.26);
	border-radius: 12px 12px 12px 12px;
}
.scan-icon {
	display: block;
	width: 40rpx;
	height: 40rpx;
	margin-right: 34rpx;
	background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/service-scan.png");
	background-size: 40rpx 40rpx;
}

.terminal-online-icon {
	display: block;
	width: 36rpx;
	height: 36rpx;
	background-size: 36rpx 36rpx;
}
.nfc-icon {
	position: fixed;
	right: 60rpx;
	bottom: 160px;
	width: 100rpx;
	height: 100rpx;
	text-align: center;
	color: #fff;
	background: linear-gradient(90deg, #ef9a6b 9%, #f3693d 100%);
	border-radius: 50%;
	line-height: 100rpx;
	font-size: 32rpx;
	font-weight: bold;
	box-shadow: #DDDDDD 4px 4px 8px, #DDDDDD -4px -4px 8px;
	letter-spacing: 1px;
}
</style>
