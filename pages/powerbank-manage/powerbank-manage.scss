
::v-deep swiper-item {
  overflow: auto !important;
}

::v-deep .uni-select {
  padding-left: 0 !important;
  background-color: transparent !important;
  border: none !important;
}
::v-deep .uni-select, .uni-select__input-box {
  height: 17px !important;
  font-size: 24rpx;
}
::v-deep .uni-select__selector-item {
  padding-top: 10rpx !important;
  padding-bottom: 10rpx !important;
  font-size: 24rpx !important;
  line-height: 40rpx !important;
  text-align: left !important;
}

.powerbank-manage-contain {
  padding-top: 11px;
  padding-bottom: 100px;
}

.tab-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 32rpx;
  padding: 0 40rpx;

  .tab-item {
    font-size: 32rpx;
    line-height: 44rpx;
    color: #B2B0AF;
  }

  .tab-item-active {
    position: relative;
    background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;

    &::after {
      position: absolute;
      left: 50%;
      bottom: -12rpx;
      transform: translateX(-50%);
      display: block;
      content: "";
      width: 96rpx;
      height: 8rpx;
      background: linear-gradient(90deg, #F9983E 0%, #FD5935 100%);
      border-radius: 2px 2px 2px 2px;
    }
  }
}

.sum-box {
  margin: 36rpx 32rpx 0;
  padding: 2px 2px 16px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px 8px 8px 8px;

  .sum-box-banner {
    width: 100%;
    height: 64px;
    padding: 0 14px;
    background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/powerbank-manage-sum-bg.png');
    background-size: 100% 64px;
    box-sizing: border-box;
  }

  .sum-box-banner-title {
    font-size: 38rpx;
    font-weight: bold;
    color: #2E2C2B;
    padding-right: 34rpx;
    background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-icon-black.png");
    background-position: right center;
    background-size: 24rpx 24rpx;
    background-repeat: no-repeat;
  }

  .sum-box-banner-value {
    font-size: 48rpx;
    font-weight: bold;
    background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .sum-item-box {
    display: flex;
    align-items: center;
    height: 57px;
    margin: 0 14px 8px;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: 4px 0px 0px 4px;
    border: 1px solid #ebe9e7;

    &:nth-last-child(1) {
      margin-bottom: 0;
    }

    .sum-item-title {
      width: 160rpx;
      text-align: center;
      background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      box-sizing: border-box;
      line-height: 57px;
      font-size: 24rpx;
      box-shadow: inset 4px 0px 4px 0px rgba(0, 0, 0, 0.04);
      border-right: 1px solid #ebe9e7;
    }

    .sum-item-title-normal {
      background: linear-gradient(0deg, #2E2C2B 0%, #6F6A67 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .sum-item-content {
      flex: 1;
      height: 57px;
      display: flex;
      box-shadow: inset 4px 0px 8px 0px rgba(0, 0, 0, 0.04);

      .sum-item-block {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .sum-item-block-title {
          color: #6F6A67;
          font-size: 24rpx;
          line-height: 17px;
          font-weight: 500;
          letter-spacing: 2rpx;
        }

        .sum-item-block-value {
          font-size: 28rpx;
          line-height: 40rpx;
          margin-top: 8rpx;
          color: #2e2c2b;
        }
      }
    }
  }
}

.my-box-bg {
  width: 100%;
  height: 80px;
  margin-top: 12px;
  box-sizing: border-box;
  border-top: 1px dashed #ebe9e7;
  background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
}

.my-box {
  margin: 0 32rpx;
  height: 276rpx;
  margin-top: -68px;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/page-powerbank-manage-my-bg.png');
  background-size: 100% 100%;

  .header {
    display: flex;

    .header-left {
      width: 400rpx;
      line-height: 22px;
      padding-top: 16px;
      padding-left: 32rpx;

      .header-left-title {
        font-size: 16px;
        font-weight: bold;
        color: #2e2c2b;
        line-height: 22px;

        margin-right: 10rpx;
        padding-right: 34rpx;
        background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-icon-black.png");
        background-position: right center;
        background-size: 24rpx 24rpx;
        background-repeat: no-repeat;
      }

      .header-left-value {
        background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-weight: bold;
        font-size: 16px;
        line-height: 22px;
      }
    }

    .header-right {
      flex: 1;
      padding-top: 11px;
      color: #f9733e;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
    }
  }

  .content {
    margin: 12px 32rpx 0;
    padding: 4px 8rpx;
    height: 72px;
    background-color: #fafafa;
    box-sizing: border-box;
    border-radius: 8px;

    .content-item {
      position: relative;
      flex: 1;
      height: 64px;
      padding: 8px 24rpx 8px 36rpx;
      margin-right: 8rpx;
      background-color: #fff;
      box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.04);
      border-radius: 8rpx;
      box-sizing: border-box;

      &:nth-last-child(1) {
        margin-right: 0;
      }

      &:nth-of-type(1)::after {
        position: absolute;
        top: 28px;
        left: 16rpx;
        display: block;
        content: "";
        width: 4rpx;
        height: 8px;
        background: #31CC17;
        border-radius: 2rpx;
      }

      &:nth-of-type(2)::after {
        position: absolute;
        top: 28px;
        left: 16rpx;
        display: block;
        content: "";
        width: 4rpx;
        height: 8px;
        background: #F45843;
        border-radius: 2rpx;
      }

      .content-item-row {
        margin-bottom: 8px;
        font-size: 28rpx;
        line-height: 48px;

        &:nth-last-child(1) {
          margin-bottom: 0;
        }

        .content-item-row-left {
          color: #6F6A67;
          font-weight: bold;
        }

        .content-item-row-right {
          color: #2E2C2B;
        }
      }
    }
  }
}

.org-box {
  margin: 12px 32rpx 0;
  padding: 32rpx;
  background-color: #fff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px 8px 8px 8px;

  .org-box-header {
    margin-bottom: 24rpx;

    .org-box-header-left {
      color: #2e2c2b;
      font-size: 32rpx;
      font-weight: bold;
      letter-spacing: 2rpx;
      line-height: 44rpx;

      padding-right: 34rpx;
      background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-icon-black.png");
      background-position: right center;
      background-size: 24rpx 24rpx;
      background-repeat: no-repeat;
    }

    .org-box-header-right {
      color: #6f6a67;
      font-size: 28rpx;
      line-height: 40rpx;
      letter-spacing: 1rpx;
    }
  }

  .org-box-content {
    .org-item {
      margin-bottom: 12px;
      padding: 16rpx;
      border: 1px solid #ebe9e7;
      border-radius: 4px;

      &:nth-last-child(1) {
        margin-bottom: 0;
      }

      .org-item-header {
        margin-bottom: 14rpx;
        padding-bottom: 16rpx;
        border-bottom: 1rpx solid #EBE9E7;

        .org-item-header-left {
          font-size: 26rpx;
          color: #2E2C2B;
          font-weight: bold;
          .org-item-header-left {
            
          }
        }

        .org-item-header-right {
          flex-shrink: 0;
          font-size: 24rpx;
          color: #2E2C2B;
          letter-spacing: 1rpx;
        }
      }

      .org-item-content {
        .org-item-content-left {
          position: relative;
          width: 432rpx;
          padding: 32rpx 0;
          flex-shrink: 0;
          background: #FAFAFA;
          box-shadow: inset 1px 4px 8px 0px rgba(0,0,0,0.04);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #EBE9E7;

          &::after {
            display: block;
            content: "";
            position: absolute;
            right: 0rpx;
            top: 30rpx;
            width: 1rpx;
            height: 24rpx;
            background-color: #EBE9E7;
          }
        }

        .org-item-content-right {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: flex-end;
          .org-item-content-total {
            font-size: 32rpx;
            font-weight: bold;
            line-height: 44rpx;
            background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .org-item-content-ratio {
            font-size: 24rpx;
            line-height: 34rpx;
            color: #B2B0AF;
          }
        }
      }
    }

    .org-item-content-row {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      margin-right: 48rpx;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      &:nth-of-type(1)::after {
        display: block;
        content: '';
        position: absolute;
        right: -24rpx;
        top: 30rpx;
        width: 1px;
        height: 24rpx;
        background-color: #EBE9E7;
      }

      .org-item-content-row-title {
        margin-bottom: 8rpx;
        font-size: 24rpx;
        line-height: 34rpx;
        font-weight: bold;
        color: #6F6A67;
      }

      .org-item-content-row-value {
        font-size: 28rpx;
        color: #2E2C2B;
        line-height: 40rpx;
      }
    }
  }

  .org-box-content-empty {
    padding: 30rpx;
    font-size: 28rpx;
    color: #999;
    text-align: center;
    line-height: 40rpx;
    letter-spacing: 2rpx;
  }
}

.sale-box {
  position: fixed;
  bottom: 0;
  left: 16rpx;
  width: 718rpx;
  height: calc(84px + 50px + env(safe-area-inset-bottom)); // 50px + env()是底部导航栏高度
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.16);
  border-radius: 8px 8px 0px 0px;
  box-sizing: border-box;
  transition: all .3s ease-in-out;
}
.slide-down-icon {
  width: 100%;
  height: 48rpx;
  background-size: 48rpx 48rpx;
  background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/powerbank-manage-slide-icon.png');
  background-repeat: no-repeat;
  background-position: center top;
  transition: .4s all;
}
.sale-box-down {
  bottom: -60px;
  .slide-down-icon {
    transform: rotate(180deg);
  }
}
.sale-btn {
  width: 320rpx;
  height: 88rpx;
  margin: 16rpx auto 0;
  box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.05);
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #EBE9E7;
  box-sizing: border-box;
  text-align: center;
  line-height: 88rpx;
  background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}


.holder-box {
  .holder-banner {
    margin: 16px 32rpx 0;
    height: 94px;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0) 25%, rgba(0, 0, 0, 0.02) 52%), #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 8px 8px 8px 8px;
    border: 2px solid #FFFFFF;

    .banner-left {
      flex-shrink: 0;
      width: 364rpx;
      height: 94px;
      padding-top: 16px;
      background-image: url('https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/page-powerbank-manage-holder-banner.png');
      background-repeat: no-repeat;
      background-size: 364rpx 94px;
      text-align: center;
      box-sizing: border-box;

      .banner-left-title {
        justify-content: center;
        margin-bottom: 8px;
        background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 16px;
        line-height: 22px;
        font-weight: bold;
        letter-spacing: 2rpx;

        .info-icon {
          display: block;
          width: 24rpx;
          height: 24rpx;
          margin-left: 10rpx;
          background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-icon-black.png");
          background-position: right center;
          background-size: 24rpx 24rpx;
          background-repeat: no-repeat;
        }
      }

      .banner-left-value {
        background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 24px;
        line-height: 32px;
        font-weight: bold;
      }
    }

    .banner-right {
      flex: 1;
      height: 94px;
      padding-top: 16px;
      text-align: center;
      box-sizing: border-box;

      .banner-right-title {
        margin-bottom: 8px;
        color: #2e2c2b;
        font-size: 16px;
        line-height: 22px;
        font-weight: bold;
        letter-spacing: 2rpx;
      }

      .banner-right-value {
        color: #F45843;
        font-size: 24px;
        line-height: 32px;
        font-weight: bold;
      }

      .green-color {
        color: #31CC17;
      }
    }
  }

  .holder-content-bg {
    height: 80px;
    margin-top: 12px;
    border-top: 1px dashed #ebe9e7;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
  }

  .holder-content {
    .holder-content-title {
      margin: -68px 32rpx 0;
      font-size: 28rpx;
      font-weight: bold;
      color: #000;
      letter-spacing: 2rpx;
    }

    .holder-content-my, .holder-content-sub-org {
      margin: 12px 32rpx 0;
      padding: 32rpx 0rpx 0;
      background-color: #fff;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
      border-radius: 8px;
      overflow: hidden;

      .holder-content-my-header {
        padding: 0 32rpx;
        margin-bottom: 12px;

        .holder-content-my-header-title {
          padding-right: 34rpx;
          background-image: url("https://oss-wukong-resource.oss-cn-shenzhen.aliyuncs.com/prod/mp/info-icon-black.png");
          background-position: right center;
          background-size: 24rpx 24rpx;
          background-repeat: no-repeat;
        }

        .loss-block,
        .add-block {
          //width: 62px;
          height: 22px;
          padding: 0 5px;
          background: rgba(244, 88, 67, 0.08);
          border-radius: 4px 4px 4px 4px;
          text-align: center;
          line-height: 22px;
          font-size: 12px;
          color: #F45843;
        }

        .add-block {
          background: rgba(49, 204, 23, 0.08);
          color: #31CC17;
        }
      }

      .holder-content-my-content {
        padding: 0 32rpx;
        margin-bottom: 16px;

        .holder-content-my-total {
          background: linear-gradient(4.**********579694e-7deg, #F9983E 0%, #FD5935 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-size: 40rpx;
          font-weight: bold;
        }

        .holder-content-block {
          width: 430rpx;
          padding: 8px 32rpx;
          justify-content: space-between;
          background: #FAFAFA;
          box-shadow: inset 1px 4px 8px 0px rgba(0, 0, 0, 0.04);
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #EBE9E7;
          box-sizing: border-box;

          .holder-content-block-item {
            display: flex;
            flex-direction: column;
            align-items: center;
          }

          .holder-content-block-title {
            margin-bottom: 4px;
            font-weight: bold;
            font-size: 28rpx;
            color: #6F6A67;
          }

          .holder-content-block-value {
            font-weight: bold;
            font-size: 28rpx;
            color: #2E2C2B;
          }
        }
      }

      .holder-content-my-footer {
        padding: 0 32rpx;
        line-height: 34px;
        background: linear-gradient(180deg, #F5F5F5 0%, #FFFFFF 100%);
        font-size: 26rpx;
        color: #6F6A67;
        letter-spacing: 2rpx;
      }
    }
  }
}


.running-box {
  .working-box,
  .stock-box,
  .abnormal-box {
    margin: 0 32rpx 12px;
    padding: 32rpx;
    background-color: #fff;
    border-radius: 12px;

    .working-title,
    .stock-title,
    .abnormal-title {
      color: #2e2c2b;
      font-size: 28rpx;
      font-weight: bold;
      letter-spacing: 2rpx;
      line-height: 40rpx;
    }
  }

  .working-box,
  .stock-box {
    margin-top: 27px;

    .working-header, .stock-header {
      margin-bottom: 12px;
      display: flex;

      .working-title, .stock-title {
        flex: 1;
      }

      .working-count, .stock-count {
        flex: 2;
      }
    }

    .working-content,
    .stock-content {
      .working-content-left,
      .stock-content-left {
        flex-shrink: 0;
        width: 252rpx;
        height: 68px;
        padding-left: 32rpx;
        border-radius: 4px;
        border: 1px solid #EBE9E7;
        box-sizing: border-box;
      }

      .working-content-right,
      .stock-content-right {
        justify-content: space-around;
        flex: 1;
        height: 60px;
        background: #FAFAFA;
        border-radius: 0px 4px 4px 0px;
        border: 1px solid #EBE9E7;
        border-left: none;

        .working-item,
        .stock-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .working-item-value,
          .stock-item-value {
            font-size: 14px;
            line-height: 20px;
            color: #2E2C2B;
            font-weight: bold;
          }

          .working-item-title,
          .stock-item-title {
            margin-top: 4px;
            font-size: 14px;
            line-height: 20px;
            color: #6f6a67;
          }
        }
      }
    }

    .working-tips,
    .stock-tips {
      margin-top: 12px;
      font-size: 26rpx;
      letter-spacing: 2rpx;
      line-height: 36rpx;
    }
  }

  .stock-chart,
  .working-chart {
    width: 40px;
    height: 40px;
    margin-right: 24rpx;
  }

  .chart-title,
  .chart-value {
    font-size: 28rpx;
    line-height: 32rpx;
    font-weight: bold;
  }

  .chart-title {
    margin-bottom: 8rpx;
    color: #2E2C2B;
  }

  .chart-value {
    color: #F9733E;
  }
}

.list-end {
  margin-top: 20rpx;
  font-size: 26rpx;
  letter-spacing: 2rpx;
  color: #999;
  text-align: center;
}