import {
	getAgentEmpHasPermiProfitChartData,
	getAgentEmpNoPermiProfitChartData,
	getAgentManagerHasPermiProfitChartData,
	getAgentManagerNoPermiProfitChartData,
	getAllyEmpHasPermiProfitChartData,
	getAllyEmpNoPermiProfitChartData,
	getAllyManagerHasPermiProfitChartData,
	getAllyManagerNoPermiProfitChartData,
} from "@/api/index.js";
import { profitArcChartData } from "@/utils/constant.js";
import { isManager, isAlly, isAgentType } from "@/utils/role.js";
import {
	getMonth,
	getToday,
	getMonsDay,
	getDate,
	getLastMonthFirstDay,
	getLastMonthEndDay,
} from "@/utils/function.js";
import { hasProfitOperationPermi, hasBdProfitPermi, } from "@/utils/permissionList.js";
import mTabbar from '@/components/m-tabbar/m-tabbar.vue';

export default {
	data() {
		return {
			profitChartData: {},
			operateChartData: {},
			orderCountChartData: {},
			dayData: {},
			monData: {},
			dayProfit: 0,
			monProfit: 0,

			expense: 0,
			agentWidth: 0,
			storekeeperWidth: 0,
			otherWidth: 0,
			otherExpense: 0,

			statisticTotal: {}, // 汇总数据
			timeType: 1, // 1 本月  2 本周  3 今日 4 全部 5 上月  6 昨日
			timeList: [
				{
					value: 4,
					text: "全部",
					string: "statisticTotal",
				},
				{
					value: 3,
					text: "今日",
					string: "statisticToday",
				},
				{
					value: 6,
					text: "昨日",
				},
				{
					value: 2,
					text: "本周",
					string: "statisticThisWeek",
				},
				{
					value: 1,
					text: "本月",
					string: "statisticThisMonth",
				},
				{
					value: 5,
					text: "上月",
				},
			],
			functionList: [],
			arcOpts1: {
				...JSON.parse(JSON.stringify(profitArcChartData)),
				extra: {
					arcbar: {
						type: "circle",
						width: 2.6,
						backgroundColor: "rgba(204,0,183,0.1)",
						startAngle: 1.5,
						endAngle: 0.25,
						gap: 2,
					},
				},
			},
			arcOpts2: {
				...JSON.parse(JSON.stringify(profitArcChartData)),
				extra: {
					arcbar: {
						type: "circle",
						width: 2.6,
						backgroundColor: "rgba(253,147,66,0.1)",
						startAngle: 1.5,
						endAngle: 0.25,
						gap: 2,
					},
				},
			},
			columnOpts: {
				color: ["#FFDABF", "#FF954A"],
				padding: [15, 0, 0, 0],
				legend: {
					show: true,
				},
				touchMoveLimit: 24,
				enableScroll: false,
				textOffset: 100,
				xAxis: {
					disableGrid: true,
					scrollShow: false,
					scrollAlign: "right",
					itemCount: 4,
				},
				yAxis: {
					gridType: "dash",
					disabled: true,
					dashLength: 3,
					gridColor: "rgba(27,27,27,0.12)",
				},
				extra: {
					column: {
						type: "meter",
						width: 20,
						activeBgColor: "#000000",
						activeBgOpacity: 0.08,
						meterBorder: 1,
						meterFillColor: "#FFDABF",
					},
				},
			},
			lineOpts: {
				color: ["#FFC998", "#F97A1B"],
				padding: [35, 10, 0, 15],
				legend: {
					show: true,
				},
				xAxis: {
					disableGrid: true,
					axisLine: {
						show: false,
					},
				},
				yAxis: {
					disabled: true,
					gridType: "dash",
					dashLength: 2,
					gridColor: "rgba(27,27,27,0.12)",
				},
				extra: {
					line: {
						type: "curve",
						width: 2,
						activeType: "hollow",
						linearType: "custom",
						onShadow: true,
						animation: "horizontal",
					},
				},
			},
			profitOpts: {
				color: [
					"#fd6915",
					"#fd6915",
					"#fd6915",
					"#fd6915",
					"#fd6915",
					"#fd6915",
					"#FC8452",
					"#9A60B4",
					"#ea7ccc",
				],
				title: {
					name: "",
					fontSize: 12,
					color: "#2D2D2D",
				},
				subtitle: {
					name: "收益率",
					fontSize: 10,
					color: "rgba(45,45,45,0.6)",
				},
				extra: {
					arcbar: {
						type: "circle",
						width: 8,
						backgroundColor: "#ffffff",
						startAngle: 1.5,
						endAngle: 0.25,
						gap: 2,
					},
				},
			},
		};
	},
	computed: {
		isManager,
		isAlly,
		isAgentType,
		isShowOperationProfit() {
			let { isAlly, statisticTotal, isAgentType } = this;
			return (
				(isAlly && statisticTotal.operationProfit) ||
				(isAgentType && statisticTotal.expenseChannelIntangible)
			);
		},
		hasProfitOperationPermi,
		hasBdProfitPermi,
	},
	components: {
		mTabbar
	},
	onLoad() {
		this.getData();
	},
	async onPullDownRefresh() {
		let res = await this.getData();
		uni.stopPullDownRefresh();
	},
	methods: {
		async getData() {
			let { isAgentType, isManager, timeType, hasProfitOperationPermi } = this;
			console.log(this.isManager, "1", hasProfitOperationPermi);
			let getProfitChartData;
			if (isAgentType) {
				getProfitChartData =
					isManager && hasProfitOperationPermi
						? getAgentManagerHasPermiProfitChartData
						: isManager && !hasProfitOperationPermi
						? getAgentManagerNoPermiProfitChartData
						: !isManager && hasProfitOperationPermi
						? getAgentEmpHasPermiProfitChartData
						: !isManager && !hasProfitOperationPermi
						? getAgentEmpNoPermiProfitChartData
						: "";
			} else {
				getProfitChartData =
					isManager && hasProfitOperationPermi
						? getAllyManagerHasPermiProfitChartData
						: isManager && !hasProfitOperationPermi
						? getAllyManagerNoPermiProfitChartData
						: !isManager && hasProfitOperationPermi
						? getAllyEmpHasPermiProfitChartData
						: !isManager && !hasProfitOperationPermi
						? getAllyEmpNoPermiProfitChartData
						: "";
			}

			let queryString = isManager
				? "findCurrentOrganization"
				: "findCurrentEmployee";
			let dayRevenueString;
			let monthRevenueString;
			if (isAgentType) {
				dayRevenueString = hasProfitOperationPermi
					? "agentRevenueManagerDayChart"
					: "agentRevenueEmpDayChart";
				monthRevenueString = hasProfitOperationPermi
					? "agentRevenueManagerMonthChart"
					: "agentRevenueEmpMonthChart";
			} else {
				dayRevenueString = hasProfitOperationPermi
					? "allyManagerRevenueDayChart"
					: "allyEmpRevenueDayChart";
				monthRevenueString = hasProfitOperationPermi
					? "allyManagerMonthChart"
					: "allyEmpMonthChart";
			}

			// monthRevenueString = isAgentType ? 'agentRevenueMonthChart' : hasProfitOperationPermi ? 'allyManagerMonthChart' : 'allyEmpMonthChart'
			this.$modal.loading("加载中");

			let dateStart =
				timeType === 1
					? getMonth()
					: timeType === 2
					? getMonsDay()
					: timeType === 3
					? getToday()
					: timeType === 4
					? ""
					: timeType === 5
					? getLastMonthFirstDay()
					: timeType === 6
					? getDate()
					: "";
			let dateEnd =
				timeType === 5 ? getLastMonthEndDay() : timeType === 6 ? getDate() : "";

			let res = await getProfitChartData({ dateStart, dateEnd });

			this.statisticTotal = res.data[queryString].statisticTotal;

			let dayData = res.data[queryString][dayRevenueString];
			this.dayProfit = dayData[dayData.length - 1].netRevenue
				? dayData[dayData.length - 1].netRevenue / 100
				: 0;
			let monData = res.data[queryString][monthRevenueString];
			this.monProfit = monData[monData.length - 1].netRevenue
				? monData[monData.length - 1].netRevenue / 100
				: 0;

			if (this.isAlly) {
				this.initProfitArcChart(this.statisticTotal);
				!this.isShowOperationProfit
					? this.initOrdercountArcChart(this.statisticTotal)
					: this.initOperateArcChart(this.statisticTotal);
			}
			if (this.isAgentType) {
				this.drawAgentChart();
			}
			this.formatDayChartData(dayData);
			this.formatMonthChartData(monData);
			this.$modal.closeLoading();
		},
		formatDayChartData(dayData) {
			let dayKey = [];
			let dayValue = [];
			let dayProfitValue = [];
			dayData.forEach((day) => {
				dayKey.push(day.time.slice(-2));
				dayValue.push(day.netRevenue ? parseInt(day.netRevenue / 100, 10) : 0);
				dayProfitValue.push(day.profit ? parseInt(day.profit / 100, 10) : 0);
				if (day.profit < -100) {
					this.columnOpts.yAxis.data = [
						{
							min: -10,
						},
					];
				}
			});
			this.initColumnChart(dayKey, dayValue, dayProfitValue);
		},
		formatMonthChartData(monData) {
			let monKey = [];
			let monValue = [];
			let monProfitValue = [];
			monData.forEach((mon) => {
				monKey.push(mon.time.slice(-5, -3));
				monValue.push(mon.netRevenue ? parseInt(mon.netRevenue / 100, 10) : 0);
				monProfitValue.push(mon.profit ? parseInt(mon.profit / 100) : 0);
			});

			this.initLineChart(monKey, monValue, monProfitValue);
		},
		initProfitArcChart(statisticTotal) {
			let data;
			if (!this.isAgentType) {
				data = statisticTotal.allyProfitRateEmp.toFixed(2);
			} else if (this.isAgentType) {
				data = statisticTotal.agentProfitRateEmp.toFixed(2);
			}

			let res1 = {
				series: [
					{
						color: "rgba(255,0,229,0.36)",
						data,
					},
				],
			};
			this.arcOpts1.title.name = parseInt(data * 100, 10) + "%";
			this.profitChartData = JSON.parse(JSON.stringify(res1));
		},
		initOrdercountArcChart(statisticTotal) {
			let data =
				statisticTotal.discountManager && statisticTotal.revenueManager
					? parseInt(
							(statisticTotal.discountManager / statisticTotal.revenueManager) *
								100,
							10
					  ) / 100
					: 0;

			let res = {
				series: [
					{
						color: "rgba(253,147,66,0.36)",
						data,
					},
				],
			};
			this.arcOpts2.title.name = parseInt(data * 100, 10) + "%";
			this.orderCountChartData = JSON.parse(JSON.stringify(res));
		},
		initOperateArcChart(statisticTotal) {
			let data;
			if (!this.isAgentType) {
				data =
					statisticTotal.operationProfit && statisticTotal.netRevenueManager
						? parseInt(
								(statisticTotal.operationProfit /
									statisticTotal.netRevenueManager) *
									100,
								10
						  ) / 100
						: 0;
			} else {
				data =
					statisticTotal.expenseChannelIntangible &&
					statisticTotal.netRevenueManager
						? parseInt(
								(statisticTotal.expenseChannelIntangible /
									statisticTotal.netRevenueManager) *
									100,
								10
						  ) / 100
						: 0;
			}
			let res2 = {
				series: [
					{
						color: "rgba(253,147,66,0.36)",
						data,
					},
				],
			};
			this.arcOpts2.title.name = parseInt(data * 100, 10) + "%";
			this.operateChartData = JSON.parse(JSON.stringify(res2));
		},
		initColumnChart(dayKey, dayValue, dayProfitValue) {
			let chartData = {
				categories: dayKey,
				series: [
					{
						name: "日实收",
						data: dayValue,
						textColor: "#fb6e15",
					},
				],
			};
			if (this.hasBdProfitPermi) {
				chartData.series.push({
					name: "日收益",
					data: dayProfitValue,
					textColor: "transparent",
				})
			}
			this.dayData = JSON.parse(JSON.stringify(chartData));
		},
		drawAgentChart() {
			let { isManager, statisticTotal, hasProfitOperationPermi } = this;
			let {
				storeKeeperProfit,
				expenseManager,
				expenseEmp,
				agentManagerProfit,
				agentEmpProfit,
				discountManager,
				discountEmp,
			} = statisticTotal;
			let expense = hasProfitOperationPermi ? expenseManager : expenseEmp;
			let expenseDiscount = hasProfitOperationPermi
				? discountManager
				: discountEmp;
			this.expense = expense - expenseDiscount;
			let agentProfit = hasProfitOperationPermi
				? agentManagerProfit
				: agentEmpProfit;
			this.otherExpense = this.expense - storeKeeperProfit - agentProfit;

			// 画进度条
			if (!this.otherExpense && !storeKeeperProfit && !agentProfit) {
				this.storekeeperWidth = 628 * 0.33;
				this.agentWidth = 628 * 0.33 + this.storekeeperWidth;
				this.otherWidth = 628 * 0.34 + this.agentWidth;
			} else {
				this.storekeeperWidth = storeKeeperProfit
					? Math.max((storeKeeperProfit / this.expense).toFixed(2), 0.04) * 628
					: 0;
				this.agentWidth = agentProfit
					? Math.max((agentProfit / this.expense).toFixed(2), 0.05) * 628 +
					  this.storekeeperWidth
					: this.storekeeperWidth;
				this.otherWidth = this.otherExpense
					? Math.max((this.otherExpense / this.expense).toFixed(2), 0.05) *
							628 +
					  this.agentWidth
					: 0;
			}
			// 画圆环
			let data;
			let rate;
			let {
				netRevenueEmp,
				netRevenueManager,
				agentProfitRateEmp,
				agentProfitRateManager,
			} = statisticTotal;
			if (!this.hasProfitOperationPermi) {
				rate = (agentProfitRateEmp * 100).toFixed(1);
				rate = rate > 0 ? rate : parseInt(rate, 10);
				agentProfitRateEmp =
					agentProfitRateEmp > 0 ? agentProfitRateEmp : -agentProfitRateEmp;
				data = Math.min((agentProfitRateEmp * 100).toFixed(1), 100) / 100;
			} else {
				rate = (agentProfitRateManager * 100).toFixed(1);
				rate = rate > 0 ? rate : parseInt(rate, 10);
				agentProfitRateManager =
					agentProfitRateManager > 0
						? agentProfitRateManager
						: -agentProfitRateManager;
				data = Math.min((agentProfitRateManager * 100).toFixed(1), 100) / 100;
			}
			let res = {
				series: [
					{
						color: "#fd6915",
						data,
					},
				],
			};
			this.profitOpts.title.name = rate + "%";
			this.profitChartData = JSON.parse(JSON.stringify(res));
		},
		initLineChart(monKey, monValue, monProfitValue) {
			let chartData = {
				categories: monKey,
				series: [
					{
						name: "月实收",
						data: monValue,
						textOffset: -20,
					},
				],
				yAxis: {
					disabled: true,
					gridType: "dash",
				},
			};
			if (this.hasBdProfitPermi) {
				chartData.series.push({
					name: "月收益",
					data: monProfitValue,
					textOffset: 0,
				})
			}
			this.monData = JSON.parse(JSON.stringify(chartData));
		},
		toExplain() {
			uni.navigateTo({
				url: `/subpkg/pages/explain-page/explain-page?type=profitData`,
			});
		},
		toProfitAnalyze() {
			if (this.isManager && this.hasProfitOperationPermi) {
				uni.navigateTo({
					url: `/subpkgProfit/pages/profit-analyze-list/profit-analyze-list`,
				});
			}
		},
		toRankList() {
			uni.navigateTo({
				url: `/subpkgProfit/pages/rank-list/rank-list`,
			});
		},
		toBill() {
			uni.navigateTo({
				url: `/subpkg/pages/profit-bill-all/profit-bill-all`,
			});
		},
		toPage(item) {
			uni.navigateTo({
				url: item.url,
			});
		},
		async changeTime() {
			let res = await this.getData();
		},
	},
};
