<template>
	<view class="operate-index-contain">
		<view class="chart-box box">
			<view class="chart-box-tab">
				<view class="tab-item" :class="{'tab-item-active': tab == 1}" @click="changeTab(1)">日收益</view>
				<view class="tab-item" :class="{'tab-item-active': tab == 2}" @click="changeTab(2)">周收益</view>
				<view class="tab-item" :class="{'tab-item-active': tab == 3}" @click="changeTab(3)">月收益</view>
			</view>
			<view class="chart-box-content">
				
			</view>
		</view>
		<view class="appreciation-box box">
			<view class="appreciation-header">
				<view class="appreciation-header-left">
					<view class="">智能运营增值比例(%)：</view>
					<input type="number" @blur="saveRatio" class="input-element" v-model="ratio" />
				</view>
				<view class="appreciation-header-right">
					<switch class="switch-element" checked />
				</view>
			</view>
			<view class="appreciation-content">
				<view class="appreciation-content-title">增值运营配置</view>
				<radio-group class="" @change="changeRadio($event,)">
					<label class="config-radio flex-row" v-for="(item, key) in configList" :key="item.id">
						<view class="">
							<radio :value="item.id" :checked="item.id == configType" />
						</view>
						<view class="unit-time-text">
							<text class="config-name">{{ item.name }}</text>
							<text class="config-tips">{{item.text}}</text>
						</view>
					</label>
				</radio-group>
			</view>
		</view>
		<view class="function-box box">
			<view class="function-item">
				<text class="function-item-icon"></text>
				<text class="function-item-value">商户列表</text>
			</view>
			<view class="function-item">
				<text class="function-item-icon"></text>
				<text class="function-item-value">增值列表</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { getIndexInfo, saveAdjustRatio } from '@/api/subpkgOperate/index.js';

	export default {
		data() {
			return {
				tab: 1,
				ratio: '',
				configType: 1,
				configList: [{
					id: 1,
					name: '高',
					icon: '',
					text: '安全度最高，不易被商户投诉，增值比例限30%以下',
				}, {
					id: 1,
					name: '中',
					icon: '',
					text: '均衡增值成功率与投诉风险，增值比例限20%-50%',
				},{
					id: 1,
					name: '低',
					icon: '',
					text: '优先保证增值率，增值比例限30%-70%',
				},{
					id: 1,
					name: '自定义',
					icon: '',
					text: '自定义增值规则',
				},]
			};
		},
		computed: {
			orgId() {
        return this.$store.state.user.orgId;
      }
		},
		async onLoad() {
			let res = await getIndexInfo();
			this.ratio = res.data.findCurrentOrganization.adjust || '';
		},
		methods: {
			changeTab(tab) {
				this.tab = tab;
			},
			changeRadio() {
				
			},
			async saveRatio() {
				let data = { userId: this.orgId, ratio: this.ratio }
				let res = await saveAdjustRatio(data);
				if (res.code == 200) {
					this.$modal.msgSuccess("修改成功")
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.operate-index-contain {
	padding-top: 20px;
}
.box {
	margin: 0 30rpx 30rpx;
	background-color: #fff;
	border-radius: 20rpx;
}
.chart-box {
	.chart-box-tab {
		display: flex;
		align-items: center;
		padding: 20rpx 0 0;
		.tab-item {
			flex: 1;
			text-align: center;
			color: #777;
			font-size: 32rpx;
			line-height: 70rpx;
		}
		.tab-item-active {
			position: relative;
			color: rgb(9, 154, 244);
		}
		.tab-item-active::before {
			content: "";
			position: absolute;
			left: 50%;
			bottom: 0;
			transform: translateX(-50%);
			display: block;
			width: 80rpx;
			height: 6rpx;
			background-color: rgb(9, 154, 244);
		}
	}
}

.appreciation-box {
	.appreciation-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16rpx 20rpx;
		border-bottom: 1px solid #eee;
		color: #555;
		.appreciation-header-left {
			display: flex;
			align-items: center;
			.input-element {
				width: 140rpx;
				border: 1px solid #eee;
				border-radius: 8rpx;
			}
		}
	}

	.appreciation-content {
		padding: 30rpx 20rpx;
		.appreciation-content-title {
			margin-bottom: 20rpx;
			font-size: 32rpx;
			color: #333;
		}
		.config-radio {
			margin-bottom: 18rpx;
			.config-name {
				margin-right: 20rpx;
				font-size: 30rpx;
				color: #333;
			}
			.config-tips {
				font-size: 22rpx;
				color: #999;
			}
		}
	}
}

.function-box {
	.function-item {
		padding: 20rpx 30rpx;
		border-bottom: 1px solid #eee;
		&:nth-last-child(1) {
			border-bottom: none;
		}
	}
}
</style>
